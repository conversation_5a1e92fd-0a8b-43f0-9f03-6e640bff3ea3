package com.cpvsn.rm.core.features.misc.company.es

import co.elastic.clients.elasticsearch._types.query_dsl.Query
import co.elastic.clients.elasticsearch._types.query_dsl.QueryBuilders
import co.elastic.clients.elasticsearch.core.SearchResponse
import com.cpvsn.crud.model.Includes
import com.cpvsn.crud.pagination.PageRequest
import com.cpvsn.crud.pagination.Sort
import com.cpvsn.crud.util.ids
import com.cpvsn.rm.core.config.CpvsnConfig
import com.cpvsn.rm.core.features.misc.company.Company
import com.cpvsn.rm.core.features.search.ElasticSearchHelper
import com.cpvsn.rm.core.util.biz_error
import com.cpvsn.rm.core.util.global_scheduled_executor
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.util.concurrent.TimeUnit

@Service
class EsCompanyService {

    private val log = LoggerFactory.getLogger(this.javaClass)

    @Autowired
    private lateinit var elasticSearchHelper: ElasticSearchHelper


    @Autowired
    private lateinit var companyEsIndex: CpvsnConfig.IndexItem

    fun buildQuery(
        input: CompanyNameSearchInput
    ): Query {
        val field_list = listOf(
            CompanyDoc::name.name,
            CompanyDoc::chinese_name.name,
            CompanyDoc::english_name.name,
            CompanyDoc::alias.name,
            CompanyDoc::stock_code.name,
        )
        val keyword = input.keyword?.trim() ?: biz_error("The keyword must not be empty")
        return if (keyword.startsWith("\"") && keyword.endsWith("\"")) {
            QueryBuilders.bool { b ->
                b.should(
                    field_list.map { f ->
                        QueryBuilders.queryString { qs ->
                            qs.fields("${f}.keyword").query(keyword)
                        }
                    }
                ).minimumShouldMatch("1")
            }
        } else {
            // replaces special characters with spaces and split the keyword string by spaces
            val splited_input = input.keyword?.trim()
                ?.replace("\\W+".toRegex(), " ")
                ?.split("\\s+".toRegex())
                ?: biz_error("The keyword must not be empty")
            return QueryBuilders.bool { boolQuery ->
                boolQuery.must(
                    splited_input.map { cur_keyword ->
                        QueryBuilders.queryString {
                            it.query("*${cur_keyword}*").fields(field_list)
                        }
                    }
                ).should(
                    field_list.map { field ->
                        QueryBuilders.match {
                            it.query(input.keyword).field(field)
                        }
                    }
                )
            }
        }

    }

    private fun company_docs(
        query: Company.Query,
        pageRequest: PageRequest? = null,
    ): Set<CompanyDoc> {
        val include = Includes.setOf(
            Company::aliases
        )
        val companies = if (pageRequest == null) {
            Company.findAll(query, include)
        } else {
            Company.list(query, pageRequest, include).items
        }
        val docs = companies.mapNotNull { company ->
            try {
                val doc = CompanyDoc.from(company)
                doc
            } catch (e: Throwable) {
                log.info("Sync doc(id=${company.id}) failed: ${e.message}")
                null
            }
        }
        return docs.toSet()
    }

    fun company_search(input: CompanyNameSearchInput): SearchResponse<CompanyDoc> {
        if (input.keyword.isNullOrBlank()) {
            biz_error("The keyword must not be empty")
        }
        val res = elasticSearchHelper.client.search(
            { search ->
                val size = input.size ?: 10
                val from = ((input.page ?: 1) - 1) * size
                search.index(companyEsIndex.index_name)
                    .query(buildQuery(input))
                    .size(size)
                    .from(from)
                    .trackTotalHits {
                        it.enabled(true)
                    }
            },
            CompanyDoc::class.java
        )
        return res
    }

    fun company_delete(
        index: String? = null,
        query: Company.Query,
    ): String {
        val idx = index ?: companyEsIndex.index_name
        val deleted_ids = Company.findAll(
            query = query
        ).ids()
        val deleted_count = elasticSearchHelper.clear_by_ids(
            idx,
            deleted_ids
        )
        val msg = "deleted count: $deleted_count"
        log.info(msg)
        return msg
    }

    fun company_delete_by_ids(
        index: String? = null,
        ids: Set<Int>,
    ): String {
        val idx = index ?: companyEsIndex.index_name
        val deleted_count = elasticSearchHelper.clear_by_ids(
            idx,
            ids
        )
        val msg = "deleted count: $deleted_count"
        log.info(msg)
        return msg
    }

    fun company_bulk(
        company_list: List<Company>,
        index: String? = null,
    ): String {
        val idx = index ?: companyEsIndex.index_name
        val step_size = 50000
        val chunked_company_list = company_list.chunked(step_size)
        val steps = chunked_company_list.size
        (0 until steps).forEach { cur_step ->
            global_scheduled_executor.schedule(
                {
                    val docs = chunked_company_list[cur_step]
                        .mapNotNull { company ->
                            try {
                                val doc = CompanyDoc.from(company)
                                doc
                            } catch (e: Throwable) {
                                log.info("Sync doc(id=${company.id}) failed: ${e.message}")
                                null
                            }
                        }
                    if (docs.isNotEmpty()) {
                        log.info("bulk companies, step: $cur_step, size: ${docs.size}")
                        elasticSearchHelper.bulk(index = idx, docs)
                    }
                },
                (cur_step * 20).toLong(),
                TimeUnit.SECONDS
            )
        }
        val msg = "bulking count: ${company_list.size}"
        log.info(msg)
        return msg
    }

    fun company_bulk(
        query: Company.Query,
        index: String? = null,
    ): String {
        val idx = index ?: companyEsIndex.index_name
        val stepSize = 50000
        val count = Company.count(query)
        val pages = count / stepSize + 1
        (1..pages).forEach { i ->
            global_scheduled_executor.schedule(
                {
                    val docs = company_docs(
                        query = query,
                        pageRequest = PageRequest(
                            page = i,
                            size = stepSize,
                            sort = Sort.by(Sort.Order("id", Sort.Direction.ASC))
                        )
                    )
                    if (docs.isNotEmpty()) {
                        log.info("bulk companies by query, page: $i, size: $stepSize, id range:${docs.minOf { it.id }}~${docs.maxOf { it.id }}")
                        elasticSearchHelper.bulk(index = idx, docs)
                    }
                },
                ((i - 1) * 20).toLong(),
                TimeUnit.SECONDS
            )
        }

        val msg = "bulking count: $count"

        log.info(msg)
        return msg
    }
}
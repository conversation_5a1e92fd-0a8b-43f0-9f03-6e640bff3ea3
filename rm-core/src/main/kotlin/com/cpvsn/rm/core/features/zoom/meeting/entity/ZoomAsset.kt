package com.cpvsn.rm.core.features.zoom.meeting.entity

import com.cpvsn.core.base.entity.BaseQuery
import com.cpvsn.crud.orm.annotation.Column
import com.cpvsn.crud.orm.annotation.Relation
import com.cpvsn.crud.orm.entity.SoftDeletable
import com.cpvsn.crud.query.Criteria
import com.cpvsn.rm.core.base.entity.RmCompanion
import com.cpvsn.rm.core.base.entity.RmEntity
import java.time.Instant

class ZoomAsset : RmEntity(), SoftDeletable, Asset {
    companion object : RmCompanion<ZoomAsset>()

    @Column
    var meeting_uuid: String = ""

    @Column
    var meeting_id: Long = 0

    @Column
    var start_time: String = ""

    @Column
    var timezone: String? = null

    @Column
    var share_url: String? = null

    @Column
    var asset_id: String = ""

    @Column
    var file_type: FileType = FileType.M4A

    @Column
    var play_url: String = ""

    @Column
    var download_url: String = ""

    @Column
    var recording_type: String = ""

    @Column
    var password: String? = null

    @Column
    var recording_play_passcode: String? = null

    @Column
    var download_token: String? = null

    @Column
    override var capvision_file_url: String = ""

    @Column
    var file_size: Long? = null

    @Column
    var recording_start: String = ""

    @Column
    var recording_end: String = ""

    @Column(updatable = false, insertable = false)
    override var delete_at: Instant? = null

    @Relation(reference = "meeting_id", backReference = "meeting_id")
    var zoom_meeting_instance: List<ZoomMeetingInstance>? = null

    @Relation(reference = "meeting_id", backReference = "meeting_id")
    var zoom_meeting: ZoomMeeting? = null

    enum class FileType {
        MP4,
        M4A,
        TRANSCRIPT,
        TIMELINE,
        CHAT,
        CSV,
        CHAT_MESSAGE,
        CC,
        TB,
        SUMMARY
    }

    data class Query(
        @Criteria.Eq
        val id: Int? = null,
        @Criteria.IdsIn
        val ids: Set<Int>? = null,
        @Criteria.Gte
        val id_gte: Int? = null,
        @Criteria.Lte
        val id_lte: Int? = null,

        @Criteria.Eq
        val meeting_id: Long? = null,
        @Criteria.IdsIn
        val meeting_ids: Set<Long>? = null,

        @Criteria.Eq
        val meeting_uuid: String? = null,
        @Criteria.In
        val meeting_uuid_in: Set<String>? = null,

        @Criteria.Eq
        val asset_id: String? = null,
        @Criteria.In
        val asset_id_in: Set<String>? = null,

        @Criteria.Eq
        val file_type: FileType? = null,
        @Criteria.Eq
        val recording_type: String? = null,

        @Criteria.Gte
        val create_at_gte: Instant? = null,

        @Criteria.Join
        val zoom_meeting: ZoomMeeting.Query? = null,

        override val includeSoftDeletedRecords: Boolean = false
    ) : BaseQuery<ZoomAsset>()
}
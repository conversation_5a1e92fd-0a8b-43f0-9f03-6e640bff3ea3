package com.cpvsn.rm.core.features.task.sq

import com.cpvsn.core.svc.spring.ext.extExecute
import com.cpvsn.crud.model.Includes
import com.cpvsn.rm.core.features.advisor.job.AdvisorJobService
import com.cpvsn.rm.core.features.advisor.tag.psq.AdvisorTagMapProjectSqService
import com.cpvsn.rm.core.features.inquiry.InquiryInstanceService
import com.cpvsn.rm.core.features.inquiry.model.InquiryInstance
import com.cpvsn.rm.core.features.inquiry.model.InquiryQuestion
import com.cpvsn.rm.core.features.outsource.OutsourceService
import com.cpvsn.rm.core.features.portal.advisor.PortalAdvisor
import com.cpvsn.rm.core.features.task.Task
import com.cpvsn.rm.core.features.task.advisor_consent.TaskAdvisorRecordConsent
import com.cpvsn.rm.core.features.task.advisor_consent.TaskAdvisorRecordConsentService
import com.cpvsn.rm.core.features.task.constant.TaskOutsourceType
import com.cpvsn.rm.core.features.task.event.TaskEvent
import com.cpvsn.rm.core.features.task.event.TaskEventService
import com.cpvsn.rm.core.features.task.event.TaskEventType
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.transaction.support.TransactionTemplate

@Service
class TaskSqService {

    @Autowired
    private lateinit var taskEventService: TaskEventService

    @Autowired
    private lateinit var inquiryInstanceService: InquiryInstanceService

    @Autowired
    private lateinit var transactionTemplate: TransactionTemplate

    @Autowired
    private lateinit var advisorTagMapProjectSqService: AdvisorTagMapProjectSqService

    @Autowired
    private lateinit var outsourceService: OutsourceService

    @Autowired
    private lateinit var advisorJobService: AdvisorJobService

    @Autowired
    private lateinit var taskAdvisorRecordConsentService: TaskAdvisorRecordConsentService

    fun sq_respond(
        task_id: Int,
        sq_instance_id: Int,
        qa_list: List<InquiryQuestion>,
        portal: PortalAdvisor? = null,
        client_publish_question_ids: List<Int>? = null
    ): InquiryInstance {
        val res = transactionTemplate.extExecute {
            val task = Task.find(task_id, include = Includes.setOf(Task::task_outsource_info))?.let {
                taskEventService.trigger_event(TaskEvent {
                    this.task_id = task_id
                    this.type = TaskEventType.SQ_RESPOND
                    this.payload_id = sq_instance_id
                })
                it
            }
            val instance = inquiryInstanceService.answer(
                sq_instance_id,
                qa_list,
                passed_client_publish_question_ids = client_publish_question_ids
            )
            process_recording_consent(instance.qa_list_snapshot, task_id, task?.advisor_id)
            advisorJobService.save_advisor_npi_number_from_sq(instance.receiver_id, instance.qa_list_snapshot, portal)
            if (task?.task_outsource_status == TaskOutsourceType.EXPORTED) {
                outsourceService.update_task_qa_request(task_id)
            }
            instance
        }

        advisorTagMapProjectSqService
            .handle_respond_sq_async(sq_instance_id)

        return res
    }

    fun process_recording_consent(
        qa_list: List<InquiryQuestion>,
        task_id: Int,
        advisor_id: Int?,
    ) {
        if (advisor_id == null) return

        val consent_question =
            qa_list.firstOrNull { it.tags.contains(InquiryQuestion.Tag.CALL_RECORDING_CONSENT) } ?: return
        val result = consent_question.answer?.content?.get("result") as? Int
        val is_consent = when (result) {
            0 -> true
            1 -> false
            else -> null
        }
        val conset = TaskAdvisorRecordConsent {
            this.task_id = task_id
            this.advisor_id = advisor_id
            this.is_consent = is_consent
        }

        taskAdvisorRecordConsentService.update_consent(conset)
    }

}

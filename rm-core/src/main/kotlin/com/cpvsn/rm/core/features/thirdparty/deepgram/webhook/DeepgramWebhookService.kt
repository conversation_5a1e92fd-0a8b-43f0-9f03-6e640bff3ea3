package com.cpvsn.rm.core.features.thirdparty.deepgram.webhook

import com.cpvsn.crud.model.Patch
import com.cpvsn.rm.core.config.CpvsnConfig
import com.cpvsn.rm.core.core.storage.StorageService
import com.cpvsn.rm.core.extensions.getBean
import com.cpvsn.rm.core.features.thirdparty.deepgram.DeepgramRestApiService
import com.cpvsn.rm.core.features.thirdparty.deepgram.response.DeepgramResponse
import com.cpvsn.rm.core.features.twiliosdk.config.TwilioProperties
import com.cpvsn.rm.core.features.twiliosdk.entity.TwilioAsset
import com.cpvsn.rm.core.features.twiliosdk.enums.TwilioAddOnEnum
import com.cpvsn.rm.core.features.zoom.meeting.ZoomAppsConfig
import com.cpvsn.rm.core.features.zoom.meeting.ZoomProperties
import com.cpvsn.rm.core.features.zoom.meeting.ZoomWebhookService
import com.cpvsn.rm.core.features.zoom.meeting.entity.Asset
import com.cpvsn.rm.core.features.zoom.meeting.entity.ZoomAsset
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.io.ByteArrayInputStream

@Service
class DeepgramWebhookService {
    private val logger = LoggerFactory.getLogger(this::class.java)
    companion object {
        val HOST: String
            get() {
                return getBean<CpvsnConfig>().mainConfig().db_api_endpoint.orEmpty() + "/webhooks/deepgram"
            }
        const val TWILIO_ENDPOINT = "/transcription/twilio"
        const val ZOOM_ENDPOINT = "/transcription/zoom"
    }

    @Autowired
    private lateinit var deepgramRestApiService: DeepgramRestApiService

    @Autowired
    private lateinit var storageService: StorageService

    @Autowired
    private lateinit var twilioProperties: TwilioProperties

    @Autowired
    private lateinit var zoomProperties: ZoomProperties

    @Autowired
    private lateinit var zoomWebhookService: ZoomWebhookService

    @Autowired
    private lateinit var zoomAppsConfig: ZoomAppsConfig

    fun transcription_callback_twilio(
        callback: DeepgramResponse,
    ) {
        val file_name = "deepgram" + callback.metadata.request_id + ".txt"
        val asset = TwilioAsset.firstOrNull(
            TwilioAsset.Query(
                add_on_result_sid = callback.metadata.request_id,
                type = TwilioAsset.Type.VOICE_CONFERENCE_TRANSCRIPTION,
                add_on = TwilioAddOnEnum.deepgram
            )
        )
        val content = callback.results.channels.first().alternatives?.first()?.paragraphs?.transcript ?: ""
        val url = storageService.save(
            inputStream = ByteArrayInputStream(content.toByteArray()),
            path = "${twilioProperties.py_uploader_conference_asset_files_directory}/$file_name"
        )
        asset?.let {
            Patch.fromMutator(it) { capvision_file_url = url }.patch()
        }
    }

    fun transcription_callback_zoom(
        callback: DeepgramResponse,
    ) {
        val file_name = "deepgram" + callback.metadata.request_id + ".txt"
        val asset = ZoomAsset.firstOrNull(
            ZoomAsset.Query(
                asset_id = callback.metadata.request_id,
                file_type = ZoomAsset.FileType.TRANSCRIPT,
                recording_type = DeepgramRestApiService.SERVICE_NAME
            )
        )
        val content = callback.results.channels.first().alternatives?.first()?.paragraphs?.transcript ?: ""
        val url = storageService.save(
            inputStream = ByteArrayInputStream(content.toByteArray()),
            path = "${zoomProperties.py_uploader_zoom_meeting_asset_files_directory}/$file_name"
        )
        asset?.let {
            Patch.fromMutator(it) {
                capvision_file_url = url
                file_size = callback.metadata.duration.toLong()
            }.patch()

            // Upload transcript to client drive
            zoomWebhookService.upload_asset_to_client_drive(listOf(it), true)
        }
    }

    fun force_transcript(
        recording_id: Int,
        type: String,
    ): Asset? {
        if (type == "twilio") {
            val asset = TwilioAsset.get(recording_id)
            return deepgramRestApiService.request_for_twilio(asset)
        }
        if (type == "zoom") {
            val asset = ZoomAsset.get(recording_id)
            return deepgramRestApiService.request_for_zoom(asset)
        }
        return null
    }

}
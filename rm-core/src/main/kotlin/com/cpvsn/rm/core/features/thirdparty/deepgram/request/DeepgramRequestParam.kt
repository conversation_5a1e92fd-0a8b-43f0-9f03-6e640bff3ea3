package com.cpvsn.rm.core.features.thirdparty.deepgram.request

data class DeepgramRequestParam(
    val model: String = DeepgramModel.WHISPER_LARGE.value,
    val smart_format: Boolean = true,
    val language: String = DeepgramLanguage.ENGLISH.value,
    val diarize: Boolean = true,
    val punctuate: Boolean = true,
    val summarize: String = Summarize.V2.value,
    val topics: Boolean = true,
    val paragraphs: <PERSON><PERSON><PERSON> = true,
    val callback: String,
)


enum class DeepgramLanguage(val value: String) {
    ENGLISH("en"),
    CHINESE("cn"),
}

enum class DeepgramModel(val value: String) {
    DEFAULT("nova-2"),
    NOVA2("nova-2"),
    BASE("base"),
    BASE_MEETING("base-meeting"),
    WHISPER_MEDIUM("whisper-medium"),
    WHISPER_LARGE("whisper-large"),
}

enum class Summarize(val value: String) {
    V2("v2")
}
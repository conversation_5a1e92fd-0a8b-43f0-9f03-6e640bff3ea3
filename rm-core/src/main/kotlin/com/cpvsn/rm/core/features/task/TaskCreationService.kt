package com.cpvsn.rm.core.features.task

import com.cpvsn.core.svc.spring.ext.extExecute
import com.cpvsn.core.util.extension.assert_valid_id
import com.cpvsn.core.util.extension.biz_error
import com.cpvsn.core.util.extension.biz_require_not_null
import com.cpvsn.core.util.extension.is_valid_id
import com.cpvsn.crud.model.Includes
import com.cpvsn.crud.model.Patch
import com.cpvsn.crud.orm.cascade.Cascade
import com.cpvsn.crud.util.ids
import com.cpvsn.rm.core.annotation.PerformanceMonitor
import com.cpvsn.rm.core.features.advisor.job.AdvisorJob
import com.cpvsn.rm.core.features.bridge.Region
import com.cpvsn.rm.core.features.client.blacklist.ClientBlacklistService
import com.cpvsn.rm.core.features.client.contact.ClientContact
import com.cpvsn.rm.core.features.inquiry.InquiryInstanceRepo
import com.cpvsn.rm.core.features.inquiry.model.Inquiry
import com.cpvsn.rm.core.features.inquiry.model.InquiryInstance
import com.cpvsn.rm.core.features.outsource.pojo.TaskOutsourceInfo
import com.cpvsn.rm.core.features.project.Project
import com.cpvsn.rm.core.features.project.ProjectMember
import com.cpvsn.rm.core.features.task.advisor_profile.TaskAdvisorProfile
import com.cpvsn.rm.core.features.task.angle.TaskAngle
import com.cpvsn.rm.core.features.task.angle.TaskAngleService
import com.cpvsn.rm.core.features.task.constant.TaskCreationSource
import com.cpvsn.rm.core.features.task.constant.TaskStatus
import com.cpvsn.rm.core.features.task.constant.TaskType
import com.cpvsn.rm.core.features.task.constant.TaskWillingnessType
import com.cpvsn.rm.core.features.task.lead_group.LeadGroup
import com.cpvsn.rm.core.features.task.lead_group.LeadGroupService
import com.cpvsn.rm.core.features.task.pojo.FindTasksWithDuplicateAdvisorResponse
import com.cpvsn.rm.core.features.task.pojo.TaskCloneRequest
import com.cpvsn.rm.core.features.task.pojo.TaskCreateRequest
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.transaction.support.TransactionTemplate
import java.time.Instant

@Service
class TaskCreationService {

    //region @
    private val logger = LoggerFactory.getLogger(this::class.java)

    @Autowired
    private lateinit var mapper: Task.MybatisMapper

    @Autowired
    private lateinit var taskDisplayIdService: TaskDisplayIdService

    @Autowired
    private lateinit var taskAdvisorProfileRepo: TaskAdvisorProfile.Repo

    @Autowired
    private lateinit var transactionTemplate: TransactionTemplate

    @Autowired
    private lateinit var taskRateService: TaskRateService

    @Autowired
    private lateinit var taskAngleService: TaskAngleService

    @Autowired
    private lateinit var clientBlacklistService: ClientBlacklistService

    @Autowired
    private lateinit var inquiryInstanceRepo: InquiryInstanceRepo

    @Autowired
    private lateinit var leadGroupService: LeadGroupService

    // for declarative transaction management
    // do not remove this property unless you are fully aware of its effects
    @Autowired
    private lateinit var self: TaskCreationService
    //endregion

    /**
     * Copies leads from one project to another. Ensures that leads are added to groups if the target project has lead groups enabled. Creates lead groups as necessary to keep lead group structure the same between the old and new projects.
     */
    fun clone(request: TaskCloneRequest, user_id: Int): List<Task> {
        val tasks = if (request.clone_all_leads_from_project_id != null) {
            Task.findAll(
                Task.Query(
                    project_id = request.clone_all_leads_from_project_id,
                    angle_id_is_null = true,
                ), include = Includes.setOf(Task::advisor_profile)
            )
        } else {
            Task.findAll(
                Task.Query(
                    ids = request.task_ids.orEmpty()
                ), include = Includes.setOf(Task::advisor_profile)
            )
        }
        val to_project = Project.find(request.to_project_id, include = setOf(Project::lead_groups.name))

        // if the new project has lead groups enabled, ensure leads end up in groups so they diplay properly
        if (to_project?.enable_lead_group == true) {
            val from_project = request.clone_all_leads_from_project_id?.let {
                Project.find(
                    it, include = setOf(Project::lead_groups.name)
                )
            }

            // if the old project had lead groups enabled, copy those groups and put the leads in them
            if (from_project?.enable_lead_group == true) {
                val lead_groups_to_clone = from_project.lead_groups?.toSet() ?: emptySet()
                val lead_group_map = lead_groups_to_clone.associate { lead_group ->
                    val new_lead_group =
                        if (lead_group.name !in (to_project.lead_groups?.map { it.name } ?: emptyList())) {
                            LeadGroup.save(LeadGroup() {
                                project_id = to_project.id
                                name = lead_group.name
                            })

                        } else to_project.lead_groups!!.first { it.name == lead_group.name }
                    val new_lead_group_with_tasks = new_lead_group.apply {
                        this.tasks = tasks.filter { it.lead_group_id == lead_group.id }
                    }
                    Pair(lead_group.name, new_lead_group_with_tasks)
                }

                return lead_group_map.flatMap {
                    clone(
                        to_project_id = request.to_project_id,
                        to_angle_id = request.to_angle_id,
                        tasks = it.value.tasks ?: emptyList(),
                        user_id = user_id,
                        to_lead_group_id = it.value.id
                    )
                }
            }

            // if the old project did not have lead groups enabled, copy those groups to a single lead group
            val new_lead_group_name = request.to_lead_group_id?.let { LeadGroup.get(it).name } ?: from_project?.name
            ?: LeadGroupService.DEFAULT_LEAD_GROUP_NAME
            val new_lead_group = if (new_lead_group_name in (to_project.lead_groups?.map { it.name }
                    ?: emptyList())) to_project.lead_groups!!.first { it.name == new_lead_group_name }
            else LeadGroup.save(LeadGroup() {
                project_id = to_project.id
                name = new_lead_group_name
            })
            return clone(
                to_project_id = request.to_project_id,
                to_angle_id = request.to_angle_id,
                tasks = tasks,
                user_id = user_id,
                to_lead_group_id = new_lead_group.id
            )

        }

        // if the new project does not have lead groups enabled, set lead_group_id to null for each lead
        return clone(
            to_project_id = request.to_project_id,
            to_angle_id = request.to_angle_id,
            tasks = tasks,
            user_id = user_id,
            to_lead_group_id = null
        )

    }

    /**
     * Note that: if Task::advisor_profile is not null, we will clone it as well.
     */
    fun clone(
        to_project_id: Int,
        to_angle_id: Int?,
        tasks: List<Task>,
        user_id: Int,
        to_lead_group_id: Int? = null,
        use_passed_raw_lead_group_id: Boolean = false,
    ): List<Task> {
        if (tasks.isEmpty()) return emptyList()

        val entities = tasks.map { raw ->
            Task {
                this.clone_from_id = raw.id
                this.angle_id = to_angle_id
                this.lead_group_id =
                    to_lead_group_id ?: let { if (use_passed_raw_lead_group_id) raw.lead_group_id else null }
                this.advisor_id = raw.advisor_id
                this.willingness = raw.willingness
                this.advisor_profile = raw.advisor_profile?.let { raw_profile ->
                    TaskAdvisorProfile {
                        advisor_id = raw_profile.advisor_id
                        job_id = raw_profile.job_id
                        background = raw_profile.background
                        company_id = raw_profile.company_id
                        position = raw_profile.position
                        is_current = raw_profile.is_current
                        start_date = raw_profile.start_date
                        end_date = raw_profile.end_date
                    }
                }
            }
        }
        val requests = entities.map {
            TaskCreateRequest(it)
        }
        return self.create_batch(to_project_id, requests, user_id)
    }

    /**
     * Note that this will cascade save
     * Task::advisor_profile it it exists,
     * if it doesn't exist, see TaskCreateRequest::advisor_job_id to find out how do we create advisor profile
     * Will also cascade save Task::latest_sq if included
     */
    fun create_batch(
        project_id: Int,
        requests: List<TaskCreateRequest>,
        user_id: Int,
    ): List<Task> {
        val start = System.currentTimeMillis()
        val res = transactionTemplate.extExecute {

            val project = Project.get(
                project_id, include = Includes.setOf(
                    Project::members,
                )
            )
            val task_for_export = project.is_exported_project
            if (requests.any {
                    it.task?.creation_source in setOf(
                        TaskCreationSource.SCRAPER_IMPORT,
                        TaskCreationSource.SCRAPER_IMPORT_FRESH
                    )
                }
            ) {
                val advisor_ids = requests.mapNotNull { it.task?.advisor_id }.toSet()
                val client_id = project.client_id ?: biz_error("client_id not found")
                clientBlacklistService.validate_task_creation(client_id = client_id, advisor_ids = advisor_ids)
            }

            // check duplication
            val check_dup_res = taskAngleService.find_tasks_with_duplicate_advisor(
                project_id,
                requests.mapNotNull { it.task }
            )
            val not_follow_up = check_dup_res.not_follow_up
            val follow_up = check_dup_res.follow_up
            val without_advisor = check_dup_res.without_advisor

            val valid_tasks = not_follow_up + follow_up + without_advisor
            follow_up.forEach { it.is_follow_up = true }

            // insert tasks to db
            var entities = self.insert_tasks_batch(valid_tasks, project, user_id)
            // reevaluate rate after creation
            entities = taskRateService.reevaluate_rate(project_id, entities)

            // take snapshots, create task advisor profile
            val job_ids = requests
                .mapNotNull { request -> request.advisor_job_id.takeIf { it.is_valid_id } }
                .toSet()

            // to reduce query times, we fetch necessary info in advance
            Task.join(valid_tasks, Includes.setOf(Task::advisor))
            val request_map = requests.associateBy { it.task?.advisor_id }
            // add in latest_submitted_sq for take_sq_snapshot_batch if requested in request
            val tasks_need_sq = valid_tasks.filter { request_map[it.advisor_id]?.include_sq == true }
            val original_task_map =
                tasks_need_sq.associate { Pair(it.advisor_id, request_map[it.advisor_id]?.original_task_id) }
            val screening_questions =
                retrieve_latest_submitted_sq(original_task_map.values.filterNotNull()).associateBy { it.source_id }
            val sq_tasks = tasks_need_sq.map { task ->
                task.apply {
                    val relevant_sq = screening_questions[request_map[task.advisor_id]?.original_task_id]
                    relevant_sq?.let {
                        this.latest_submitted_sq = it
                    }
                }
            }
            // this step require Task::advisor
            self.take_job_snapshot_batch(valid_tasks, job_ids)
            // this step requires Task::latest_sq or Task::latest_submitted_sq or TaskCreateRequest::advisor_inquiry_instance_id
            self.take_sq_snapshot_batch(valid_tasks = sq_tasks, entities = entities)
            // handle rank, display_id
            self.adjust_ranks_batch(project_id, entities)
            if (task_for_export) {
                set_task_ready_to_export_status(entities.ids())
            }
            entities
        }

        val took_time = System.currentTimeMillis() - start
        logger.info("took ${took_time}ms to create ${res.size} tasks")

        return res
    }

    fun set_task_ready_to_export_status(task_ids: Set<Int>) {
        TaskOutsourceInfo.findAll(
            query = TaskOutsourceInfo.Query(
                task_ids = task_ids
            )
        ).forEach {
            TaskOutsourceInfo.delete(it.id)
        }
        val ready_to_save = task_ids.map {
            TaskOutsourceInfo {
                this.task_id = it
                this.outsource_advisor_from = Region.current
            }
        }
        TaskOutsourceInfo.save(ready_to_save)
    }

    @PerformanceMonitor
    internal fun insert_tasks_batch(
        tasks: List<Task>,
        project: Project,
        user_id: Int,
    ): List<Task> {
        // currently, we can't use the batch API to save tasks, cause we need the autoincrement id
        // (spring-jdbc batch update API doesn't update autoincrement id)
        return tasks.map { task ->
            task.create_by_id = user_id
            task.project_id = project.id
            val entity = autofil_task_properties(
                task = task,
                project = project,
                user_id = user_id,
            )
            Task.save(
                entity,
                cascades = setOf(
                    Cascade.Save.oneToOne(Task::task_outsource_info)
                ),
            )
        }
    }

    /**
     * to speed up batch creation
     * ensure the following properties are joined before calling this method
     * Project::members,
     */
    private fun autofil_task_properties(
        task: Task,
        project: Project,
        user_id: Int,
    ): Task {

        // auto fill: project_type, project_sub_type , client_id
        task.project_type = project.type
        task.project_sub_type = project.sub_type
        if (task.type == TaskType.CONSULTATION) {
            task.client_id = project.client_id
        }
        if (task.type == TaskType.CLIENT_TASK) {
            // fixme: may cause performance issue in future
            val contact =
                ClientContact.get(task.client_contact_id.assert_valid_id())
            task.client_id = contact.client_id
        }

        // new task set support_uid to request uid, support_uid should be one of the project members.
        if (task.type in listOf(TaskType.CONSULTATION, TaskType.ADVISOR_TASK)) {
            task.lead_uid =
                project.members?.firstOrNull { it.role == ProjectMember.Role.PROJECT_MANAGER }?.uid
            task.support_uid = user_id
        }

        return task
    }

    // note that this method also mutates tasks
    @PerformanceMonitor
    internal fun adjust_ranks_batch(project_id: Int, tasks: List<Task>) {
        // set rank for tasks without angle
        val without_angle_tasks = tasks.filter { !it.angle_id.is_valid_id }
        var rank_start = mapper.autoinc_rank(project_id)
        without_angle_tasks.forEach {
            Patch.fromMutator(it) {
                it.rank = rank_start++
            }.patch()
        }

        // set rank for tasks with valid angle id
        val affected_angle_ids = tasks.ids(Task::angle_id)
            .filter { it.is_valid_id }
        taskDisplayIdService.reorder_angles_task_rank(affected_angle_ids)
    }

    internal fun retrieve_latest_submitted_sq(task_ids: List<Int>): List<InquiryInstance> {
        val items = InquiryInstance.findAll(
            InquiryInstance.Query(
                create_type = InquiryInstance.CreateType.BEFORE_TASK,
                source_ids = task_ids.toSet(),
                inquiry_type = Inquiry.Type.SCREENING,
                status_ne = InquiryInstance.Status.ASSIGNED
            )
        )
            // get latest submitted instance
            .groupBy { it.source_id }
            .map { entry ->
                entry.value.maxByOrNull {
                    it.submit_time ?: Instant.EPOCH
                }!!
            }
            .toList()
        return items
    }

    /**
     * for speed up com.cpvsn.rm.core.features.task.TaskService#create_batch method
     * ensure the following properties are joined before calling this method
     * Task::advisor
     *
     * if Task.advisor_profile already present, we update its task_id property
     * to newly created task_id.
     * otherwise, we create a TaskAdvisorProfile with the following rules.
     * if job_id is not specified when create task, we use the advisor's current job
     *
     * Note that for each advisor_id, it can only occur at most one time in tasks parameter.
     */
    @PerformanceMonitor
    internal fun take_job_snapshot_batch(tasks: List<Task>, job_ids: Set<Int>) {
        val advisor_tasks = tasks.filter { it.is_with_advisor }
        val specified_jobs = AdvisorJob.findAll(
            AdvisorJob.Query(
                ids = job_ids
            ), include = Includes.setOf(AdvisorJob::company)
        ).associateBy { it.advisor_id }
        val current_jobs = AdvisorJob.findAll(
            AdvisorJob.Query(
                advisor_ids = advisor_tasks.ids(Task::advisor_id) - specified_jobs.keys,
                is_current = true,
            ), include = Includes.setOf(AdvisorJob::company)
        ).associateBy { it.advisor_id }
        val advisor_id_job_map = specified_jobs + current_jobs

        val entities = advisor_tasks.map { task ->
            val profile = task.advisor_profile
            if (profile != null) {
                profile.task_id = task.id
                profile
            } else {
                val advisor = biz_require_not_null(task.advisor)
                val job = advisor_id_job_map[advisor.id]
                TaskAdvisorProfile {
                    task_id = task.id
                    advisor_id = advisor.id
                    background = advisor.background.orEmpty()
                    job_id = job?.id
                    company_id = job?.company_id ?: 0
                    position = job?.position.orEmpty()
                    is_current = job?.is_current ?: false
                    start_date = job?.start_date.orEmpty()
                    end_date = job?.end_date.orEmpty()
                    time_minimum = advisor.time_minimum
                }
            }
        }

        taskAdvisorProfileRepo.batchSave(entities)
    }

    /**
     * Cascade saves the old task's screening questions and screening question answers. Requires valid_tasks to have `latest_sq` included.
     * A normal cascade save probably wouldn't work because the relationship between [Task] and [InquiryInstance] is not an `@Relation`
     * @param valid_tasks old tasks that are being copied - each task's `advisor_id` should only occur once in this list
     * @param entities new task copies that have been saved already - each task's `advisor_id` should only occur once in this list
     */
    internal fun take_sq_snapshot_batch(
        valid_tasks: List<Task>, entities: List<Task>
    ): List<InquiryInstance> {
        /**
         * Used to find the id of the new task that each new `latest_submitted_sq` should be associated with
         */
        val new_entity_task_map = entities.associate { Pair(it.advisor_id, it.id) }

        /**
         * Take each task's `latest_sq` and `latest_submitted_sq` change its source_id to the newly created task with the same `advisor_id`
         */
        val sq_copy_tasks = valid_tasks.mapNotNull { task ->
            new_entity_task_map[task.advisor_id]?.let { new_task_id ->
                task.latest_submitted_sq?.apply {
                    // We don't want to include any automatic SQs
                    this.client_publish_question_ids = task.latest_submitted_sq?.qa_list_snapshot?.filter {
                        !it.is_advisor_job_confirm
                    }?.map { it.id }
                    this.qa_list_snapshot = this.qa_list_snapshot.filter {
                        !it.is_advisor_job_confirm
                    }
                    this.id = 0
                    this.copied_from_source_id = source_id
                    this.source_id = new_task_id
                    this.is_one_off_question = false
                }
            }
        }

        val new_inquiry_instances = inquiryInstanceRepo.save_copy_batch(sq_copy_tasks)
        return new_inquiry_instances
    }

    /**
     * Copies tasks from one project to another on the Project Search New page. Differs from the task copy function in the Client Tasks tab of the project page because the front-end doesn't already have the task loaded in this instance.
     * This function also uses [create_batch] to copy tasks, but it loads the tasks first.
     * @param selected_angle_ids copies tasks from this angle
     * @param selected_sq_instance_ids copies tasks associated with these SQ instances
     * @param tc_signed if true, only copies tasks if they have signed TCs
     * @param screened if true, only copies screened experts
     * @param target_project_id copies tasks to this project
     * @param target_angle_id copies tasks to this angle within the specified project
     * @param user_id the new tasks' `create_by_id`
     * @param include_sq_responses if true, will include the SQ and SQ responses associated with this task
     * @param include_sq_summary if true, will include the tasks' SQ summary
     * @param include_relevant_employer if true, will include the advisor job associated with this task
     */
    fun attach_copied_experts(
        selected_angle_ids: Set<Int>,
        selected_sq_instance_ids: Set<Int>,
        tc_signed: Boolean,
        screened: Boolean,
        target_project_id: Int,
        target_angle_id: Int,
        user_id: Int,
        include_sq_responses: Boolean = false,
        include_sq_summary: Boolean = false,
        include_relevant_employer: Boolean = false
    ): FindTasksWithDuplicateAdvisorResponse {
        val sq_status = if (screened) TaskStatus.SQ.RESPONDED else null

        val includes = if (include_relevant_employer) Includes.setOf(Task::advisor, Task::angle, Task::advisor_profile)
        else Includes.setOf(Task::advisor, Task::angle)

        val angle_tasks = Task.findAll(
            query = Task.Query(
                angle_ids = selected_angle_ids,
                sq_status = sq_status
            ),
            includes
        )

        val instance = InquiryInstance.findAll(
            InquiryInstance.Query(ids = selected_sq_instance_ids)
        )
        val instance_tasks = Task.findAll(
            query = Task.Query(
                ids = instance.map { it.source_id }.toSet(),
                sq_status = sq_status
            ),
            Includes.setOf(Task::advisor, Task::angle)
        )

        var merged_tasks = angle_tasks + instance_tasks

        if (tc_signed) {
            merged_tasks = merged_tasks.filter { it.advisor?.tc_term_valid?.containsValue(true) == true }
        }

        val create_tasks = merged_tasks.filter { it.advisor_id != null }.map {
            Task().apply {
                this.advisor_id = it.advisor_id
                this.angle_id = target_angle_id
                this.willingness = TaskWillingnessType.UNKNOWN
                this.screening_summary = if (include_sq_summary) it.screening_summary else ""
            }
        }
        // check duplication
        val check_dup_res = taskAngleService.find_tasks_with_duplicate_advisor(
            target_project_id,
            create_tasks
        )
        val valid_tasks = check_dup_res.not_follow_up
            .plus(check_dup_res.follow_up)
            .minus(check_dup_res.employees_conflict_investment_target)
            .minus(check_dup_res.empty_employment_history_advisor)
            .minus(check_dup_res.with_not_current_region_advisor)
        val create_task_requests = valid_tasks.map { task ->
            val original_task = merged_tasks.find { task.advisor_id == it.advisor_id }
            TaskCreateRequest(
                task = task,
                original_task_id = original_task?.id,
                include_sq = include_sq_responses,
                advisor_job_id = if (include_relevant_employer) original_task?.advisor_profile?.job_id else null
            )
        }
        create_batch(target_project_id, create_task_requests, user_id)
        return check_dup_res
    }

    fun angle_contains_keywords(
        angle: TaskAngle?,
        angles_keywords: List<String>,
    ): Boolean {
        if (angle == null) return false
        val contains_keyword = angles_keywords.any { keyword ->
            angle.name.contains(keyword, true)
                    || (angle.topic?.contains(keyword, true) == true)
                    || (angle.discussion_topic?.contains(keyword, true) == true)
        }
        return contains_keyword
    }

}

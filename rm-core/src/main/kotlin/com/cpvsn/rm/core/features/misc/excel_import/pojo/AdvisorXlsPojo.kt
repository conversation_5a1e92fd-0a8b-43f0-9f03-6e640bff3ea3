package com.cpvsn.rm.core.features.misc.excel_import.pojo

import com.cpvsn.rm.core.features.misc.excel_import.model.AdvisorXlsRow
import com.cpvsn.rm.core.util.ExcelColumn
import com.cpvsn.rm.core.util.Formatters
import org.apache.poi.ss.usermodel.DateUtil
import java.time.LocalDate

class AdvisorXlsPojo : AdvisorXlsPojoInterface {
    @ExcelColumn(naturalIndex = 0)
    var row: Int = 0

    @ExcelColumn(naturalIndex = 1)
    var linkedin_url: String = ""

    @ExcelColumn(naturalIndex = 2)
    var name: String = ""

    @ExcelColumn(naturalIndex = 3)
    var position: String = ""

    @ExcelColumn(naturalIndex = 4)
    var company: String = ""

    @ExcelColumn(naturalIndex = 5)
    var location: String = ""

    @ExcelColumn(naturalIndex = 6)
    var start_date: String = ""

    @ExcelColumn(naturalIndex = 7)
    var project_id: String = ""

    @ExcelColumn(naturalIndex = 8)
    var sourced_by_name: String = ""

    @ExcelColumn(naturalIndex = 9)
    var email_1: String = ""

    @ExcelColumn(naturalIndex = 10)
    var email_2: String = ""

    @ExcelColumn(naturalIndex = 11)
    var email_3: String = ""

    @ExcelColumn(naturalIndex = 12)
    var email_4: String = ""

    @ExcelColumn(naturalIndex = 13)
    var phone_1: String = ""

    @ExcelColumn(naturalIndex = 14)
    var phone_2: String = ""

    @ExcelColumn(naturalIndex = 15)
    var phone_3: String = ""

    @ExcelColumn(naturalIndex = 16)
    var is_linkedin_premium: String = ""

    @ExcelColumn(naturalIndex = 17)
    var lead_group_name: String = ""


    val emails: List<String> by lazy {
        listOf(email_1, email_2, email_3, email_4)
            .map { it.trim() }
            .distinct()
            .filter { it != "0" && it != "#N/A" && it != "#ERROR!" && it.isNotBlank() }
    }
    val phones: List<String> by lazy {
        listOf(phone_1, phone_2, phone_3)
            .map { it.trim() }
            .distinct()
            .filter { it != "0" && it != "#N/A" && it != "#ERROR!" && it.isNotBlank() }
    }
    val location_list: List<String> by lazy {
        location.split(",")
            .map { s -> s.trim() }
            .filter { it.isNotBlank() }
    }

    override fun toAdvisorXlsRow(
        batch: String,
    ): AdvisorXlsRow {
        val entity = AdvisorXlsRow()
        entity.let {
            it.batch = batch
            it.row_num = this.row

            it.linkedin_url = this.linkedin_url.trim()
            it.name = this.name.trim()
            it.position = this.position.trim()
            it.company = this.company.trim()
            it.locations = this.location_list
            it.start_date = try {
                // Excel Date Number
                this.start_date.takeIf { s -> s.isNotBlank() }?.let {
                    DateUtil.getJavaDate(this.start_date.removeSuffix(".0").toInt().toDouble()).let { date ->
                        Formatters.DATETIME.`yyyy-MM-dd`.format(date.toInstant())
                    }
                } ?: ""
            } catch (e: Exception) {
                try {
                    // csv string
                    val strs = this.start_date.split("/")
                    LocalDate.of(strs[2].toInt(), strs[0].toInt(), strs[1].toInt()).let { localDate ->
                        Formatters.DATETIME.`yyyy-MM-dd`.format(localDate)
                    }
                } catch (e: Exception) {
                    ""
                }
            }
            it.target_project_id = this.project_id.trim().removeSuffix(".0")
                .takeIf { s -> s.isNotBlank() }?.toInt()
            it.sourced_by_name = this.sourced_by_name.trim()
            it.emails = this.emails
            it.phones = this.phones
            it.is_linkedin_premium = this.is_linkedin_premium.toBoolean()
            it.lead_group_name = this.lead_group_name
        }
        return entity
    }
}
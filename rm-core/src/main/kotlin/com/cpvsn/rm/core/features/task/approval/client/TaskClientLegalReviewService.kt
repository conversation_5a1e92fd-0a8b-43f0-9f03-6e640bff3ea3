package com.cpvsn.rm.core.features.task.approval.client

import com.cpvsn.core.util.extension.assert_valid_id
import com.cpvsn.core.util.extension.is_valid_id
import com.cpvsn.crud.jdbc.batch.JdbcEntityBatchDao
import com.cpvsn.crud.jdbc.batch.JdbcEntityBatchRepo
import com.cpvsn.crud.model.Includes
import com.cpvsn.crud.model.dot
import com.cpvsn.rm.core.base.service.RmBaseRepository
import com.cpvsn.rm.core.features.client.Client
import com.cpvsn.rm.core.features.client.legal_preference.ClientComplianceApproveMethod
import com.cpvsn.rm.core.features.client.legal_preference.chaperone.TaskClientChaperone
import com.cpvsn.rm.core.features.email.EmailService
import com.cpvsn.rm.core.features.email_template.EmailTemplate
import com.cpvsn.rm.core.features.email_template.PlaceholderBasedEmailTemplateService
import com.cpvsn.rm.core.features.email_template.constant.EmailContentType
import com.cpvsn.rm.core.features.email_template.models.PlaceholderBasedModelIds
import com.cpvsn.rm.core.features.outsource.OutsourceService
import com.cpvsn.rm.core.features.project.ProjectWorkflow
import com.cpvsn.rm.core.features.task.Task
import com.cpvsn.rm.core.features.task.TaskService
import com.cpvsn.rm.core.features.task.approval.TaskLegalReviewResult
import com.cpvsn.rm.core.features.task.event.TaskEvent
import com.cpvsn.rm.core.features.task.event.TaskEventService
import com.cpvsn.rm.core.util.biz_error
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional


@Service
class TaskClientLegalReviewService : RmBaseRepository<TaskClientLegalReview>(),
    JdbcEntityBatchRepo<Int, TaskClientLegalReview> {

    //region @
    @Autowired
    private lateinit var taskService: TaskService

    @Autowired
    private lateinit var taskEventService: TaskEventService

    @Autowired
    private lateinit var placeholderBasedEmailTemplateService: PlaceholderBasedEmailTemplateService

    @Autowired
    private lateinit var emailService: EmailService

    @Autowired
    private lateinit var outsourceService: OutsourceService
    //endregion

    companion object {
        const val WEISS_COMPLIANCE_ADDRESS = "<EMAIL>"
    }

    override val batchDao: JdbcEntityBatchDao<Int, TaskClientLegalReview> by lazy {
        JdbcEntityBatchDao(TaskClientLegalReview::class, dataSource)
    }

    @Transactional
    fun review_batch(reviews: List<TaskClientLegalReview>): List<TaskClientLegalReview> {
        val review_result = reviews.map {
            review(it)
        }
        return review_result
    }

    /**
     * 8.Consultation Approval:
     *  a.Approval/Rejection Process
     *    i.Client Approve: Only if Approval by Email is turned on, not Approval by Portal
     *    ii.Capvision Approve: Only appears 1) after Client Approval or 2) if no Client Approval required
     *    iii.Approve Both: Only if Approval by Email is turned on, not Approval by Portal
     */
    @Transactional
    fun review(review: TaskClientLegalReview): TaskClientLegalReview {
        val task = Task.get(
            review.task_id,
            Includes.setOf(
                Task::latest_submitted_pre_ca,
                Task::client dot Client::compliance_preference,
            )
        )
        review.chaperone_ids?.let { chaperone_ids ->
            TaskClientChaperone.findAll(
                query = TaskClientChaperone.Query(
                    task_id = task.id
                )
            ).forEach {
                TaskClientChaperone.delete(it)
            }
            TaskClientChaperone.saveBatch(chaperone_ids.map { chaperone_id ->
                TaskClientChaperone().apply {
                    this.task_id = task.id
                    this.chaperone_id = chaperone_id
                }
            })
        }
        pre_review_validate(task, review)

        review.advisor_id = task.advisor_id.assert_valid_id()
        review.project_id = task.project_id
        review.task_ca_id = task.latest_submitted_pre_ca?.id

        review.apply { save(this) }

        taskEventService.trigger_event(TaskEvent().apply {
            task_id = review.task_id
            type = review.event_type
            payload_id = review.id
        })

        if (!task.ban_capvision_com_emails()) {
            send_email_notification(task, review)
        }

        return review
    }

    private fun pre_review_validate(task: Task, review: TaskClientLegalReview) {
        val manipulate_by_cap_legal = !review.contact_id.is_valid_id
        val approve_method = task.client?.compliance_preference?.approve_method
        if (manipulate_by_cap_legal
            && approve_method != ClientComplianceApproveMethod.EMAIL
        ) {
            biz_error("approve method of client(with id ${task.client?.id}) is set to $approve_method, therefore cannot be approved by Capvision Legal Team")
        }
    }

    private fun send_email_notification(task: Task, review: TaskClientLegalReview) {
        val manipulate_by_cap_legal = !review.contact_id.is_valid_id
        val content_type = when (review.result) {
            TaskLegalReviewResult.HOLD, TaskLegalReviewResult.REQUEST_MORE_INFO -> return
            TaskLegalReviewResult.CAPVISION_DIRECT_APPROVED -> error("impossible")
            TaskLegalReviewResult.APPROVED -> if (manipulate_by_cap_legal)
                EmailContentType.NOTIFY_CAP_LEGAL_CLIENT_APPROVED
            else
                EmailContentType.NOTIFY_CLIENT_CLIENT_LEGAL_APPROVED

            TaskLegalReviewResult.REJECTED -> if (manipulate_by_cap_legal)
                EmailContentType.NOTIFY_CAP_LEGAL_CLIENT_APPROVED
            else
                EmailContentType.NOTIFY_CLIENT_CLIENT_LEGAL_REJECTED
        }
        val email = placeholderBasedEmailTemplateService.process_first_by_data_id(
            EmailTemplate.Query(
                is_draft = false,
                content_type = content_type,
                is_system_template = true
            ),
            PlaceholderBasedModelIds(
                user_id = review.user_id,
                client_id = task.client_id,
                project_id = task.project_id,
                task_id = task.id,
                advisor_id = task.advisor_id
            ).apply {
                context_params["task_client_legal_review"] = review
            }
        ).to_email_request()
        emailService.send(email)

        // extra approved email for client Weiss
        val client = Client.firstOrNull(
            query = Client.Query(id = task.client_id),
            sort = null,
            include = Includes.setOf(Client::preference)
        )
        if (client?.preference?.preferred_workflow == ProjectWorkflow.WEISS
            && content_type == EmailContentType.NOTIFY_CLIENT_CLIENT_LEGAL_APPROVED
        ) {
            val extra_eamil = email.copy(
                to_list = listOf(WEISS_COMPLIANCE_ADDRESS),
                cc_list = emptyList(),
                bcc_list = emptyList()
            )
            emailService.send(extra_eamil)
        }
    }

}

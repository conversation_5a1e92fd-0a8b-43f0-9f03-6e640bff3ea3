package com.cpvsn.rm.core.features.client.contact

import com.cpvsn.core.base.BitwiseEnum
import com.cpvsn.core.base.entity.BaseQuery
import com.cpvsn.core.svc.spring.CoreAppContextHolder
import com.cpvsn.core.util.delegates.PropDelegates
import com.cpvsn.crud.orm.annotation.Column
import com.cpvsn.crud.orm.annotation.PatchFields
import com.cpvsn.crud.orm.annotation.Relation
import com.cpvsn.crud.orm.entity.SoftDeletable
import com.cpvsn.crud.query.Criteria
import com.cpvsn.crud.query.CustomSorts
import com.cpvsn.rm.core.base.entity.RmCompanion
import com.cpvsn.rm.core.base.entity.RmEntity
import com.cpvsn.rm.core.base.model.MaybeHasNdbID
import com.cpvsn.rm.core.base.model.Zoned
import com.cpvsn.rm.core.base.pojo.ClientPortalJsonView
import com.cpvsn.rm.core.features.auth.GeneralUser
import com.cpvsn.rm.core.features.auth.GeneralUserRole
import com.cpvsn.rm.core.features.client.Client
import com.cpvsn.rm.core.features.client.ClientNote
import com.cpvsn.rm.core.features.client.ClientOffice
import com.cpvsn.rm.core.features.client.accountnote.ClientAccountNote
import com.cpvsn.rm.core.features.client.usage.UsagePojo1
import com.cpvsn.rm.core.features.email.EmailAddressScore
import com.cpvsn.rm.core.features.misc.communication.CommunicationRecord
import com.cpvsn.rm.core.features.misc.constant.CapProduct
import com.cpvsn.rm.core.features.misc.contact_info.ContactInfo
import com.cpvsn.rm.core.features.misc.location.Location
import com.cpvsn.rm.core.features.misc.location.LocationRepo
import com.cpvsn.rm.core.features.portal.client_user.ClientPortalUser
import com.cpvsn.rm.core.features.project.Project
import com.cpvsn.rm.core.features.project.ProjectClientContact
import com.cpvsn.rm.core.features.task.Task
import com.cpvsn.rm.core.features.user.User
import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.annotation.JsonView
import org.springframework.beans.factory.getBean
import java.time.Instant
import javax.validation.constraints.Email

class ClientContact : RmEntity(), Zoned, SoftDeletable, GeneralUser, MaybeHasNdbID {
    companion object : RmCompanion<ClientContact>() {
        override fun saveSql(): String {
            return super.saveSql() + " on duplicate key update update_at=now()"
        }
    }

    @Column
    override var ndb_id: Int? = null

    @get:JsonIgnore
    @get:JsonView(ClientPortalJsonView::class)
    override val general_user_role: GeneralUserRole
        get() = GeneralUserRole.CLIENT_CONTACT

    @Column
    var client_id: Int = 0

    @Column
    var office_id: Int = 0

    /**
     * Reports to Function: Ability to tag an assistant to a common user
     */
    @Column
    var reports_to_contact_id: Int = 0

    @Column
    @JsonView(ClientPortalJsonView::class)
    var firstname: String = ""

    @Column
    @JsonView(ClientPortalJsonView::class)
    var lastname: String = ""

    @Column
    @get:JsonView(ClientPortalJsonView::class)
    override val name: String
        get() = listOfNotNull(
            firstname,
            lastname,
        ).filter { it.isNotBlank() }.joinToString(" ")

    @Column
    @JsonIgnore
    var types_str: String = ""

    @Column
    @JsonIgnore
    var types_bits: Int = 0

    @PatchFields(["types_str", "types_bits"])
    var types: Set<Type>
        get() {
            return Type.fromBits(types_bits)
        }
        set(value) {
            types_str = value.joinToString(",")
            types_bits = Type.toBits(value)
        }

    @get: JsonIgnore
    val is_common_user: Boolean
        get() = types.any { it == Type.COMMON }

    @get: JsonIgnore
    val is_legal_user: Boolean
        get() = types.any { it == Type.LEGAL }

    @Column
    var status: Status = Status.PROSPECT

    @Column
    var location_id: Int? = null

    @Column
    var background: String = ""

    @Column
    @Email
    // hide contact info
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    override var email: String? = null

    @Column
    @get:JsonView(ClientPortalJsonView::class)
    override var zone_id_string: String? = null

    // password to login compliance platform
    @Column
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    var password: String? = null

    /**
     * indicates if a contact is allowed to use our portal (both rm-portal and client-portal)
     */
    @Column
    @get:JsonView(ClientPortalJsonView::class)
    override var is_blocked: Boolean = false

    @Column
    @JsonIgnore
    var product_of_interests_bits: Int? = null

    @PatchFields(["product_of_interests_bits"])
    var product_of_interests: Set<CapProduct> by PropDelegates.delegate(
        this::product_of_interests_bits,
        { CapProduct.fromBits(it ?: 0) },
        { CapProduct.toBits(it) }
    )

    @Column
    @JsonIgnore
    var coverages_bits: Int? = null

    /**
     * https://www.notion.so/capvision/BCG-Division-data-point-at-client-level-18523199f41a809aa2e9e52f0df87245?d=18723199f41a80bfbb30001c18dd3562
     * BCG only - tells you to which division of BCG the client contact belongs
     */
    @Column
    var division: Division? = null

    @Column
    @JsonIgnore
    var roles_str: String? = null

    @Column
    var create_by_id: Int = 0

    @Column
    var delete_by_id: Int = 0

    @PatchFields(["roles_str"])
    var roles: Set<Role>? by PropDelegates.comma_nullable_set(this::roles_str) {
        try {
            Role.valueOf(it)
        } catch (e: IllegalArgumentException) {
            null
        }
    }

    @PatchFields(["coverages_bits"])
    var coverages: Set<Coverage> by PropDelegates.delegate(
        this::coverages_bits,
        { Coverage.fromBits(it ?: 0) },
        { Coverage.toBits(it) }
    )

    /**
     * https://www.notion.so/capvision/AM-Dashboard-Tie-WW-records-to-Client-Users-967fbe7fd13f4d4e88bdbb3e6b2f041f
     */
    @Relation
    var whale_wisdom_filer_maps: List<ClientContactWhaleWisdomFilerMap>? = null

    @Relation
    var advisor_tag_maps: List<ClientContactAdvisorTagMap>? = null

    override var delete_at: Instant? = null

    //region +
    var contact_infos: List<ContactInfo>? = null

    var contact_infos_without_mosaic: List<ContactInfo>? = null

    // hide contact info
    /**
     * referenced in invoice templates
     * as InvoiceReportModel.{{billing_contact?.mobile_jit}}
     */
    @JsonIgnore
    var email_jit: String? = null

    @JsonIgnore
    var mobile_jit: String? = null

    @JsonIgnore
    var linkedin_url_jit: String? = null

    @Relation
    var project_client_contacts: List<ProjectClientContact>? = null

    @Relation
    var location: Location? = null

    @Relation
    var client: Client? = null

    @Relation
    var office: ClientOffice? = null

    @Relation
    var tasks: List<Task>? = null

    @Relation
    var notes: List<ClientNote>? = null

    @Relation(backReference = "client_contact_id")
    var account_notes: List<ClientAccountNote>? = null

    var projects_agg: Map<Project.Status, Int>? = null

    var current_period_usage: UsagePojo1? = null

    @Relation(backReference = "client_contact_id")
    var communication_records: List<CommunicationRecord>? = null

    @Relation(backReference = "client_contact_id")
    var jobs: List<ClientContactJob>? = null

    var last_communication_time: Instant? = null

    @Relation
    var client_portal_user: ClientPortalUser? = null

    @Relation(reference = "email", backReference = "email")
    var email_address_score: EmailAddressScore? = null

    @Relation(reference = "create_by_id")
    var create_user: User? = null

    @Relation(reference = "delete_by_id")
    var delete_user: User? = null
    //endregion

    enum class Status {
        /**
         * "Active: Client user has completed a consultation or launched a project within the last 30 days"
         */
        ACTIVE,

        /**
         * "Dormant: Client user is inactive for 30 or more days.
         * As in, the client user has not started a project or completed consultation within the last 30 days"
         */
        INACTIVE,

        TRANSFERRED,

        /**
         * "Prospect: This will be the default setting for client users.
         * Prospect is when a potential client user is not engaged with Capvision team for projects"
         */
        PROSPECT,

        LEFT_COMPANY,
    }

    /**
     * WARN: never change the mask without changing corresponding records in DB simultaneously
     */
    enum class Type(
        override val mask: Int
    ) : BitwiseEnum {
        COMMON(1),
        LEGAL(2),
        BILLING(4),
        ADMINISTRATIVE(8),
        RENEWAL_CONTRACT(16),
        ASSISTANT(32),
        COMPLIANCE(64)
        ;

        companion object : BitwiseEnum.BitwiseEnumCompanion<Type>
    }

    /**
     * WARN: never change the mask without changing corresponding records in DB simultaneously
     */
    enum class Coverage(
        override val mask: Int
    ) : BitwiseEnum {
        GENERALIST(1),
        CONSUMER(2),
        FINANCIAL_SERVICES(4),
        HEALTHCARE(8),
        TECHNOLOGY(16),

        AGRICULTURE(32),
        APPLIANCE(64),
        AUTOMOTIVE(128),
        CHEMICALS(256),
        EDUCATION(512),
        ENERGY_ENVIRONMENT(1024),
        ENGINEERING(2048),
        ENTERTAINMENT(4096),
        FOOD_AND_BEVERAGE(8192),
        LOGISTICS(16384),
        MANUFACTURING(32768),
        MATERIALS_AND_METALS(65536),
        PROFESSIONAL_SERVICES(131072),
        RETAIL(262144),
        TMT(524288),
        ;

        companion object : BitwiseEnum.BitwiseEnumCompanion<Coverage>
    }

    /**
     * https://www.notion.so/capvision/BCG-Leadership-attributes-at-client-user-level-17623199f41a80008d64fb88e8d66deb?d=17723199f41a800b9e5a001ce9b29609#17623199f41a803c9fe9f9ad560c9338
     * currently it is bcg role only
     *
     */
    enum class Role {
        ASSOCIATE_CONSULTANT,
        CONSULTANT,
        PRINCIPAL,
        PARTNER,
        MANAGING_DIRECTOR_AND_PARTNER_MDP,
        PROJECT_LEADER,
    }

    /**
     * https://www.notion.so/capvision/BCG-Division-data-point-at-client-level-18523199f41a809aa2e9e52f0df87245?d=18723199f41a80bfbb30001c18dd3562
     * BCG only - tells you to which division of BCG the client contact belongs
     */
    enum class Division {
        PIPE,
        PE_STRATEGY,
        STRATEGY
    }

    @CustomSorts(
        // prefer desc
        CustomSorts.Key(
            name = "outreach_opened",
            expr = "MAX({this.outreach_records.client_contact_tracking}.status in ('OPEN','CLICK'))"
        ),
        // prefer desc
        CustomSorts.Key(
            name = "outreach_opened_at",
            expr = "MAX({this.outreach_records.client_contact_tracking.send_grid_open_events}.timestamp)"
        ),
        CustomSorts.Key(
            name = "outreach_tracking_status",
            expr = "MAX({this.outreach_records.client_contact_tracking}.status)"
        ),
        // prefer desc
        CustomSorts.Key(
            name = "outreach_tracking_update_at",
            expr = "MAX({this.outreach_records.client_contact_tracking_tracking}.update_at)"
        ),
        /**
         * https://www.notion.so/capvision/Account-Client-lists-sorting-c284d43c1b874589967228bd447d2d32
         */
        CustomSorts.Key(
            name = "current_position",
            /**
             * We need to pick the latest current job from client_contact_job group.
             * then order by its "position" field.
             *
             * I don't have clearer way to implement this feature.
             * You are not supposed to understand this trash code tho,
             *
             * ref:
             * - https://stackoverflow.com/questions/********/get-records-with-max-value-for-each-group-of-grouped-sql-results
             */
            expr = """
                (
                select __ccj_1.position
                from client_contact_job __ccj_1
                left join client_contact_job __ccj_2
                    on __ccj_1.client_contact_id = __ccj_2.client_contact_id 
                    and __ccj_1.start < __ccj_2.start 
                    and __ccj_1.is_current = __ccj_2.is_current
                where 
                    __ccj_2.start is null -- # bigger "start" not found, so that the result row will be the latest job.
                    and __ccj_1.client_contact_id = {this}.id
                    and __ccj_1.is_current 
                    order by __ccj_1.position limit 1
                )
            """
        ),
        CustomSorts.Key(
            name = "last_communication_time",
            expr = "MAX({this.communication_records}.create_at)"
        ),
    )
    data class Query(
        @Criteria.Eq
        val id: Int? = null,
        @Criteria.IdsIn
        val ids: Set<Int>? = null,
        @Criteria.Eq
        val ndb_id: Int? = null,
        @Criteria.IdsIn
        val ndb_ids: Set<Int>? = null,
        @Criteria.Eq
        val client_id: Int? = null,
        @Criteria.IdsIn
        val client_ids: Set<Int>? = null,

        @Criteria.Eq
        val office_id: Int? = null,
        @Criteria.IdsIn
        val office_ids: Set<Int>? = null,

        @Criteria.Eq
        val status: Status? = null,
        @Criteria.In
        val status_in: Set<Status>? = null,
        @Criteria.Eq
        val is_blocked: Boolean? = null,
        @Criteria.Eq
        val name: String? = null,
        @Criteria.Contains
        val name_contains: String? = null,
        @Deprecated("use name_contains instead")
        @Criteria.Contains(columnName = "name")
        val name_like: String? = null,
        @Criteria.Eq
        val email: String? = null,
        @Criteria.In
        val email_in: Set<String>? = null,
        @Criteria.Contains
        val email_contains: String? = null,

        @Criteria.Expr(
            expr = """
            {this}.name like concat('%',#{value},'%')
            or {this}.email like concat('%',#{value},'%')
            
            or {this.contact_infos}.value like concat('%',#{value},'%')
            
            or {this.jobs}.company like concat('%',#{value},'%')
            or {this.jobs}.position like concat('%',#{value},'%')
            or {this.jobs}.description like concat('%',#{value},'%')
            
            or {this.office}.name like concat('%',#{value},'%')
            or {this.office}.description like concat('%',#{value},'%')
        """
        )
        val keyword: String? = null,

        @Criteria.Eq
        val location_id: Int? = null,
        @Criteria.IdsIn
        var location_ids: Set<Int>? = null,

        // do not add @Criteria here
        val product_of_interests_contains_all: Set<CapProduct>? = null,
        // do not add @Criteria here
        val product_of_interests_contains_any: Set<CapProduct>? = null,

        // do not add @Criteria here
        val coverages_contains_all: Set<Coverage>? = null,
        // do not add @Criteria here
        val coverages_contains_any: Set<Coverage>? = null,

        /**
         * for backward compatibility, this works as the same way as types_contains_any
         */
        // do not add @Criteria here
        val type: Type? = null,

        // do not add @Criteria here
        val types_contains_all: Set<Type>? = null,
        // do not add @Criteria here
        val types_contains_any: Set<Type>? = null,

        @Criteria.Join
        val office: ClientOffice.Query? = null,
        @Criteria.Join
        val jobs: ClientContactJob.Query? = null,
        @Criteria.Join
        val project_client_contacts: ProjectClientContact.Query? = null,
        @Criteria.Join
        val tasks: Task.Query? = null,
        @Criteria.Join(on = "{this.project_client_contacts}.project_id = {that}.id or {this.tasks}.project_id = {that}.id")
        val projects: Project.Query? = null,
        @Criteria.Join(on = "{this}.id = {that}.owner_id and {that}.owner_type = 'CLIENT_CONTACT'")
        val contact_infos: ContactInfo.Query? = null,

        @Criteria.Join
        val location: Location.Query? = null,
        @Criteria.Join(on = "{that}.client_contact_id = {this}.id and {that}.email_content_type = 'CLIENT_OUTREACH'")
        val outreach_records: CommunicationRecord.Query? = null,
        @Criteria.Join
        val communication_records: CommunicationRecord.Query? = null,
        @Criteria.Join
        val whale_wisdom_filer_maps: ClientContactWhaleWisdomFilerMap.Query? = null,
        @Criteria.Or
        val or: List<Query>? = null,
        override val includeSoftDeletedRecords: Boolean = false,
    ) : BaseQuery<ClientContact>() {
        /**
         * e.g. 5 & b'110' = 4, 6 & b'110' = 6, 7 & b'110' = 6
         */
        @Criteria.Expr("({this}.product_of_interests_bits & #{value}) = #{value}")
        val _product_of_interests_contains_all = product_of_interests_contains_all?.let { CapProduct.toBits(it) }

        /**
         * e.g. 1 & b'110' = 0, 2 & b'110' = 2
         */
        @Criteria.Expr("({this}.product_of_interests_bits & #{value})")
        val _product_of_interests_contains_any = product_of_interests_contains_any?.let { CapProduct.toBits(it) }

        @Criteria.Expr("({this}.coverages_bits & #{value}) = #{value}")
        val _coverages_contains_all = coverages_contains_all?.let { Coverage.toBits(it) }

        @Criteria.Expr("({this}.coverages_bits & #{value})")
        val _coverages_contains_any = coverages_contains_any?.let { Coverage.toBits(it) }

        @Criteria.Expr("({this}.types_bits & #{value}) = #{value}")
        val _types_contains_all = types_contains_all?.let { Type.toBits(it) }

        @Criteria.Expr("({this}.types_bits & #{value})")
        val _types_contains_any = types_contains_any?.let { Type.toBits(it) }

        @Criteria.Expr("({this}.types_bits & #{value})")
        val _type = type?.mask

        // WTF, I think that this shit is not written by me
        // I just move ClientContactQuery file to ClientContact.Query
        init {
            val locationService = try {
                CoreAppContextHolder.context.getBean<LocationRepo>()
            } catch (e: Throwable) {
                null
            }
            locationService?.let { service ->
                location_ids = location_ids?.flatMap {
                    service.get_sub_location_ids(it)
                }?.toSet()
            }
        }
    }
}

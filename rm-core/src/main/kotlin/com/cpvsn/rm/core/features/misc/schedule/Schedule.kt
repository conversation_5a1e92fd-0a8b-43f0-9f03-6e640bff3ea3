package com.cpvsn.rm.core.features.misc.schedule

import com.cpvsn.core.base.entity.BaseQuery
import com.cpvsn.crud.orm.annotation.Column
import com.cpvsn.crud.orm.annotation.Relation
import com.cpvsn.crud.query.Criteria
import com.cpvsn.rm.core.annotation.DocConstant
import com.cpvsn.rm.core.base.entity.RmCompanion
import com.cpvsn.rm.core.base.entity.RmEntity
import com.cpvsn.rm.core.base.model.Zoned
import com.cpvsn.rm.core.base.pojo.SimpleTimeRange
import com.cpvsn.rm.core.base.pojo.TimeRange
import com.cpvsn.rm.core.base.taskscoped.TaskScopedEntity
import com.cpvsn.rm.core.features.advisor.Advisor
import com.cpvsn.rm.core.features.client.contact.ClientContact
import com.cpvsn.rm.core.features.misc.entity_update_log.EntityUpdateLog
import com.cpvsn.rm.core.features.project.Project
import com.cpvsn.rm.core.features.task.Task
import com.cpvsn.rm.core.features.task.review.TaskContactReview
import com.cpvsn.rm.core.util.Formatters

import com.fasterxml.jackson.annotation.JsonIgnore
import java.time.Instant
import java.time.ZoneId

/**
 * after user arrange/schedule a task, all of advisor_id, client_contact_id, pm_id will be set to the correct value.
 * which means a schedule can be relevant to advisor, client_contact, user at the same time
 */
class Schedule : RmEntity(), Zoned, TaskScopedEntity {

    companion object : RmCompanion<Schedule>()

    @Column
    var project_id: Int? = null

    override fun set_project_id(project_id: Int) {
        this.project_id = project_id
    }

    @Column
    var task_id: Int? = null

    @Column
    var advisor_id: Int? = null

    @Column
    var client_contact_id: Int? = null

    @Column
    var pm_id: Int? = null

    @Column
    var status: Status = Status.INITIAL

    /**
     * a schedule can refer to advisor, contact, pm
     * this property decides who creates this schedule
     * for historical reason, if PM create schedule for advisors
     * this value will still be ADVISOR
     * we need to further evaluate if we should change this behavior
     */
    @Column
    var creator_type: Role = Role.ADVISOR

    @Column
    var comment: String? = null

    @Column
    var location: String = ""

    @Column
    var start_time: Instant? = null

    @Column
    var end_time: Instant? = null

    /**
     * https://www.notion.so/capvision/Client-Portal-Project-Experts-For-Review-7e265465ca17434e96761793e6bde95e
     * Client Portal:
     *  However let’s require they enter their email address above the submit button so we know who they are
     */
    @Column
    var email: String? = null

    @Column
    var expected_duration_in_minutes: Int? = null

    @Column
    override var zone_id_string: String? = null

    //region +
    @Relation
    var advisor: Advisor? = null

    @Relation
    var client_contact: ClientContact? = null

    @Relation
    var calendars: List<Calendar>? = null

    @Relation(reference = "task_id")
    var task: Task? = null

    @Relation(reference = "project_id")
    var project: Project? = null

    // a hint to client contact in schedule page
    var task_contact_review: TaskContactReview? = null

    var user_edit_log: List<EntityUpdateLog>? = null

    //endregion

    @JsonIgnore
    fun to_range(): TimeRange? {
        if (start_time == null || end_time == null) return null
        return SimpleTimeRange(start_time!!, end_time!!)
    }

    @JsonIgnore
    fun to_range_string(): String {
        val formatter = Formatters.DATETIME.`yyyy-MM-dd HHmmss`
            .withZone(ZoneId.systemDefault())
        return listOfNotNull(
            start_time,
            end_time
        ).joinToString(" - ") {
            formatter.format(it)
        }
    }

    @DocConstant
    enum class Role(val description: String) {
        PM("pm"),
        ADVISOR("advisor"),
        CLIENT_CONTACT("client contact"),
        OUTSOURCE_BRIDGE("outsource bridge"),
        BCG_EEH("bch eeh"),
        BCG_EEH_FOLLOW_UP_REQUEST("bcg eeh pass this schedule to request a follow up call")
    }

    @DocConstant
    enum class Status {
        INITIAL,
        CALENDAR_SENT,
        CANCELLED,
        ;
    }

    data class Query(
        @Criteria.Eq
        val id: Int? = null,
        @Criteria.IdsIn
        val ids: Set<Int>? = null,
        @Criteria.IsNull
        val id_is_null: Boolean? = null,
        @Criteria.IsNotNull
        val id_is_not_null: Boolean? = null,

        @Criteria.Eq
        val project_id: Int? = null,
        @Criteria.Eq
        val task_id: Int? = null,
        @Criteria.IdsIn
        val task_ids: Set<Int>? = null,

        @Criteria.Eq
        val pm_id: Int? = null,
        @Criteria.Eq
        val status: Status? = null,

        @Criteria.Eq
        val creator_type: Role? = null,
        @Criteria.In
        val creator_type_in: Set<Role>? = null,

        @Criteria.Gt
        val start_time_gt: Instant? = null,
        @Criteria.Gte
        val start_time_gte: Instant? = null,
        @Criteria.Lt
        val start_time_lt: Instant? = null,
        @Criteria.Lte
        val start_time_lte: Instant? = null,

        @Criteria.Gt
        val end_time_gt: Instant? = null,
        @Criteria.Lt
        val end_time_lt: Instant? = null,

        @Criteria.IdsIn
        val advisor_ids: Set<Int>? = null,
        @Criteria.IdsIn
        val client_contact_ids: Set<Int>? = null,

        @Criteria.Or
        val or: List<Query>? = null,

        // if value is null or false, no effect
        // if value is true, exclude cancelled records
        // we add '{this}.id is null' here because when we use this query
        // as a joined query. we don't want this condition interfere the id is null query
        // to understand the reason, just compare this following two sql.
        // select * from a left join b on a.id = b.a_id and b.id is null and b.status != 'CANCELLED'.
        // select * from a left join b on a.id = b.a_id and b.id is null and (b.id is null or b.status != 'CANCELLED').
        @Criteria.Expr(expr = "({this}.id is null or not #{value}) or {this}.status != 'CANCELLED'")
        val not_cancelled: Boolean? = true, // by default, we

        @Criteria.Eq
        val advisor_id: Int? = null,
        @Criteria.Eq
        val client_contact_id: Int? = null,

        @Criteria.Join
        val task: Task.Query? = null,

        val param_preset: ParamPreset? = null,
    ) : BaseQuery<Schedule>() {
        enum class ParamPreset {
            ARRANGE,
            PM,
            CLIENT_CONTACT_PORTAL,
            ADVISOR_PORTAL
        }
    }

}

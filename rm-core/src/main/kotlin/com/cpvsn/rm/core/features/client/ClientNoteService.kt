package com.cpvsn.rm.core.features.client

import com.cpvsn.core.model.BusinessException
import com.cpvsn.rm.core.config.Webpage
import com.cpvsn.rm.core.features.slack.SlackService
import com.cpvsn.rm.core.features.slack.SlackUserService
import com.cpvsn.rm.core.features.user.User
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.format.TextStyle
import java.util.*

@Service
class ClientNoteService {

    @Autowired
    private lateinit var slackService: SlackService

    @Autowired
    private lateinit var slackUserService: SlackUserService

    /**
     * Sends a slack message to users who have non-complete ClientNote with a remind date = today.
     */
    fun client_note_reminder(){
        val now = LocalDateTime.now()
        val start_time = now.toLocalDate().atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()
        val end_time = now.toLocalDate().atTime(23,59,59).atZone(ZoneId.systemDefault()).toInstant()

        val notes = ClientNote.findAll(
            ClientNote.Query(
                reminder_at_gte = start_time,
                reminder_at_lte = end_time,
                status_not_in = setOf(ClientNote.Status.COMPLETED)
            ),
            include = setOf(ClientNote::client.name, ClientNote::client_contact.name)
        ).groupBy { it.assignee_id }

        val users = User.findAll(
            User.Query(
                ids = notes.keys.filterNotNull().toSet()
            )
        )

        notes.forEach { remind_notes ->
            if (remind_notes.value.isNotEmpty()) {
                remind_notes.key?.let { user_id ->
                    users.find { user_id == it.id }?.let {user ->
                        // construct reminder list
                        val text = "*Your Daily Reminders:*\n" +
                                remind_notes.value
                                    .sortedBy { it.priority?.ordinal ?: Int.MAX_VALUE } // bring higher priority items to the top of list
                                    .joinToString("\n") { note ->
                                        val recipient: String =
                                            if (note.client_contact != null) "<${Webpage.ClientContactProfile(note.client_contact_id).url}|${note.client_contact!!.name}>: "
                                            else if (note.client != null) "<${Webpage.ClientProfile(note.client_id).url}|${note.client!!.name}>: "
                                            else ""
                                        "$recipient${note.content}"
                                    }
                        // construct message greeting
                        val day_of_week = now.dayOfWeek.getDisplayName(TextStyle.FULL, Locale.ENGLISH)
                        val part_of_day = if (now < now.toLocalDate().atTime(12, 0 ))"morning"
                        else "evening"
                        val greeting = "Good $part_of_day, ${user.given_name}! Happy $day_of_week!\n\n"
                        // retrieve user ID to be used as channel ID
                        val channel_id = try {
                            slackUserService.get_slack_user_id(user.id)
                        } catch (exception: BusinessException) { // if slack ID can't be found, move to the next one
                            return@forEach
                        }
                        //send message
                        slackService.postTextMessageToChannel(
                            text = greeting + text,
                            channel = channel_id
                        )
                    }
                }
            }
        }
    }
}
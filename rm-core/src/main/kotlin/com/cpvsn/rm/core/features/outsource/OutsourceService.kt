package com.cpvsn.rm.core.features.outsource

import com.cpvsn.bridge.sdk.consumer.*
import com.cpvsn.bridge.sdk.model.*
import com.cpvsn.bridge.sdk.router.RouterApiClients
import com.cpvsn.bridge.sdk.shared.TaskLegalApproved
import com.cpvsn.bridge.sdk.shared.UpdateTask
import com.cpvsn.bridge.sdk.supplier.CreateTasks
import com.cpvsn.bridge.sdk.supplier.ProjectExternalEvent
import com.cpvsn.bridge.sdk.supplier.ResetTaskCA
import com.cpvsn.bridge.sdk.supplier.TaskCACompleted
import com.cpvsn.core.model.BusinessException
import com.cpvsn.core.util.extension.assert_not_null
import com.cpvsn.core.util.extension.biz_error
import com.cpvsn.crud.model.Includes
import com.cpvsn.crud.model.Patch
import com.cpvsn.crud.model.dot
import com.cpvsn.rm.core.config.EmailProperties
import com.cpvsn.rm.core.config.oversea.OverSeaEnvService
import com.cpvsn.rm.core.extensions.assert_exist
import com.cpvsn.rm.core.extensions.user_id
import com.cpvsn.rm.core.features.advisor.Advisor
import com.cpvsn.rm.core.features.advisor.job.AdvisorJob
import com.cpvsn.rm.core.features.bridge.Region
import com.cpvsn.rm.core.features.bridge.Region.Companion.to_outsource_region
import com.cpvsn.rm.core.features.client.Client
import com.cpvsn.rm.core.features.client.ClientPreference
import com.cpvsn.rm.core.features.client.contact.ClientContact
import com.cpvsn.rm.core.features.client.contact.ClientContactService
import com.cpvsn.rm.core.features.client.legal_preference.ClientComplianceApprovePolicy
import com.cpvsn.rm.core.features.core.app_config.AppConfigEntry
import com.cpvsn.rm.core.features.core.app_config.AppConfigKey
import com.cpvsn.rm.core.features.email.EmailService
import com.cpvsn.rm.core.features.email_template.EmailTemplate
import com.cpvsn.rm.core.features.email_template.PlaceholderBasedEmailTemplateService
import com.cpvsn.rm.core.features.email_template.constant.EmailContentType
import com.cpvsn.rm.core.features.email_template.models.PlaceholderBasedModel
import com.cpvsn.rm.core.features.inquiry.model.InquiryBranch
import com.cpvsn.rm.core.features.inquiry.model.InquiryInstance
import com.cpvsn.rm.core.features.inquiry.model.InquiryInstanceTextFormUtil
import com.cpvsn.rm.core.features.inquiry.model.InquiryQuestion
import com.cpvsn.rm.core.features.misc.contact_info.ContactInfo
import com.cpvsn.rm.core.features.misc.location.Location
import com.cpvsn.rm.core.features.outsource.pojo.OutsourceProjectApproval
import com.cpvsn.rm.core.features.outsource.pojo.OutsourceProjectEvent
import com.cpvsn.rm.core.features.outsource.pojo.TaskOutsourceInfo
import com.cpvsn.rm.core.features.outsource.pojo.requests.CreateProjectRequest
import com.cpvsn.rm.core.features.outsource.pojo.requests.ExportTasksRequest
import com.cpvsn.rm.core.features.outsource.pojo.requests.SelectTasksRequest
import com.cpvsn.rm.core.features.outsource.pojo.responses.CreateTasksResponse
import com.cpvsn.rm.core.features.portal.advisor.PortalAdvisor
import com.cpvsn.rm.core.features.portal.advisor.PortalAdvisorCreationService
import com.cpvsn.rm.core.features.project.Project
import com.cpvsn.rm.core.features.project.ProjectClientContact
import com.cpvsn.rm.core.features.project.ProjectCreationService
import com.cpvsn.rm.core.features.project.ProjectMember
import com.cpvsn.rm.core.features.right.model.Role
import com.cpvsn.rm.core.features.right.model.UserRole
import com.cpvsn.rm.core.features.task.Task
import com.cpvsn.rm.core.features.task.TaskCreationService
import com.cpvsn.rm.core.features.task.angle.TaskAngle
import com.cpvsn.rm.core.features.task.approval.TaskLegalReviewResult
import com.cpvsn.rm.core.features.task.approval.cap.TaskCapLegalReview
import com.cpvsn.rm.core.features.task.arrange.enums.ConferenceParticipantRole
import com.cpvsn.rm.core.features.task.constant.BridgeType
import com.cpvsn.rm.core.features.task.constant.TaskOutsourceType
import com.cpvsn.rm.core.features.task.pojo.TaskArrangeRequest
import com.cpvsn.rm.core.features.task.pojo.TaskCreateRequest
import com.cpvsn.rm.core.features.user.User
import com.cpvsn.rm.core.util.global_executor
import com.cpvsn.web.auth.AuthContext
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.annotation.Lazy
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.RequestBody
import java.math.BigDecimal
import java.time.LocalDate

@Service
class OutsourceService {

    private val logger = LoggerFactory.getLogger(this::class.java)

    @Autowired
    private lateinit var routerApiClient: RouterApiClients

    @Autowired
    private lateinit var portalAdvisorCreationService: PortalAdvisorCreationService

    @Autowired
    private lateinit var projectCreationService: ProjectCreationService

    @Autowired
    private lateinit var placeholderBasedEmailTemplateService: PlaceholderBasedEmailTemplateService

    @Autowired
    private lateinit var emailService: EmailService

    @Autowired
    private lateinit var emailProperties: EmailProperties

    @Autowired
    private lateinit var clientContactService: ClientContactService

    @Autowired
    private lateinit var overSeaEnvService: OverSeaEnvService

    @Autowired
    @Lazy
    private lateinit var taskCreationService: TaskCreationService

    fun request_outsource_team_support(
        @RequestBody request: CreateProjectRequest,
    ): OutsourceProjectApproval {
        val project = Project.get(request.project_id, Includes.setOf(Project::client))
        val cur_user = User.get(AuthContext.user_id)
        OutsourceProjectApproval.findAll(
            query = OutsourceProjectApproval.Query(
                project_id = project.id,
                outsource_advisor_from = request.request_region
            )
        ).forEach {
            Patch.fromMutator(it) {
                if (this.local_approval == OutsourceProjectApproval.ApprovalStatus.INITIAL) {
                    this.local_approval = OutsourceProjectApproval.ApprovalStatus.CANCELED
                }
                if (this.outsource_approval == OutsourceProjectApproval.ApprovalStatus.INITIAL) {
                    this.outsource_approval = OutsourceProjectApproval.ApprovalStatus.CANCELED
                }
                this.is_latest_approval = false
            }.patch()
        }
        val approval = OutsourceProjectApproval.save(
            OutsourceProjectApproval {
                this.project_id = project.id
                this.project_tsid = project.tsid
                this.project_name = project.name
                this.sq_branch_id = request.sq_branch_id
                this.request_content = request.request_content
                this.original_request_content = project.request_content
                this.client_event_rules = request.client_event_rules
                this.consumer_client_name = project.client?.name.orEmpty()
                this.outsource_advisor_from = request.request_region
                this.outsource_advisor_to = Region.current
                this.is_latest_approval = true
                this.requester_name = cur_user.name
                this.requester_email = cur_user.email
                this.project_type = when (project.sub_type) {
                    Project.SubType.Survey -> ProjectType.Survey
                    Project.SubType.Consultation -> ProjectType.Consultation
                    else -> null
                }
            }
        )
        approve_outsource_project_approval(
            approval = approval,
            operator_email = emailProperties.dbSender,
            operator_name = emailProperties.dbSenderName,
            message = "${Region.current} DB auto approval"
        )
        return approval
    }

    @Transactional
    fun send_project_create_request(
        approval: OutsourceProjectApproval,
        message: String? = null,
    ) {
        try {
            routerApiClient.consumerActions.create_project(
                CreateProjectRequest(
                    project_id = approval.project_id.assert_exist(),
                    sq_branch_id = approval.sq_branch_id,
                    request_content = approval.request_content.orEmpty(),
                    client_event_rules = approval.client_event_rules.orEmpty(),
                    request_region = approval.outsource_advisor_from.assert_exist(),
                    approve_message = message.orEmpty(),
                    apply_user_name = approval.requester_name,
                    apply_user_email = approval.requester_email,
                ).to_outsoure_request()
            )
        } catch (_: Exception) {
            biz_error("outsource request failed. please try again or report")
        }
    }

    @Transactional
    fun outsource_tasks_select(
        @RequestBody request: SelectTasksRequest,
    ) {
        val target_project = Project.find(request.project_id).assert_exist()
        val target_tasks = Task.findAll(
            query = Task.Query(
                ids = request.task_ids,
            ),
            include = Includes.setOf(
                Task::task_outsource_info,
                Task::project,
            )
        ).filter {
            it.task_outsource_info?.outsource_advisor_to == Region.current
        }
        target_tasks.mapNotNull { it.task_outsource_info }.forEach {
            Patch.fromMutator(it) {
                this.task_outsource_general_status = TaskStatus.SELECTED
            }.patch()
        }
        val client = Client.get(
            id = target_project.client_id ?: biz_error("related client not found"),
            include = Includes.setOf(
                Client::compliance_preference
            ),
        )

        val client_need_ca = client.require_pre_call_ca
        val task_legal_approve_policy =
            client.compliance_preference?.approve_policy ?: ClientComplianceApprovePolicy.NO_NEED
        // it's best to pass tasks of a single target region at one time, or else it can't be roll back
        // part of the api call succeed
        target_tasks.groupBy {
            it.task_outsource_info?.outsource_advisor_from.assert_exist("not outsource task")
        }.forEach { entry ->
            try {
                routerApiClient.consumerActions.select_tasks(
                    SelectTasks.Request(
                        project_id = target_project.tsid.assert_exist(),
                        task_ids = entry.value.mapNotNull { it.tsid },
                        target_region = entry.key.to_outsource_region(),
                        client_need_ca = client_need_ca,
                        need_consumer_legal_approve = task_legal_approve_policy != ClientComplianceApprovePolicy.NO_NEED
                    )
                )
            } catch (_: Exception) {
                biz_error("outsource request failed. please try again or report")
            }
        }
    }

    @Transactional
    fun export_tasks(
        request: ExportTasksRequest
    ): CreateTasksResponse {
        val exported_tasks = Task.findAll(
            query = Task.Query(
                ids = request.task_ids
            ),
            include = Includes.setOf(
                Task::task_outsource_info,
                Task::advisor dot Advisor::jobs dot AdvisorJob::company,
                Task::advisor dot Advisor::location,
                Task::advisor dot Advisor::latest_signed_tc,
                Task::sq_instances,
                Task::client_rate_usd,
                Task::specified_client_rate_currency_after_multiple,
                Task::default_client_rate_usd,
                Task::default_client_rate_usd_considering_unsigned_tc,
            )
        ).filter { it.task_outsource_info?.outsource_advisor_from == Region.current }
        val project = Project.find(
            request.project_id,
            include = Includes.setOf(
                Project::manager dot ProjectMember::user,
                Project::members dot ProjectMember::user,
                Project::client dot Client::effective_contract,
                Project::client dot Client::compliance_preference,
            )
        ).assert_exist("request project doesn't exist in local")

        val task_legal_approve_policy =
            project.client?.compliance_preference?.approve_policy ?: ClientComplianceApprovePolicy.NO_NEED

        val project_members = project.members.orEmpty().filter {
            it.id != project.manager?.id
        }.map {
            Member(
                email = it.user?.email.orEmpty(),
                name = it.user?.name.orEmpty(),
                user_id = it.user?.id.toString(),
                is_manager = false,
            )
        } + (project.manager?.let {
            Member(
                email = it.user?.email.orEmpty(),
                name = it.user?.name.orEmpty(),
                user_id = it.user?.id.toString(),
                is_manager = true
            )
        } ?: biz_error("no manager found"))
        val task_request = exported_tasks.map {
            val cur_advisor = it.advisor.assert_exist()
            val client_rate_field_value =
                it.client_rate_usd ?: it.specified_client_rate_currency_after_multiple ?: it.default_client_rate_usd
                ?: it.default_client_rate_usd_considering_unsigned_tc
            val continent_names = Location.findAll(
                query = Location.Query(
                    level = 1
                )
            ).map { highest_level_location -> highest_level_location.name }.toSet()
            val full_location_name = cur_advisor.location?.let { cur_location ->
                Location.join(
                    cur_location,
                    Includes.setOf(Location::name_paths)
                )
                if (request.target_region == Region.CN) {
                    cur_location.name_paths.orEmpty().filter { cur_name_path -> cur_name_path !in continent_names }
                        .joinToString("-")
                } else {
                    cur_location.name_paths.orEmpty().joinToString("-")
                }
            }
            CreateTasks.CreateTaskRequest(
                id = it.tsid.assert_exist(),
                advisor = CreateTasks.Advisor(
                    id = cur_advisor.tsid.assert_exist(),
                    firstname = cur_advisor.firstname.orEmpty(),
                    lastname = cur_advisor.lastname.orEmpty(),
                    location = cur_advisor.location?.name.orEmpty(),
                    full_location = full_location_name.orEmpty(),
                    jobs = cur_advisor.jobs?.map { db_job ->
                        CreateTasks.AdvisorJob(
                            company_name = db_job.company?.name.orEmpty(),
                            position = db_job.position,
                            start_date = db_job.start_date,
                            end_date = db_job.end_date,
                            is_current = db_job.is_current,
                            company_type = kotlin.run {
                                val company_type = db_job.company?.type?.name?.let { company_type_name ->
                                    CreateTasks.CompanyType.valueOf(company_type_name)
                                }
                                if (request.target_region == Region.CN && company_type == null) {
                                    biz_error("advisor company type must be all not null for exporting to ${request.target_region}")
                                }
                                company_type
                            },
                        )
                    }.orEmpty(),
                    latest_sign_tc_time = cur_advisor.latest_signed_tc?.respond_time,
                ),
                rate = client_rate_field_value ?: project.client?.effective_contract?.unit_price_china
                ?: BigDecimal(it.advisor?.rate ?: 0),
                currency = project.client?.effective_contract?.currency ?: it.advisor?.rate_currency?.name,
                sq_text = convert_qa_to_text(it.sq_instances.orEmpty()),
                need_supplier_legal_approve = task_legal_approve_policy != ClientComplianceApprovePolicy.NO_NEED,
            )
        }
        val api_response = try {
            routerApiClient.supplierActions.create_tasks(
                CreateTasks.Request(
                    project_id = project.tsid.assert_exist(),
                    tasks = task_request,
                    supplier_members = project_members.filterNotNull(),
                    from_region = Region.current.to_outsource_region(),
                    supplier_project_name = project.name.orEmpty(),
                    target_region = request.target_region.to_outsource_region()
                )
            )
        } catch (_: Exception) {
            biz_error("outsource request failed. please try again or report")
        }

        val succeed_task_tsids = api_response.data.filter { it.succeeded }.map { it.id }.toSet()
        val tsid_res_map = api_response.data.associateBy { it.id }
        val succeed_tasks = exported_tasks.filter {
            it.tsid in succeed_task_tsids
        }
        val failed_tasks = exported_tasks.filter {
            it.tsid !in succeed_task_tsids
        }
        succeed_tasks.mapNotNull {
            it.task_outsource_info
        }.forEach {
            Patch.fromMutator(it) {
                this.outsource_advisor_to = request.target_region
            }.patch()
        }
        return CreateTasksResponse(
            failed_tasks = failed_tasks.map {
                val task_res = tsid_res_map[it.tsid]
                CreateTasksResponse.TaskExportRes(
                    task_id = it.id,
                    succeed = false,
                    error_message = task_res?.error_message.orEmpty(),
                    warn_message = task_res?.warn_message.orEmpty(),
                    company_restricted = task_res?.company_restricted,
                )
            }
        )
    }


    private fun convert_sq_to_text(sq_list: List<InquiryQuestion>): String {
        var current_index = 0
        return sq_list.map { cur_question ->
            // the english , will become a newline character in email template. I don't know the reason so replace
            // it to chinese ， as a temporary solution
            val cur_title = cur_question.title_text.orEmpty().replace(',', '，') + " [${cur_question.type.name}]"
            val options = if (cur_question.type == InquiryQuestion.Type.TEXT)
                ""
            else {
                cur_question.options?.mapIndexed { index, it ->
                    val current_mark = 'A' + index
                    if (cur_question.type in setOf(
                            InquiryQuestion.Type.RANKING_SET,
                            InquiryQuestion.Type.NO_RANKING_SET
                        )
                    ) {
                        "${current_mark}: ${it.value.replace(',', '，')}"
                    } else {
                        "${current_mark}: ${it.text.replace(',', '，')}"
                    }
                }.orEmpty().joinToString("<br/>")
            }
            current_index++
            "$cur_title<br/>$options".trimMargin()
        }.joinToString("<br/>")
    }

    private fun convert_qa_to_text(qa_list: List<InquiryInstance>): String {
        var current_index = 0
        return qa_list.map {
            it.questions
        }.flatten().map { cur_question ->
            val cur_title = cur_question.title_text.orEmpty()
            val cur_answer = InquiryInstanceTextFormUtil.construct_answer_text(cur_question, cur_question.answer)
            current_index++
            """
                                |Q$current_index: $cur_title
                                |A$current_index: $cur_answer
                            """.trimMargin()
        }.joinToString("\n\n")
    }

    private fun CreateProjectRequest.to_outsoure_request(): CreateProject.Request {
        val project = Project.firstOrNull(
            query = Project.Query(
                id = this.project_id
            ),
            include = Includes.setOf(
                Project::manager dot ProjectMember::user,
                Project::members dot ProjectMember::user,
                Project::client,
            )
        ).assert_exist("request project doesn't exist in local")
        // choose only project manager if set
        val project_members =
            project.members.orEmpty().filter {
                it.id != project.manager?.id
            }.map {
                Member(
                    email = it.user?.email.orEmpty(),
                    name = it.user?.name.orEmpty(),
                    user_id = it.user?.id.toString(),
                    is_manager = false,
                )
            } + Member(
                email = project.manager?.user?.email.orEmpty(),
                name = project.manager?.user?.name.orEmpty(),
                user_id = project.manager?.user?.id.toString(),
                is_manager = true
            )
        if (!project_members.any { it.is_manager == true }) {
            biz_error("no manager project")
        }
        val target_sq = this.sq_branch_id?.let { cur_branch_id ->
            InquiryBranch.firstOrNull(
                query = InquiryBranch.Query(
                    id = cur_branch_id
                ),
                include = Includes.setOf(
                    InquiryBranch::questions
                )
            ).assert_exist("sq doesn't exist").questions.orEmpty().map {
                convert_sq_to_text(listOf(it))
            }
        }
        val client_type = project.client?.type?.name.orEmpty()

        return CreateProject.Request(
            project_id = project.tsid.assert_not_null { "the project's tsid is null" },
            project_name = project.name.orEmpty(),
            project_request = this.request_content.orEmpty(),
            original_request = project.request_content,
            consumer_members = project_members,
            client_type = client_type,
            client_event_rules_desc = this.client_event_rules.orEmpty(),
            screening_questions = target_sq,
            approve_message = this.approve_message,
            consumer_client_name = project.client?.name,
            from_region = Region.current.to_outsource_region(),
            target_region = this.request_region.to_outsource_region(),
            requester_name = this.apply_user_name,
            requester_email = this.apply_user_email,
            project_type = when (project.sub_type) {
                Project.SubType.Consultation -> ProjectType.Consultation
                Project.SubType.Survey -> ProjectType.Survey
                else -> null
            }
        )
    }

    fun verify_outsource_legal_status(task_ids: Set<Int>) {
        Task.findAll(
            query = Task.Query(
                ids = task_ids
            ),
            include = Includes.setOf(Task::task_outsource_info),
        ).filter {
            it.task_outsource_info?.outsource_advisor_to == Region.current
        }.forEach { cur_task ->
            cur_task.task_outsource_info?.let {
                if (it.task_outsource_compliance_status != TaskLegalStatus.APPROVED) {
                    throw BusinessException("outsource compliance not approved")
                }
            }
        }
    }

    fun arrange_imported_task(
        task: Task,
        request: TaskArrangeRequest,
    ) {
        if (task.task_outsource_info == null) {
            Task.join(task, Includes.setOf(Task::task_outsource_info))
        }
        if (task.task_outsource_status != TaskOutsourceType.IMPORTED) {
            return
        }
        val dial_info = when (task.arrange_bridge_type) {
            BridgeType.LOOPUP -> {
                Task.join(task, Includes.setOf(Task::loopup_dial_in_for_advisor))
                mapOf("interview_info" to task.loopup_dial_in_for_advisor)
            }

            BridgeType.TWILIO, BridgeType.ZOOM -> {
                val interview_info = task.arrangement?.conference_invitees
                    ?.firstOrNull { it.role == ConferenceParticipantRole.EXPERT }
                    ?.portal_dial_info?.computer_audio_link
                mapOf("interview_info" to interview_info)
            }

            // CLIENT_PROVIDED, OTHER
            else -> {
                val interview_info =
                    "Conference Bridge Information:${request.task?.arrange_advisor_bridge_info_str ?: ""}, ".plus(
                        request.task?.arrange_advisor_calendar_location ?: ""
                    )
                mapOf("interview_info" to interview_info)
            }
        }

        try {
            routerApiClient.consumerActions.arrange(
                ArrangeTask.Request(
                    task_id = task.tsid.assert_exist(),
                    arrangement = TaskArrangement(
                        start = (task.start_time ?: task.arrangement?.start_time).assert_exist(),
                        end = task.end_time ?: task.arrangement?.end_time,
                        communication_tool = task.arrange_bridge_type?.name.orEmpty(),
                        communication_tool_props = dial_info,
                        custom_props = emptyMap()
                    ),
                    target_region = task.task_outsource_info?.outsource_advisor_from.assert_exist()
                        .to_outsource_region()
                )
            )
        } catch (_: Exception) {
            biz_error("outsource request failed. please try again or report")
        }

    }

    fun set_task_ready_to_export_status(task_ids: Set<Int>) {
        TaskOutsourceInfo.findAll(
            query = TaskOutsourceInfo.Query(
                task_ids = task_ids
            )
        ).forEach {
            TaskOutsourceInfo.delete(it.id)
        }
        val ready_to_save = task_ids.map {
            TaskOutsourceInfo {
                this.task_id = it
                this.outsource_advisor_from = Region.current
            }
        }
        TaskOutsourceInfo.save(ready_to_save)
    }

    fun send_legal_approve_request(
        reviews: List<TaskCapLegalReview>,
    ) {
        // todo: consider think of a way to distinguish outsource tasks
        TaskCapLegalReview.joinOnce(
            reviews,
            Includes.setOf(TaskCapLegalReview::task dot Task::task_outsource_info)
        )
        reviews.filter {
            (it.result in setOf(
                TaskLegalReviewResult.APPROVED,
                TaskLegalReviewResult.CAPVISION_DIRECT_APPROVED
            )) && it.task?.task_outsource_info != null
        }.forEach {
            val target_region = if (it.task?.task_outsource_info?.outsource_advisor_from == Region.current) {
                it.task?.task_outsource_info?.outsource_advisor_to
            } else {
                it.task?.task_outsource_info?.outsource_advisor_from
            }.to_outsource_region()

            global_executor.submit {
                try {
                    when (it.task?.task_outsource_status) {
                        TaskOutsourceType.EXPORTED -> {
                            routerApiClient.supplierActions.task_legal_approved(
                                TaskLegalApproved.Request(
                                    task_id = it.task?.tsid.assert_exist(),
                                    target_region = target_region,
                                )
                            )
                        }

                        TaskOutsourceType.IMPORTED -> {
                            routerApiClient.consumerActions.task_legal_approved(
                                TaskLegalApproved.Request(
                                    task_id = it.task?.tsid.assert_exist(),
                                    target_region = target_region,
                                )
                            )
                        }

                        else -> {
                            biz_error("illegal task outsource status")
                        }
                    }
                } catch (e: Exception) {
                    logger.error(e.message)
                    e.printStackTrace()
                    biz_error("outsource request failed. please try again or report")
                }
            }
        }
    }

    fun send_task_ca_link_to_gkm(
        portal: PortalAdvisor
    ) {
        val advisor_portal = portalAdvisorCreationService.create(portal)
        val target_region = TaskOutsourceInfo.firstOrNull(
            TaskOutsourceInfo.Query(task_id = advisor_portal.task_id!!)
        )?.outsource_advisor_from.assert_not_null { "outsource advisor from information is null." }

        val request = SendTaskCA.Request(
            task_id = advisor_portal.task?.tsid.assert_not_null { "task is null." },
            ca_link = advisor_portal.link_jit,
            target_region = target_region.to_outsource_region()
        )
        try {
            routerApiClient.consumerActions.send_task_ca(request)
        } catch (_: Exception) {
            biz_error("outsource request failed. please try again or report")
        }
    }

    /**
     * when the target_region is CN, FileAttachment is not null.
     * when the target_region is US or SEA, FileAttachment is null.
     */
    fun task_ca_completed(
        task_id: Int,
        file_name: String?,
        file_url: String?,
    ) {
        val task = Task.find(task_id, Includes.setOf(Task::task_outsource_info))
        val target_region =
            task?.task_outsource_info?.outsource_advisor_to.assert_not_null {
                "outsource advisor to information is null."
            }
        val file_attachement = when (target_region) {
            Region.CN -> FileAttachment(
                name = file_name.assert_not_null { "file name is null." },
                url = file_url.assert_not_null { "file url is null." }
            )

            else -> null
        }

        val request = TaskCACompleted.Request(
            task_id = task?.tsid.assert_not_null { "task is null." },
            ca_file = file_attachement,
            target_region = target_region.to_outsource_region()
        )
        try {
            routerApiClient.supplierActions.task_ca_completed(request)
        } catch (_: Exception) {
            biz_error("outsource request failed. please try again or report")
        }
    }

    fun reset_task_ca(
        task_id: Int,
    ): ResetTaskCA.Response {
        val task = Task.find(task_id, Includes.setOf(Task::task_outsource_info))
        val target_region =
            task?.task_outsource_info?.outsource_advisor_to.assert_not_null {
                "outsource advisor to information is null."
            }

        val request = ResetTaskCA.Request(
            task_id = task?.tsid.assert_not_null { "task is null." },
            target_region = target_region.to_outsource_region()
        )
        try {
            return routerApiClient.supplierActions.reset_task_ca(request)
        } catch (_: Exception) {
            biz_error("outsource request failed. please try again or report")
        }
    }

    fun task_ca_responded(
        task_id: Int,
        qa_list: List<InquiryQuestion>,
    ) {
        val task = Task.find(task_id, Includes.setOf(Task::task_outsource_info))
        val target_region =
            task?.task_outsource_info?.outsource_advisor_from.assert_not_null { "outsource advisor from information is null" }
        val responded_ca_content = build_outsource_ca_content(qa_list)

        val request = TaskCaResponded.Request(
            task_id = task?.tsid!!,
            responded_ca_content = responded_ca_content,
            target_region = target_region.to_outsource_region()
        )
        try {
            routerApiClient.consumerActions.task_ca_responded(request)
        } catch (_: Exception) {
            biz_error("outsource request failed. please try again or report")
        }
    }

    fun build_outsource_ca_content(
        qa_list: List<InquiryQuestion>
    ): List<String> {
        var current_index = 0
        return qa_list.map { cur_question ->
            val cur_title = cur_question.title_text.orEmpty()
            val cur_answer = InquiryInstanceTextFormUtil.construct_answer_text(cur_question, cur_question.answer)
            current_index++
            """
                |Q$current_index: $cur_title<br/>
                |A$current_index: $cur_answer
            """.trimMargin()
        }
    }


    fun approve_outsource_project_approval(
        approval: OutsourceProjectApproval,
        operator_email: String,
        operator_name: String,
        message: String? = null
    ) {
        val updated_approval = Patch.fromMutator(approval) {
            this.local_approval = OutsourceProjectApproval.ApprovalStatus.APPROVED
        }.patchThenGet()
        when (updated_approval.outsource_advisor_from) {
            Region.current -> {
                global_executor.execute {
                    // since CN DB may not support receiving event when requesting outsource support, execute it
                    // in another thread and sleep 10s as a tmp solution
                    if (approval.outsource_advisor_to == Region.CN) {
                        Thread.sleep(10000)
                    }
                    project_external_event(
                        approval = approval,
                        operator_email = operator_email,
                        operator_name = operator_name,
                        event_type = ProjectEventType.APPROVE,
                        supplier_members = (
                                User.findAll(
                                    query = User.Query(
                                        user_role = UserRole.Query(
                                            role = Role.Query(
                                                name = Role.Enum.OUTSOURCE_SUPPORTER.value
                                            )
                                        )
                                    )
                                ).map {
                                    Member(
                                        user_id = it.id.toString(),
                                        name = it.name,
                                        email = it.email,
                                        is_manager = false
                                    )
                                } + find_specified_supplier_manager_for_project_creation(approval).map {
                                    Member(
                                        user_id = it.id.toString(),
                                        name = it.name,
                                        email = it.email,
                                        is_manager = true
                                    )
                                }),
                        supplier_project_name = updated_approval.project_name,
                        message = message,
                    )
                }
                try {
                    create_or_update_project_for_export(updated_approval, message)
                } catch (e: Exception) {
                    logger.error("An error occurred while creating or updating the project for export", e)
                    send_auto_create_outsource_project_failed_notification_email(approval, e)
                    logger.error("Auto create outsource project failed notification email sent")
                }
            }

            else -> {
                send_project_create_request(updated_approval, message = message)
                if (approval.outsource_advisor_from == Region.CN && approval.project_type == ProjectType.Survey) {
                    create_special_advisor_for_survey_statistic(approval = approval)
                }
                project_external_event(
                    approval = approval,
                    operator_email = operator_email,
                    operator_name = operator_name,
                    event_type = ProjectEventType.APPROVE,
                    message = message,
                )
            }
        }
    }

    private fun create_special_advisor_for_survey_statistic(approval: OutsourceProjectApproval) {
        val current_project = approval.project_id?.let {
            Project.get(it)
        } ?: biz_error("current project not found")
        val task_angle = TaskAngle.firstOrNull(
            query = TaskAngle.Query(
                project_id = current_project.id,
                name = "CN outsource"
            )
        ) ?: let {
            TaskAngle.save(
                TaskAngle().apply {
                    this.project_id = current_project.id
                    this.name = "CN outsource"
                }
            )
        }
        val outsource_statistic_advisor_ids = AppConfigEntry.firstOrNull(
            AppConfigEntry.Query(
                key_enum = AppConfigKey.OUTSOURCE_STATISTIC_ADVISOR_IDS,
            )
        )?.value?.split(",")?.mapNotNull { it.toIntOrNull() }.orEmpty().toSet()
        val outsource_statistic_advisor = Advisor.firstOrNull(
            query = Advisor.Query(
                ids = outsource_statistic_advisor_ids,
            )
        ) ?: return
        // 如果存在则不自动创建
        if (Task.exists(
                query = Task.Query(
                    advisor_id = outsource_statistic_advisor.id,
                    project_id = current_project.id,
                    angle_id = task_angle.id,
                )
            )
        ) return
        taskCreationService.create_batch(
            project_id = current_project.id,
            requests = listOf(
                TaskCreateRequest(
                    task = Task().apply {
                        this.angle_id = task_angle.id
                        this.advisor_id = outsource_statistic_advisor.id
                    }
                )
            ),
            user_id = 0,
        )
    }

    fun send_auto_create_outsource_project_failed_notification_email(
        approval: OutsourceProjectApproval,
        exception: Exception
    ) {
        val model = PlaceholderBasedModel(
            outsource_approval = approval,
        ).apply {
            context_params = mapOf("exception_message" to exception.message.orEmpty())
        }
        emailService.send_and_persist(
            placeholderBasedEmailTemplateService.process_first_by_data(
                EmailTemplate.Query(
                    content_type = EmailContentType.OUTSOURCE_PROJECT_AUTO_CREATION_FAILED,
                    is_system_template = true,
                ),
                model
            ).to_email_request()
        )
        return
    }

    @Transactional
    fun reject_outsource_project_approval(
        approval: OutsourceProjectApproval,
        operator_email: String,
        operator_name: String,
        message: String? = null,
    ): OutsourceProjectEvent {
        Patch.fromMutator(approval) {
            this.local_approval = OutsourceProjectApproval.ApprovalStatus.REJECTED
        }.patch()
        return project_external_event(
            approval = approval,
            operator_email = operator_email,
            operator_name = operator_name,
            event_type = ProjectEventType.REJECT,
            message = message,
        )
    }

    @Transactional
    fun comment_outsource_project_approval(
        approval: OutsourceProjectApproval,
        operator_email: String,
        operator_name: String,
        message: String? = null,
    ): OutsourceProjectEvent {
        return project_external_event(
            approval = approval,
            operator_email = operator_email,
            operator_name = operator_name,
            event_type = ProjectEventType.COMMENT,
            message = message,
        )
    }

    fun reply_approval_message(
        approval: OutsourceProjectApproval,
        operator_email: String,
        operator_name: String,
        message: String? = null,
    ): OutsourceProjectEvent {
        return project_external_event(
            approval = approval,
            operator_email = operator_email,
            operator_name = operator_name,
            event_type = ProjectEventType.REPLY,
            message = message,
        )
    }


    private fun project_external_event(
        approval: OutsourceProjectApproval,
        operator_email: String,
        operator_name: String,
        event_type: ProjectEventType,
        supplier_members: List<Member>? = null,
        supplier_project_name: String? = null,
        message: String? = null
    ): OutsourceProjectEvent {
        val event = OutsourceProjectEvent.save(
            OutsourceProjectEvent {
                this.project_approval_id = approval.id
                this.event_type = event_type
                this.message = message
                this.operator_email = operator_email
                this.operator_name = operator_name
                this.from_region = Region.current
                this.target_region = approval.outsource_region
            }
        )
        send_project_external_event_request(
            project_tsid = approval.project_tsid.assert_exist(),
            target = event.target_region.assert_exist(),
            event_type = event.event_type.assert_exist(),
            operator_email = event.operator_email.orEmpty(),
            operator_name = event.operator_name.orEmpty(),
            supplier_members = supplier_members,
            supplier_project_name = supplier_project_name,
            message = event.message
        )
        return event
    }

    private fun send_project_external_event_request(
        project_tsid: String,
        target: Region,
        event_type: ProjectEventType,
        operator_email: String,
        operator_name: String,
        supplier_members: List<Member>? = null,
        supplier_project_name: String? = null,
        message: String? = null,
    ) {
        try {
            routerApiClient.sharedActions.project_external_event(
                ProjectExternalEvent.Request(
                    project_id = project_tsid,
                    event_type = event_type,
                    message = message.orEmpty(),
                    operator = ProjectExternalEvent.Request.Operator(
                        name = operator_name,
                        email = operator_email,
                    ),
                    supplier_members = supplier_members,
                    supplier_project_name = supplier_project_name,
                    from_region = Region.current.to_outsource_region(),
                    target_region = target.to_outsource_region()
                )
            )
        } catch (_: Exception) {
            biz_error("outsource request failed. please try again or report")
        }
    }

    @Transactional
    fun create_or_update_project_for_export(
        approval: OutsourceProjectApproval,
        approve_message: String? = null,
    ): Project {
        val target_client = Client.firstOrNull(
            query = Client.Query(
                preference = ClientPreference.Query(
                    is_outsource_client = true,
                    outsource_region = approval.outsource_advisor_to
                )
            )
        ).assert_exist()
        val client_contacts = approval.outsource_project_manager.orEmpty().map {
            ContactInfo.firstOrNull(
                query = ContactInfo.Query(
                    value = it.email,
                    owner_type = ContactInfo.OwnerType.CLIENT_CONTACT,
                )
            )?.let { contact_info ->
                val contact = ClientContact.find(contact_info.owner_id)?.let {
                    if (it.client_id != target_client.id) {
                        biz_error("client contact's client not consist")
                    }
                    it
                }
                contact
            } ?: clientContactService.save_cascade(
                ClientContact {
                    this.client_id = target_client.id
                    this.email = it.email
                    this.firstname = it.name
                    this.types = setOf(ClientContact.Type.COMMON)
                    this.contact_infos = listOf(
                        ContactInfo {
                            this.type = ContactInfo.Type.EMAIL
                            this.owner_type = ContactInfo.OwnerType.CLIENT_CONTACT
                            this.is_main = true
                            this.value = it.email
                        }
                    )
                }
            )
        }
        val res = Project.firstOrNull(
            Project.Query(
                tsid = approval.project_tsid
            ),
            include = Includes.setOf(Project::manager, Project::members)
        )?.let {
            val previous_approvals = OutsourceProjectApproval.findAll(
                query = OutsourceProjectApproval.Query(
                    project_id = it.id,
                    local_approval = OutsourceProjectApproval.ApprovalStatus.APPROVED,
                )
            )
            val previous_request_content = it.outsource_request_content.orEmpty()
            Patch.fromMutator(it) {
                this.name = approval.project_name
                this.request_content = approval.original_request_content.orEmpty()
                this.outsource_request_content =
                    "${previous_request_content}<br/>${approval.request_content.orEmpty()}"
                this.outsource_project_supplier_approve_message = approve_message
                this.outsource_project_consumer_approve_message = approval.outsource_consumer_approve_message
                this.outsource_client_compliance_rules = approval.outsource_client_event_rules
                this.outsource_client_name = approval.consumer_client_name
                this.outsource_project_type_enum = approval.project_type
                if (approval.outsource_region != Region.CN && approval.project_sub_type == Project.SubType.Survey) {
                    this.sub_type = Project.SubType.Survey
                }
            }.patchThenGet(
                include = Includes.setOf(Project::manager, Project::members, Project::project_client_contacts)
            )
        } ?: let {
            val saved_project = projectCreationService.save_cascade(
                Project {
                    this.tsid = approval.project_tsid
                    this.name = approval.project_name
                    this.client_id = target_client.id
                    this.exported_to = approval.outsource_advisor_to
                    this.start_date = LocalDate.now(overSeaEnvService.timezone_via_profile())
                    this.request_content = approval.original_request_content.orEmpty()
                    this.outsource_request_content =
                        "${approval.request_content.orEmpty()}<br/>${
                            approval.outsource_screening_questions?.joinToString(
                                ""
                            ).orEmpty()
                        }"
                    this.outsource_project_supplier_approve_message = approve_message
                    this.outsource_project_consumer_approve_message = approval.outsource_consumer_approve_message
                    this.outsource_client_compliance_rules = approval.outsource_client_event_rules
                    this.outsource_client_name = approval.consumer_client_name
                    this.outsource_project_type_enum = approval.project_type
                    if (approval.outsource_region != Region.CN && approval.project_sub_type == Project.SubType.Survey) {
                        this.sub_type = Project.SubType.Survey
                    }
                    this.project_client_contacts = client_contacts.map {
                        ProjectClientContact {
                            this.client_contact_id = it.id
                            this.client_contact_type = ProjectClientContact.Type.GENERAL
                        }
                    }
                    this.investment_target_company_ids = emptyList()
                    this.members = (
                            User.findAll(
                                query = User.Query(
                                    user_role = UserRole.Query(
                                        role = Role.Query(
                                            name = Role.Enum.OUTSOURCE_SUPPORTER.value
                                        )
                                    )
                                )
                            ).map {
                                ProjectMember().apply {
                                    this.uid = it.id
                                    this.role = ProjectMember.Role.SUPPORT_MEMBER
                                }
                            } + find_specified_supplier_manager_for_project_creation(approval).map {
                                ProjectMember().apply {
                                    this.uid = it.id
                                    this.role = ProjectMember.Role.PROJECT_MANAGER
                                }
                            })
                }
            )

            OutsourceProjectApproval.joinOnce(
                entity = approval,
                include = Includes.setOf(OutsourceProjectApproval::events)
            )

            val model = PlaceholderBasedModel(
                project = saved_project,
                client = target_client,
            ).apply {
                context_params = context_params + mapOf(
                    "outsource_project_approve_comment" to saved_project.outsource_project_approve_message.orEmpty()
                )
            }
            emailService.send_and_persist(
                placeholderBasedEmailTemplateService.process_first_by_data(
                    EmailTemplate.Query(
                        content_type = EmailContentType.OUTSOURCE_PROJECT_AUTO_CREATION_NOTIFICATION,
                        is_system_template = true,
                    ),
                    model
                ).to_email_request()
            )
            saved_project
        }
        Patch.fromMutator(approval) {
            this.project_id = res.id
        }.patch()
        return res
    }

    fun update_task_qa_request(task_id: Int) {
        val task = Task.get(task_id, Includes.setOf(Task::task_outsource_info, Task::sq_instances))
        val target_region = listOfNotNull(
            task.task_outsource_info?.outsource_advisor_from,
            task.task_outsource_info?.outsource_advisor_to
        ).firstOrNull { it != Region.current } ?: biz_error("target region not found")
        try {
            routerApiClient.sharedActions.update_task_info(
                request = UpdateTask.Request(
                    task_id = task.tsid ?: biz_error("none tsid task"),
                    sq_comment = convert_qa_to_text(task.sq_instances.orEmpty()),
                    from_region = Region.current.to_outsource_region(),
                    target_region = target_region.to_outsource_region(),
                )
            )
        } catch (_: Exception) {
            biz_error("outsource request failed. please try again or report")
        }
    }

    private fun find_specified_supplier_manager_for_project_creation(approval: OutsourceProjectApproval): List<User> {
        // some project created by sea user requesting sea support in cn db, we need to find and set the manager in auto created project
        return User.findAll(
            query = User.Query(
                email_in = approval.outsource_project_manager.orEmpty()
                    .filter { it.source == Region.SEA && approval.outsource_advisor_to == Region.CN }.map { it.email }
                    .toSet()
            )
        ).takeUnless { it.isEmpty() } ?: let {
            User.findAll(
                query = User.Query(
                    user_role = UserRole.Query(
                        role = Role.Query(
                            name = Role.Enum.OUTSOURCE_MANAGER.value
                        )
                    )
                )
            )
        }
    }

}

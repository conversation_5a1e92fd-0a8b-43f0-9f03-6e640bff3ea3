package com.cpvsn.rm.core.features.thirdparty.bcg.pojo

import com.cpvsn.rm.core.features.thirdparty.bcg.pojo.enums.BcgCallStatus
import com.cpvsn.rm.core.features.thirdparty.bcg.pojo.enums.BcgExpertProfileStatus
import com.cpvsn.rm.core.features.thirdparty.bcg.util.BcgHubConst
import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.annotation.JsonInclude
import java.time.Instant

/**
 * Cursor Prompt:
 *
请生成该swagger api文档中schemas部分所有类型对应的kotlin dataclass定义，要求：
（1）对于non-required字段，要对应成nullable类型，且默认值为Null
（2）每个字段带注释，字段注释用的是/** */
(3) 对于嵌套的复杂对象，比如Error, 要单独定义一个dataclass而不是用Any类型
(4) 对于文档中描述其值为Enum的字段，要单独在dataclass内部定义一个Enum类型。
(5)生成的enum类型要包含一个value字段，存放对应的string value, 且enum class应该带类型定义。
 *  另外，为每个dataclass添加一行javadoc注释，标注它是在哪几个api中被使用的
（6） 请根据api doc中定义的api模块和接口，去重新归类和排序schema部分的类型，并按顺序输出，要求使用//region //endregion来基于api模块来分割dataclass类型定义
 */
interface BcgHub {


    interface Project {


        //region poll interview
        /**
         * Used in: GET /expert-interview-requests
         */
        data class ExpertInterviewRequest(
            /** Acknowledgement ID */
            val acknowledgementId: String,
            /** ID of the Expert Interview Request */
            val interviewRequestId: Long,
            /** Category of the Request. It can be either Expert Interview or Expert Surveys. */
            val requestCategory: String,
            /** Title of the request */
            val requestTitle: String,
            /** Type of the project */
            val projectType: String,
            /** Description of the project */
            val projectDescription: String,
            /** Industries of the client */
            val clientIndustries: String,
            /** Anticipated number of interviews */
            val interviewAnticipatedCount: Int,
            /** Start date of the anticipated interviews in YYYY-MM-DD format */
            val interviewAnticipatedStartDate: String,
            /** End date of the anticipated interviews in YYYY-MM-DD format */
            val interviewAnticipatedEndDate: String,
            /** Creation date and time of the request in YYYY-MM-DDTHH:MM:SS format */
            val createdDateTime: String,
            /** Last update date and time of the request in YYYY-MM-DDTHH:MM:SS format */
            val updatedDateTime: String? = null,
            /** Charge code associated with the request */
            val chargeCode: String? = null,
            /** Off-limit companies for the request */
            val offLimitCompanies: String? = null,
            /** Types of Experts requested */
            val expertTypes: List<String>? = null,
            /** Contacts assigned to the request */
            val assignContacts: List<AssignContact>? = null,
            /** Email of the research manager */
            val researchManagerEmail: String? = null,
            /** Details of the Requestor */
            val requestor: Requestor? = null,
            /** S3 Links of the supporting documents as attachments related to the request */
            val attachments: List<String>? = null
        )
        //endregion

        //region ack interview
        /**
         * Used in: POST /expert-interview-requests/projectId
         */
        data class ExpertInterviewProjectIdRequest(
            /** ID of the Expert Interview Request */
            val requestId: Long,
            /** Project ID from Vendor */
            val projectId: String,
            /** Acknowledgement ID for processed message by Vendor */
            val acknowledgementId: String,
            val contactDetailsRequest: ResearchManager.ContactDetailsRequest? = null,
        )


        /**
         * Used in: POST /expert-interview-requests/projectId
         */
        data class ExpertInterviewProjectIdResponse(
            /** Expert Interview Request ID */
            val requestId: Long? = null,
            /** Project ID from Vendor */
            val projectId: String? = null,
            /** Success message */
            val message: String? = null,
            /** Http status code for Project ID save request */
            val statusCode: String? = null,
            /** Error */
            val error: String? = null
        )
        //endregion


        //region poll survey
        /**
         * Used in: GET /expert-survey-requests
         */
        data class ExpertSurveyRequest(
            /** ID of the Expert Survey Request */
            val surveyRequestId: Long? = null,
            /** Category of the Request. It can be either Expert Interview or Expert Surveys. */
            val requestCategory: String? = null,
            /** Charge code associated with the request */
            val chargeCode: String? = null,
            /** Title of the request */
            val requestTitle: String? = null,
            /** Type of the project */
            val projectType: String? = null,
            /** Description of the project */
            val projectDescription: String? = null,
            /** IncQuery flag */
            val incQuery: Boolean? = null,
            /** Anticipated number of Surveys */
            val surveyRespondentsCount: Int? = null,
            /** Start date of the anticipated surveys in YYYY-MM-DD format */
            val surveyStartDate: String? = null,
            /** End date of the anticipated surveys in YYYY-MM-DD format */
            val surveyEndDate: String? = null,
            /** Email of the research manager */
            val researchManagerEmail: String? = null,
            /** Details of the Requestor */
            val requestor: Requestor? = null,
            /** S3 Links of the supporting documents as attachments related to the request */
            val supportingDocumentsLinks: List<String>? = null,
            /** Creation date and time of the request in YYYY-MM-DDTHH:MM:SS format */
            val createdDateTime: String? = null
        )
        //endregion

        //region + nested
        /**
         * Used in: GET /expert-interview-requests
         */
        data class AssignContact(
            val expertType: String? = null,
            val caseTeamMemberEmail: String? = null
        )

        /**
         * Used in: GET /expert-interview-requests, GET /expert-survey-requests
         */
        data class Requestor(
            val email: String,
            val location: String? = null,
            val timeZone: String,
            val phoneNumber: String? = null
        )
        //endregion
    }

    interface Template {

        //region send
        /**
         * Used in: POST /templates
         */
        data class TemplateRequest(
            /** Network Provider template Id */
            val networkTemplateId: String,
            /** ID of the Expert Interview Request */
            val requestId: Long,
            /** Template */
            val template: String,
            /** expertTypes list applicable for template */
            val expertTypes: List<String>,
            /** Network provider */
            val networkProvider: String? = null
        )

        /**
         * Used in: POST /templates
         */
        data class TemplateResponse(
            /** Network Provider template Id */
            val networkTemplateId: String? = null,
            /** ID of the Expert Interview Request */
            val requestId: Long? = null,
            /** Template Status */
            val status: String? = null,
            /** Template statusCode */
            val statusCode: String? = null,
            /** Error */
            val error: Error? = null
        )
        //endregion

        //region poll
        /**
         * Used in: GET /templates/approved
         */
        data class ApprovedTemplateResponse(
            /** Acknowledgement ID */
            val acknowledgementId: String,
            /** Expert Interview Request ID */
            val requestId: Long,
            /** Vendor Project ID */
            val projectId: String,
            /** Status of multiple templates */
            val templateDetails: List<TemplateDetails>
        )
        //endregion

        //region ack
        /**
         * Used in: POST /templates/acknowledgement
         */
        data class AcknowledgementRequest(
            /** Acknowledgement ID for processed message by Vendor */
            val acknowledgementId: String,
            /** Network Provider template Id */
            val networkTemplateId: String

        )

        /**
         * Used in: POST /templates/acknowledgement
         */
        data class AcknowledgementResponse(
            /** Network Provider template Id */
            val networkTemplateId: String? = null,
            /** Success message */
            val message: String? = null,
            /** Http status code for Project ID save request */
            val statusCode: String? = null,
            /** Error */
            val error: String? = null
        )
        //endregion

        //region + nested
        /**
         * Used in: GET /templates/approved
         */
        data class TemplateDetails(
            /** Network Provider template Id */
            val networkTemplateId: String,
            /** Template */
            val template: String,
            /** Designation of the Template Approver */
            val approverDesignation: String,
            /** Timestamp in YYYY-MM-DDTHH:MM:SS format when the template gets approved */
            val approvedTimestamp: String,
            /** Timestamp in YYYY-MM-DDTHH:MM:SS format when the template gets approved */
            val lastUpdatedTimestamp: String? = null
        )
        //endregion
    }


    /**
     * Expert Profile
     */
    interface Profile {
        //region send
        /**
         * Used in: POST /expert-profile
         */
        data class ExpertProfileRequest(
            /** ID of the Expert Interview Request */
            val requestId: Long,
            /** Project ID from Vendor */
            val projectId: String,
            /** Expert profiles from Vendor */
            val expertProfile: List<ExpertProfile>,
            /** Network provider */
            val networkProvider: String? = null
        )

        /**
         * Used in: POST /expert-profile
         */
        data class ExpertProfileResponse(
            /** ID of the Expert Interview Request */
            val requestId: Long? = null,
            /** Project ID from Vendor */
            val projectId: String? = null,
            /** List of valid Profile id from Vendor */
            val profileId: List<String>? = null,
            /** Success message */
            val status: String? = null,
            /** Http status code for Expert profile save request */
            val statusCode: String? = null,
            /** Error */
            val error: String? = null,
            /** Expert profile validation errors before save or update */
            val validationErrors: List<ExpertProfileValidationError>? = null
        )

        //endregion

        //region poll, update status
        /**
         * Used in: POST /expert-profile, PUT /expert-profile/update-availability
         */
        data class ExpertProfilesStatusUpdate(
            /** Acknowledgement ID */
            val acknowledgementId: String?,
            /** ID of the Expert Interview Request */
            val requestId: Long,
            /** Project ID from Vendor */
            val projectId: String,
            /** Status of multiple profiles */
            val profileStatus: List<ProfileStatus>
        )
        //endregion

        //region ack
        /**
         * Used in: POST /expert-profile/acknowledgement
         */
        data class AckExpertProfile(
            /** Acknowledgement ID for processed message by Vendor */
            val acknowledgementId: String,
            /** Expert profile id for declined profile */
            val profileId: String
        )

        /**
         * Used in: POST /expert-profile/acknowledgement
         */
        data class AckExpertProfileResponse(
            /** Expert profile id for declined profile */
            val profileId: String? = null,
            /** Success or failure message */
            val message: String,
            /** Http status code */
            val statusCode: String,
            /** Error */
            val error: String? = null
        )

        //endregion

        //region + nested


        /**
         * Used in: POST /expert-profile
         */
        data class ExpertProfile(
            /** Profile unique identifier */
            val profileId: String,
            /** Name of the expert */
            val expertName: String,
            /** Country code of the expert */
            val countryCode: String,
            /** Relevant experience related to interview request */
            val relevantExperience: RelevantExperience,
            /** Employment details related to current and previous experience */
            val employmentHistory: List<EmploymentHistory>,
            /** Expert rates related information */
            val rate: Rate,
            /** Recommendation from Vendor */
            val recommended: Boolean? = null,
            /** Biography of the expert */
            val biography: String? = null,
            /** Expert Type/Module applicable for expert */
            val expertTypes: List<String>? = null,
            /** Screening questions */
            val screeningQuestions: List<ScreeningQuestion>? = null,
            /** Available slots for expert type whose details have been shared and this is optional */
            val availableSlots: List<ExpertSchedule>? = null
        )

        /**
         * Used in: POST /expert-profile
         */
        data class EmploymentHistory(
            /** Employer Name */
            val employerName: String,
            /** Designation of employee in the company */
            val title: String,
            /** Start month */
            val startMonth: Int,
            /** Start year */
            val startYear: Int,
            /** Whether employment is current or not */
            val currentEmployment: Boolean? = null,
            /** End month */
            val endMonth: Int? = null,
            /** End year */
            val endYear: Int? = null,
            /** Home office location of employee in the company */
            val homeOffice: String? = null
        )

        /**
         * Used in: POST /expert-profile
         */
        data class RelevantExperience(
            /** Employer Name */
            val employerName: String,
            /** Designation of employee in the company */
            val title: String,
            /** Start month */
            val startMonth: Int,
            /** Start year */
            val startYear: Int,
            /** Home office location of employee in the company */
            val homeOffice: String,
            /** Whether employer related to relevant experience is 'Current' or 'Former' */
            val employerStatus: EmployerStatus,
            /** End month */
            val endMonth: Int? = null,
            /** End year */
            val endYear: Int? = null
        ) {
            enum class EmployerStatus(val value: String) {
                Former("Former"),
                Current("Current")
            }
        }

        /**
         * Used in: POST /expert-profile
         */
        data class Rate(
            /** Whether rate shared for expert is 'Premium Rate' or 'Standard Rate' */
            val rateType: String,
            /** Experts rates per hour */
            val costPerHour: Double,
            /** Experts rates per 30 minutes */
            val costPer30Mins: Double,
            /** Rates provided in which currency */
            val currency: String
        ) {
            enum class RateType(val value: String) {
                PREMIUM_RATE("Premium Rate"),
                STANDARD_RATE("Standard Rate")
            }

            @get:JsonIgnore
            val rateTypeEnum: RateType
                get() = RateType.values().first {
                    it.value == this.rateType
                }
        }

        /**
         * Used in: POST /expert-profile
         */
        data class ScreeningQuestion(
            /** Question */
            val question: String,
            /** Answer to respective question */
            val answer: String
        )

        /**
         * Used in: POST /expert-profile
         */
        data class ExpertSchedule(
            /** On the given date starting time when Expert is available and Time should be provided in HH:mm format */
            val startTime: String? = null,
            /** On the given date end time when Expert is available and Time should be provided in HH:mm format */
            val endTime: String? = null,
            /** Available date for Expert */
            val availableDate: String? = null,
            /** For given date and time we have to provide zone id for eg.'Asia/Kolkata' */
            val zoneId: String? = null,
            /** Start date and time when Expert is available and should be provided in UTC format */
            val startDateTime: String? = null,
            /** End date and time when Expert is available and should be provided in UTC format */
            val endDateTime: String? = null
        )

        /**
         * Used in: POST /expert-profile, PUT /expert-profile/update-availability
         */
        data class ProfileStatus(
            /** Profile unique identifier */
            val profileId: String,
            /** Expert Profile Status */
            val status: BcgExpertProfileStatus
        )


        /**
         * Used in: POST /expert-profile
         */
        data class ExpertProfileValidationError(
            /** Expert profile ID which got validation errors */
            val profileId: String? = null,
            /** Validation error messages */
            val errors: List<String>? = null
        )

        //endregion
    }


    interface Call {

        //region send
        /**
         * Used in: POST /call-management
         */
        @JsonInclude(JsonInclude.Include.NON_NULL)
        data class CallDetailsNetworkRequest(
            /** ID of the Expert Interview Request */
            val requestId: Long,
            /** Project ID from Network */
            val projectId: String,
            /** Network sharing current status of call which can be 'Availability','Scheduled','Interviewed' or 'Cancelled' */
            val networkCallStatus: BcgCallStatus,
            /** Sharing reason of cancellation of call for multiple experts */
            var profileCancellations: List<ProfileCancellation>? = null,
            /** Network provider */
            val networkProvider: String = BcgHubConst.VENDOR_CAPVISION,
            /** Sharing slot/slots depending upon call status for multiple experts which are having same call status */
            var profileSlot: List<ProfileSlot>? = null
        )

        /**
         * Used in: POST /call-management
         */
        data class CallDetailsResponse(
            /** ID of the Expert Interview Request */
            val requestId: Long? = null,
            /** Project ID from Network */
            val projectId: String? = null,
            /** Profile unique identifier */
            val profileId: List<String>? = null,
            /** Network sharing current status of call which can be 'Availability','Scheduled','Interviewed' or 'Cancelled' */
            val networkCallStatus: BcgCallStatus,
            /** Id shared by Networks to uniquely identify call for that expert related to particular request */
            val networkCallId: String? = null,
            /** Success message */
            val status: String? = null,
            /** Http status code for calls details shared by Network */
            val statusCode: String? = null,
            /** Error */
            val error: String? = null,
            /** Call details network request validation errors */
            val validationErrors: List<String>? = null
        )

        //endregion

        //region poll
        /**
         * Used in: GET /call-management
         */
        data class CallDetails(
            /** Acknowledgement ID */
            val acknowledgementId: String,
            /** ID of the Expert Interview Request */
            val requestId: Long,
            /** Project ID from Network */
            val projectId: String,
            /** Profile unique identifier */
            val profileId: String,
            /** Requestor of interview request sharing current call status related to partcular expert which can be 'Availability','Scheduled' or 'Disputed' */
            val callStatus: BcgCallStatus,
            /** Team members who all should be invited when call is scheduled between Expert and Requestor. Only in case of Status 'Availability' requestor can sometimes share team member emails. */
            val teamMemberEmails: List<String>? = null,
            /** Id shared by Networks to uniquely identify call for that expert related and it will shared in cases where call is 'Cancelled' or 'Disputed' */
            val networkCallId: String? = null,
            /** Available slots shared by Requestor with Network for that expert profile and this field will be mandatory in case of Status 'Availability' */
            val availableSlots: List<Schedule>? = null,
            /** Reason shared by Requestor of that request  when call has been cancelled/Disputed after call completion */
            val reason: String? = null,
            val isReschedule: Boolean = false
        )
        //endregion

        //region ack
        /**
         * Used in: POST /call-management/acknowledgement
         */
        data class AcknowledgementCallDetails(
            /** Acknowledgement ID for processed message by Vendor */
            val acknowledgementId: String,
            /** Profile unique identifier */
            val profileId: String,
            /** Requestor of interview request sharing current call status related to partcular expert which can be 'Availability','Scheduled' or 'Disputed' */
            val callStatus: BcgCallStatus
        )

        /**
         * Used in: POST /call-management/acknowledgement
         */
        data class AcknowledgementCallDetailsResponse(
            /** Profile unique identifier */
            val profileId: String? = null,
            /** Requestor of interview request sharing current call status related to partcular expert which can be 'Availability','Scheduled' or 'Disputed' */
            val callStatus: BcgCallStatus? = null,
            /** Success or failure message */
            val message: String? = null,
            /** Http status code */
            val statusCode: String? = null,
            /** Error */
            val error: String? = null
        )
        //endregion

        //region + nested
        /**
         * Used in: POST /call-management
         */
        data class ProfileCancellation(
            /** Id shared by Networks to uniquely identify call for that expert related to particular request */
            val networkCallId: String,
            /** Profile unique identifier */
            val profileId: String,
            /** Reason shared by network when call has been cancelled for scheduled call of expert and it will be mandatory in case of Status 'Cancelled' */
            val cancellationReason: String
        )

        /**
         * Used in: POST /call-management
         */
        @JsonInclude(JsonInclude.Include.NON_NULL)
        data class ProfileSlot(
            /** Profile unique identifier */
            val profileId: String,
            /** Available slots shared by Network for that expert profile and these will be mandatory in case of Status 'Availability' */
            val availableSlots: List<Schedule>? = null,
            /** Scheduled Slot shared by network and it will be mandatory in case of Status 'Scheduled' */
            val scheduledSlot: Slot? = null,
            /** Interviewed Slot shared by network and will be mandatory in case of Status 'Interviewed' */
            val interviewedSlot: InterviewedSlot? = null
        )
        //endregion

        //region + nested: slot types
        /**
         * Used in: POST /call-management
         */
        data class Schedule(
            /** Start date and time when Expert is available and should be provided in UTC format */
            val startDateTime: String,
            /** End date and time when Expert is available and should be provided in UTC format */
            val endDateTime: String,
            val duration: Int? = null
        ) {
            @get:JsonIgnore
            val start_time_ist: Instant
                get() = startDateTime.fromMinuteStringForEeh()

            @get:JsonIgnore
            val end_time_ist: Instant
                get() = endDateTime.fromMinuteStringForEeh()
        }

        /**
         * Used in: POST /call-management
         */
        data class Slot(
            /** Id shared by Networks to uniquely identify call for that expert related to particular request */
            val networkCallId: String,
            /** Start date and time when Expert is available and should be provided in UTC format */
            val startDateTime: String,
            /** End date and time when Expert is available and should be provided in UTC format */
            val endDateTime: String,
            val isReschedule: Boolean? = null,
        )

        /**
         * Used in: POST /call-management
         */
        data class InterviewedSlot(
            /** Id shared by Networks to uniquely identify call for that expert related to particular request */
            val networkCallId: String,
            /** Total cost charged for completed call with expert */
            val totalCost: Double,
            /** Start date and time when Expert is available and should be provided in UTC format */
            val startDateTime: String,
            /** End date and time when Expert is available and should be provided in UTC format */
            val endDateTime: String
        )
        //endregion
    }

    //region common: errors & exception
    /**
     * Used in multiple APIs
     */
    data class ServiceException(
        /** Service code representing the error status */
        val errorCode: String? = null,
        val message: String? = null,
        /** Detailed error message */
        val errorDetails: String? = null,
        /** Timestamp of the error occurrence */
        val timestamp: String? = null
    )

    /**
     * Used in multiple APIs
     */
    data class Error(
        val cause: ErrorCause? = null,
        val stackTrace: List<StackTraceElement>? = null,
        val message: String? = null,
        val suppressed: List<Error>? = null,
        val localizedMessage: String? = null
    )

    /**
     * Used in Error class
     */
    data class ErrorCause(
        val stackTrace: List<StackTraceElement>? = null,
        val message: String? = null,
        val localizedMessage: String? = null
    )

    /**
     * Used in Error and ErrorCause classes
     */
    data class StackTraceElement(
        val classLoaderName: String? = null,
        val moduleName: String? = null,
        val moduleVersion: String? = null,
        val methodName: String? = null,
        val fileName: String? = null,
        val lineNumber: Int? = null,
        val nativeMethod: Boolean? = null,
        val className: String? = null
    )

    //endregion

    interface ResearchManager {

        data class ContactDetailsRequest(
            val request_id: String? = null,
            val primaryContact: ContactInfo,
            val otherContacts: List<ContactInfo>,
        )

        data class ContactInfo(
            val email: String,
            val firstName: String,
            val lastName: String,
            val phone: String,
        )

        data class ContactDetailsResponse(
            val status: String? = null,
            val statusCode: String? = null,
            val errorCode: String? = null,
            val errorMessage: String? = null,
            val errorDetails: String? = null,
        )
    }

//endregion
}
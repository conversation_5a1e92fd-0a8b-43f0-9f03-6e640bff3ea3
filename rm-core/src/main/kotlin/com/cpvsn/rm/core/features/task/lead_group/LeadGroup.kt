package com.cpvsn.rm.core.features.task.lead_group

import com.cpvsn.core.base.entity.BaseCompanion
import com.cpvsn.core.base.entity.BaseQuery
import com.cpvsn.core.util.delegates.PropDelegates
import com.cpvsn.crud.orm.annotation.Column
import com.cpvsn.crud.orm.annotation.PatchFields
import com.cpvsn.crud.orm.annotation.Relation
import com.cpvsn.crud.orm.entity.SoftDeletable
import com.cpvsn.crud.query.Criteria
import com.cpvsn.rm.core.base.entity.RmEntity
import com.cpvsn.rm.core.features.email_template.EmailTemplate
import com.cpvsn.rm.core.features.misc.constant.BuiltInCurrency
import com.cpvsn.rm.core.features.project.Project
import com.cpvsn.rm.core.features.task.Task
import com.cpvsn.rm.core.features.user.User
import com.cpvsn.rm.core.features.user.UserResourceOrder
import com.cpvsn.web.auth.AuthContext
import com.fasterxml.jackson.annotation.JsonIgnore
import java.math.BigDecimal
import java.time.Instant

class LeadGroup : RmEntity(), SoftDeletable {

    companion object : BaseCompanion<LeadGroup>()

    @Column
    var project_id: Int = 0

    @Column
    var name: String = ""

    @Column
    var rank: Int = 0

    @Column
    var outreach_template_id: Int? = null

    @Column
    var honorarium_amount: BigDecimal? = null

    @Column
    var honorarium_currency: String? = null

    @get: JsonIgnore
    @PatchFields(["honorarium_currency"])
    var honorarium_currency_enum: BuiltInCurrency? by PropDelegates.enum_nullable(this::honorarium_currency)

    @Relation(reference = "outreach_template_id")
    var outreach_template: EmailTemplate? = null

    @Relation
    var project: Project? = null

    @Relation
    var tasks: List<Task>? = null

    var customize_order: UserResourceOrder? = null

    @Column(insertable = false, updatable = false)
    override var delete_at: Instant? = null

    @Column
    var survey_url: String? = null

    data class Query(
        @Criteria.Eq
        val id: Int? = null,
        @Criteria.IdsIn
        val ids: Set<Int>? = null,
        @Criteria.Eq
        val project_id: Int? = null,
        @Criteria.IdsIn
        val project_ids: Set<Int>? = null,
        @Criteria.Eq
        val name: String? = null,
        @Criteria.Contains
        val name_contains: String? = null,
        @Criteria.In
        val name_in: Set<String>? = null,
        @Criteria.Gt
        val rank_gt: Int? = null,
        @Criteria.Eq
        val rank: Int? = null,
        @Criteria.Eq
        val outreach_template_id: Int? = null,
        @Criteria.Join(on = "{this}.id = {that}.resource_id and {that}.resource_type='LEAD_GROUP' and {that}.user_id = #{current_user_id}")
        val customize_order: UserResourceOrder.Query? = null,
        val current_user_id: Int? = AuthContext.fetch<User>()?.id,
    ) : BaseQuery<LeadGroup>()
} 
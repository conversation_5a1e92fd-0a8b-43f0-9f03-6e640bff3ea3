package com.cpvsn.rm.core.features.task.angle

import com.cpvsn.core.svc.spring.ext.extExecute
import com.cpvsn.core.util.experimental.BeanUtil
import com.cpvsn.core.util.extension.is_valid_id
import com.cpvsn.crud.model.Includes
import com.cpvsn.crud.model.Patch
import com.cpvsn.crud.model.dot
import com.cpvsn.crud.orm.cascade.Cascade
import com.cpvsn.crud.util.ids
import com.cpvsn.rm.core.annotation.RequireEsSync
import com.cpvsn.rm.core.extensions.contains_any
import com.cpvsn.rm.core.features.advisor.Advisor
import com.cpvsn.rm.core.features.advisor.job.AdvisorJob
import com.cpvsn.rm.core.features.bridge.Region
import com.cpvsn.rm.core.features.core.app_config.AppConfigEntry
import com.cpvsn.rm.core.features.core.app_config.AppConfigKey
import com.cpvsn.rm.core.features.inquiry.InquiryBranchService
import com.cpvsn.rm.core.features.inquiry.model.InquiryBranch
import com.cpvsn.rm.core.features.inquiry.model.InquiryQuestion
import com.cpvsn.rm.core.features.misc.company.Company
import com.cpvsn.rm.core.features.outsource.pojo.OutsourceProjectApproval
import com.cpvsn.rm.core.features.project.Project
import com.cpvsn.rm.core.features.project.ProjectService
import com.cpvsn.rm.core.features.search.EsIndex
import com.cpvsn.rm.core.features.task.Task
import com.cpvsn.rm.core.features.task.TaskDisplayIdService
import com.cpvsn.rm.core.features.task.TaskRateService
import com.cpvsn.rm.core.features.task.TaskService
import com.cpvsn.rm.core.features.task.arrange.enums.ConferenceParticipantRole
import com.cpvsn.rm.core.features.task.pojo.FindTasksWithDuplicateAdvisorResponse
import com.cpvsn.rm.core.features.user.UserResourceOrder
import com.cpvsn.rm.core.util.Event
import com.cpvsn.rm.core.util.InvokeUtil
import com.cpvsn.rm.core.util.biz_require
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.transaction.support.TransactionTemplate
import java.math.BigDecimal

@Service
class TaskAngleService {

    private val logger = LoggerFactory.getLogger(this::class.java)

    @Autowired
    private lateinit var transactionTemplate: TransactionTemplate

    // circular reference
    @Autowired
    private lateinit var taskService: TaskService

    @Autowired
    private lateinit var taskRateService: TaskRateService

    @Autowired
    private lateinit var taskDisplayIdService: TaskDisplayIdService

    @Autowired
    private lateinit var userResourceOrderRepo: UserResourceOrder.Repo

    @Autowired
    private lateinit var projectService: ProjectService

    @Autowired
    private lateinit var inquiryBranchService: InquiryBranchService

    fun move_tasks_to_angle(
        task_ids: List<Int>,
        target_angle_id: Int,
        id_position_map: Map<Int, Double>? = null,
    ): List<Task> {
        return transactionTemplate.extExecute {
            val tasks = Task.findAll(Task.Query(ids = task_ids.toSet()))
            val filtered_tasks = tasks.filter { it.angle_id != target_angle_id }
            val target_angle = TaskAngle.get(target_angle_id)

            validate_tasks_by_advisor_duplication(
                target_angle.project_id,
                // we need to change angle_id to target_angle_id before validating duplication
                // so we spawn new instances to do it.
                // another choice would be call it after "taskDisplayIdService.move_tasks_to_angle"
                // that way we check validity after we have done tons of actions,
                // and we have to rollback these actions when duplication is found.
                // the performance would be worse I think.
                filtered_tasks.map {
                    // shallow copy
                    val spawn_instance = BeanUtil.assign(Task(), it)
                    spawn_instance.angle_id = target_angle_id
                    spawn_instance
                }
            )

            // here we mutate task.angle_id to target_angle_id
            taskDisplayIdService.move_tasks_to_angle(
                filtered_tasks,
                target_angle,
                id_position_map
            )
            taskRateService.reevaluate_rate(
                target_angle.project_id,
                filtered_tasks.ids()
            )
        }
    }

    fun validate_tasks_by_advisor_duplication(
        project_id: Int,
        tasks: List<Task>,
    ) {
        val check_dup_res = find_tasks_with_duplicate_advisor(
            project_id,
            tasks,
        )
        val duplicate_in_angle = check_dup_res.duplicate_in_angle
        val duplicate_in_project = check_dup_res.duplicate_in_project
        biz_require(duplicate_in_angle.isEmpty()) {
            val task = duplicate_in_angle.first()
            "Advisor(with id = ${task.advisor_id}) already exist in the Angle(with id = ${task.angle_id})!"
        }
        biz_require(duplicate_in_project.isEmpty()) {
            val task = duplicate_in_project.first()
            "Advisor(with id = ${task.advisor_id}) already exist in the Project(with id = ${task.project_id})!"
        }
    }

    /**
     * This method should not mutate its arguments.
     */
    fun find_tasks_with_duplicate_advisor(
        project_id: Int,
        tasks: List<Task>,
    ): FindTasksWithDuplicateAdvisorResponse {
        val project = Project.get(
            project_id, Includes.setOf(
                Project::investment_target_companies dot Company::aliases,
                Project::outsource_approvals,
            )
        )
        val outsource_statistic_advisor_ids = AppConfigEntry.firstOrNull(
            AppConfigEntry.Query(
                key_enum = AppConfigKey.OUTSOURCE_STATISTIC_ADVISOR_IDS,
            )
        )?.value?.split(",")?.mapNotNull { it.toIntOrNull() }.orEmpty().toSet()
        val outsource_limit_advisor = Advisor.firstOrNull(
            query = Advisor.Query(
                ids = outsource_statistic_advisor_ids,
            ),
        )?.let { outsource_survey_statistic_advisor ->
            val contains_this_advisor_tasks = tasks.filter { it.advisor_id == outsource_survey_statistic_advisor.id }
            if (contains_this_advisor_tasks.isEmpty()) {
                return@let emptyList<Task>()
            }
            val latest_outsource_approval =
                project.outsource_approvals?.sortedByDescending { it.create_at }?.firstOrNull()
            if (!(latest_outsource_approval?.outsource_approval == OutsourceProjectApproval.ApprovalStatus.APPROVED &&
                        latest_outsource_approval.local_approval == OutsourceProjectApproval.ApprovalStatus.APPROVED &&
                        project.sub_type == Project.SubType.Survey)
            ) {
                contains_this_advisor_tasks

            } else {
                emptyList()
            }
        }.orEmpty()
        val without_advisor = tasks.filter { !it.is_with_advisor }
        // duplicate advisors among completed tasks is allowed according to requirement.
        val completed = tasks.filter { it.has_completed }

        val not_follow_up = mutableListOf<Task>()
        val follow_up = mutableListOf<Task>()
        val duplicate_in_angle = mutableListOf<Task>()
        val duplicate_in_project = mutableListOf<Task>()

        // with advisor && has not completed
        val to_be_validated = tasks.filter {
            it.is_with_advisor && !it.has_completed
        }

        // locate duplication in "tasks" parameter
        // not a robust way, depends on Object's equals/hashcode.
        val filtered_advisor_tasks = to_be_validated
            .distinctBy { it.angle_id to it.advisor_id }
        val valid_tasks_set = filtered_advisor_tasks.toSet()
        val duplicate_advisor_tasks = to_be_validated.filter {
            it !in valid_tasks_set
        }
        duplicate_advisor_tasks.forEach {
            if (it.angle_id.is_valid_id) {
                duplicate_in_angle.add(it)
            } else {
                duplicate_in_project.add(it)
            }
        }
        // end

        val advisor_ids = filtered_advisor_tasks.ids(Task::advisor_id)
        val advisor_id_task_map = Task.findAll(
            Task.Query(
                project_id = project_id,
                advisor_ids = advisor_ids,
                id_not_in = tasks.ids()
            )
        ).groupBy { it.advisor_id }

        filtered_advisor_tasks.forEach { task ->
            val existed_tasks = advisor_id_task_map[task.advisor_id].orEmpty()
            val existed_in_project = existed_tasks.isNotEmpty()
            val existed_uncompleted_in_angle = task.angle_id.is_valid_id
                    && existed_tasks.any {
                it.angle_id == task.angle_id
                        && !it.has_completed
            }

            if (task.angle_id.is_valid_id) {
                // will be added to client task tab
                when {
                    existed_uncompleted_in_angle -> {
                        duplicate_in_angle.add(task)
                    }

                    existed_in_project -> {
                        follow_up.add(task)
                    }

                    else -> {
                        // does not exist in project
                        not_follow_up.add(task)
                    }
                }
            } else {
                // will be added to leads tab
                // currently, duplicated advisor in leads tab is not allowed
                if (existed_in_project) {
                    duplicate_in_project.add(task)
                } else {
                    // does not exist in project
                    not_follow_up.add(task)
                }
            }
        }

        // advisors restriction
        Task.join(tasks, Includes.setOf(Task::advisor dot Advisor::jobs dot AdvisorJob::company))
        val empty_employment_history_advisor = tasks.filter { it.is_with_advisor && it.advisor?.jobs.isNullOrEmpty() }


        val conflict_advisors = projectService.validate_investment_targets(project, tasks.mapNotNull { it.advisor })
        val employees_conflict_investment_target = tasks.filter { task ->
            task.advisor_id in conflict_advisors.map { it.id }.toSet()
        }

        val with_not_current_region_advisor = tasks.filter { task ->
            task.advisor?.origin_enum != null && task.advisor?.origin_enum != Region.current
        }
        // end

        return FindTasksWithDuplicateAdvisorResponse(
            not_follow_up,
            follow_up,
            duplicate_in_angle,
            duplicate_in_project,
            without_advisor = without_advisor,
            completed = completed,
            empty_employment_history_advisor = empty_employment_history_advisor,
            employees_conflict_investment_target = employees_conflict_investment_target,
            with_not_current_region_advisor = with_not_current_region_advisor,
            outsource_limit_advisor = outsource_limit_advisor,
        )
    }

    @RequireEsSync(es_index = EsIndex.PROJECT, id_field = "project_id")
    @Transactional
    fun save_cascade(entity: TaskAngle): TaskAngle {
        handle_require_npi_number_question(entity)
        return TaskAngle.save(
            entity, cascades = setOf(
                Cascade.Save.oneToMany(TaskAngle::members),
            )
        )
    }

    @Transactional
    fun patch_cascade(patch: Patch<TaskAngle>, include: Set<String>): TaskAngle {
        val res = TaskAngle.patchThenGet(
            patch, cascades = setOf(
                Cascade.oneToMany(
                    TaskAngle::members,
                    actions = Cascade.Action.SAVE_DELETE
                )
            ), include = include
        )

        if (patch.fields.contains_any(
                TaskAngle::rate.name,
                TaskAngle::rate_currency.name,
            )
        ) {
            post_update_rate(
                patch.entity.id,
                patch.entity.rate,
                patch.entity.rate_currency,
            )
        }

        if (patch.fields.contains_any(
                TaskAngle::inquiry_branch_id.name,
                TaskAngle::require_npi_number_question.name
            )
        ) {
            handle_require_npi_number_question(res)
        }

        InvokeUtil.trigger(Event.PROJECT_DOC_CHANGED(project_id = res.project_id))
        return res
    }

    private fun post_update_rate(
        angle_id: Int,
        rate: BigDecimal?,
        rate_currency: String?,
    ): TaskAngle {
        // either all null or none null
        biz_require((rate == null) == (rate_currency == null)) {
            "invalid 'rate' and 'rate_currency' value"
        }
        val angle = TaskAngle.get(angle_id)
        taskRateService.reevaluate_rate(
            project_id = angle.project_id,
            angle_id = angle.id
        )
        return angle
    }

    @Transactional
    fun delete_cascade(id: Int) {
        transactionTemplate.execute {
            val angle = TaskAngle.get(id, include = Includes.setOf(TaskAngle::tasks))
            // cascade remove tasks
            taskService.remove_batch(
                angle.tasks.orEmpty().ids(),
                reorder_affected_angles = false
            )
            TaskAngle.delete(id)
        }
    }

    fun user_customize_sort(
        project_id: Int,
        id_position_map: Map<Int, Double>,
        user_id: Int,
    ): List<TaskAngle> {
        return transactionTemplate.extExecute { _ ->
            val project = Project.get(
                project_id, Includes.setOf(
                    Project::angles dot TaskAngle::customize_order
                )
            )
            val angles = project.angles.orEmpty()

            val orders = angles.sortedBy {
                id_position_map[it.id]
                    ?: it.customize_order?.display_order?.toDouble()
                    ?: it.rank.toDouble()
            }.mapIndexed { index, angle ->
                UserResourceOrder {
                    this.user_id = user_id
                    this.resource_id = angle.id
                    this.resource_type = UserResourceOrder.Type.TASK_ANGLE
                    this.display_order = index + 1
                }
            }

            userResourceOrderRepo.batchSave(orders)

            TaskAngle.join(angles, Includes.setOf(TaskAngle::customize_order))
        }
    }

    fun handle_client_emails_change(task_angle: TaskAngle) {
        val original_task_angle = TaskAngle.find(task_angle.id)
        val original_angle_client_emails = original_task_angle?.calendar_invite_to_email_list.orEmpty().toSet()
        val new_angle_client_emails = task_angle.calendar_invite_to_email_list.orEmpty().toSet()
        val deleted_emails = original_angle_client_emails.filter { it !in new_angle_client_emails }.toSet()
        val tasks = Task.findAll(
            query = Task.Query(
                angle_id = task_angle.id
            ),
            include = Includes.setOf(Task::arrangement)
        )
        tasks.mapNotNull { it.arrangement }.forEach { arrangement ->
            val new_to_list = arrangement.mail_info?.get(ConferenceParticipantRole.CLIENT)?.to_list.orEmpty().filter {
                it !in deleted_emails
            }
            Patch.fromMutator(arrangement) {
                arrangement.mail_info?.get(ConferenceParticipantRole.CLIENT)?.let {
                    val new_mail_info = arrangement.mail_info?.toMutableMap()
                    new_mail_info?.set(
                        ConferenceParticipantRole.CLIENT,
                        it.copy(to_list = new_to_list)
                    )
                    this.mail_info = new_mail_info
                }
            }.patch()
        }
    }

    fun add_npi_question_to_question_list(
        questions: List<InquiryQuestion>
    ): List<InquiryQuestion> {
        val updated_questions = questions.toMutableList()
        updated_questions.add(
            InquiryQuestion.create_npi_confirm_question().apply {
                // add npi question to the end of list
                this.sort_order = questions.size
                this.display_order = questions.size + 1
            }
        )
        return updated_questions
    }

    // if TaskAngle::require_npi_number_question is true, add a NPI question if the question branch doesn't already have one
    fun handle_require_npi_number_question(
        angle: TaskAngle
    ) {
        if (angle.require_npi_number_question && angle.inquiry_branch_id.is_valid_id) {
            InquiryBranch.firstOrNull(
                InquiryBranch.Query(id = angle.inquiry_branch_id),
                sort = null,
                Includes.setOf(InquiryBranch::questions)
            )?.let { branch ->
                if (!branch.has_npi_question && branch.questions != null) {
                    val patch = Patch.of(
                        InquiryBranch {
                            this.id = branch.id
                            this.questions = add_npi_question_to_question_list(branch.questions!!)
                        },
                        fields = setOf(InquiryBranch::questions.name)
                    )
                    inquiryBranchService.patch_cascade(patch, patch.fields)
                }
            }
        }
    }
}

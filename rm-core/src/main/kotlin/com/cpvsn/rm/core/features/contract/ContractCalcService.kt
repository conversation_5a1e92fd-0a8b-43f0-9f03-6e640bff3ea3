package com.cpvsn.rm.core.features.contract

import com.cpvsn.core.util.extension.BD
import com.cpvsn.core.util.extension.assert_exist
import com.cpvsn.rm.core.extensions.assert_exist
import com.cpvsn.rm.core.extensions.safe_change_precision_to_2digits
import com.cpvsn.rm.core.extensions.safe_change_precision_to_5digits
import com.cpvsn.rm.core.features.advisor.Advisor
import com.cpvsn.rm.core.features.advisor.AdvisorService
import com.cpvsn.rm.core.features.contract.entity.Contract
import com.cpvsn.rm.core.features.email.EmailService
import com.cpvsn.rm.core.features.email_template.PlaceholderBasedEmailTemplateService
import com.cpvsn.rm.core.features.finance.revenue.RevenueService
import com.cpvsn.rm.core.features.misc.constant.BuiltInCurrency
import com.cpvsn.rm.core.features.task.Task
import com.cpvsn.rm.core.features.task.constant.TaskType
import com.cpvsn.rm.core.util.CurrencyUtil
import com.cpvsn.rm.core.util.biz_check
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.transaction.support.TransactionTemplate
import java.math.BigDecimal
import java.math.RoundingMode

@Service
class ContractCalcService {

    //region @
    private val log = LoggerFactory.getLogger(this.javaClass)

    @Autowired
    private lateinit var revenueService: RevenueService

    @Autowired
    private lateinit var emailService: EmailService

    @Autowired
    private lateinit var advisorService: AdvisorService

    @Autowired
    private lateinit var placeholderBasedEmailTemplateService: PlaceholderBasedEmailTemplateService

    @Autowired
    private lateinit var transactionTemplate: TransactionTemplate
    //endregion

    //region multiplier 加收倍数
    /**
     * judge is_oversea by advisor location
     */
    private fun calc_oversea_rate_multiplier(
        advisor: Advisor,
        contract: Contract,
    ): BigDecimal {
        return if (advisorService.is_cn_advisor_by_location(advisor.id)) {
            1.BD
        } else {
            contract.overseas_term?.rate ?: 1.BD
        }
    }

    fun calc_senior_rate_multiplier(
        rate: BigDecimal,
        rate_currency: BuiltInCurrency,
        contract: Contract,
    ): BigDecimal? {
        return if (rate_currency == BuiltInCurrency.RMB) {
            contract.china_senior_term?.evaluate(rate)
        } else {
            val rate_in_USD = CurrencyUtil.to_usd_approximate(rate, rate_currency)
            contract.non_china_senior_term?.evaluate(rate_in_USD)
        }
    }

    /**
     * advisor only. else return null
     */
    fun calc_senior_rate_multiplier(
        advisor: Advisor,
        contract: Contract,
    ): BigDecimal? {
        if (advisor.rate == null || advisor.rate_currency == null) return null
        return calc_senior_rate_multiplier(
            advisor.rate!!.toBigDecimal(),
            advisor.rate_currency!!,
            contract
        )
    }

    /**
     * 反过来，若指定了task.client_rate_usd(不是default)，则根据这个基于合同单价倒推multiplier
     */
    fun calc_given_senior_rate_multiplier(
        client_rate_usd: BigDecimal,
        is_cn_advisor: Boolean,
        contract: Contract,
    ): BigDecimal? {
        val unit_price = if (is_cn_advisor) {
            contract.unit_price_china
        } else {
            contract.unit_price_us
        } ?: return null
        return client_rate_usd.divide(unit_price, 5, RoundingMode.HALF_UP).safe_change_precision_to_5digits()
    }
    //endregion

    fun evaluate_client_rate_usd(
        rate: BigDecimal,
        rate_currency: BuiltInCurrency,
        is_cn_advisor: Boolean,
        contract: Contract,
    ): BigDecimal? {
        val senior_rate_multiplier = calc_senior_rate_multiplier(rate, rate_currency, contract)
            ?: return null
        val unit_price = if (is_cn_advisor) {
            contract.unit_price_china
        } else {
            contract.unit_price_us
        } ?: return null
        return senior_rate_multiplier.multiply(unit_price).safe_change_precision_to_2digits()
    }

    /**
     * @param assume_client_hours for assumption (when task not completed, sometimes want preview billable hours)
     */
    fun evaluate_billing_hours_using_contract(
        task_id: Int,
        contract: Contract,
        assume_client_hours: BigDecimal? = null,
        task: Task? = null,
    ): BigDecimal {
        val task = task ?: Task.get(task_id).assert_exist(task_id)
        biz_check(task.type == TaskType.CONSULTATION) {
            "Cannot caculate billable hours for non consultation task"
        }
        if (task.not_charged == true) {
            return 0.BD
        }
        val suffix = "hasn't been clarified in client contract:${contract.id}"
        val advisor = Advisor.get(task.advisor_id!!)

        val client_hours = assume_client_hours ?: task.client_hours ?: 0.BD
        val discount_hours = task.discount_hours ?: 0.BD
        val senior_rate = when {
            task.senior_rate != null -> task.senior_rate!!
            advisor.rate != null -> calc_senior_rate_multiplier(advisor, contract)!!
            else -> 1.BD
        }

        val overseas_rate = when (task.has_overseas) {
            false -> 1.BD
            true -> contract.overseas_term?.rate ?: 1.BD
            else -> calc_oversea_rate_multiplier(advisor, contract)
        }
        val translation_extra = if (task.has_translation == true) {
            contract.translation_term.assert_exist("translation_term $suffix").extra
        } else {
            0.BD
        }
        val transcription_extra = if (task.has_transcription == true) {
            contract.transcription_term.assert_exist("transcription_term $suffix").extra
        } else {
            0.BD
        }
        val in_person = if (task.has_in_person == true) {
            contract.in_person_term.assert_exist("in_person_term $suffix").value
        } else {
            0.BD
        }

        return client_hours * (senior_rate * overseas_rate + translation_extra + transcription_extra) + in_person - discount_hours
    }
    //endregion
}
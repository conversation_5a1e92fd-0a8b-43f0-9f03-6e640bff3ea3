package com.cpvsn.rm.core.features.thirdparty.advisor_information.rocket_reach


interface SearchPeople {

    data class Request(
        val query: Query,
    )

    data class Query(
        val link: List<String>,
    )

    data class Response(
        val profiles: List<Profile>? = null,
    )

    data class Profile(
        val id: Int? = null,
        val status: String? = null,
        val linkedin_url: String? = null,
        val teaser: Teaser? = null,
    )

    data class Teaser(
        val emails: List<String>? = null,
        val phones: List<Phone>? = null,
        val personal_emails: List<String>? = null,
        val professional_emails: List<String>? = null,
        val personal_phones: List<Phone>? = null,
    )

    data class Phone(
        val number: String,
        val is_premium: Boolean,
    )
}
package com.cpvsn.rm.core.features.thirdparty.deepgram.response


data class DeepgramResponse(
    val metadata: Metadata,
    val results: Results
) {
    data class Metadata(
        val channels: Double,
        val request_id: String,
        val duration: Double,
        val sha256: String,
        val created: String,
        val models: List<String>,
        val tags: List<String>? = null
    )

    data class Results(
        val channels: List<Channel>,
        val utterances: List<Utterance>? = null,
        val summary: Summary? = null,
        val topics: Topics? = null,
        val intents: Intents? = null,
        val sentiments: Sentiments? = null
    )

    data class Channel(
        val alternatives: List<Alternative>? = null,
        val detected_language: String? = null,
        val search: List<Search>? = null
    )

    data class Alternative(
        val confidence: Double? = null,
        val paragraphs: Paragraphs? = null,
        val transcript: String? = null,
        val words: List<Word>? = null,
        val summaries: List<SummaryItem>? = null,
        val topics: List<TopicItem>? = null
    )

    data class Paragraphs(
        val paragraphs: List<Paragraph>? = null,
        val transcript: String? = null
    )

    data class Word(
        val confidence: Double? = null,
        val end: Double? = null,
        val punctuated_word: String? = null,
        val start: Double? = null,
        val word: String? = null
    )

    data class Paragraph(
        val end: Double? = null,
        val num_words: Int? = null,
        val sentences: List<Sentence>? = null,
        val start: Double? = null
    )

    data class Sentence(
        val end: Double? = null,
        val start: Double? = null,
        val text: String? = null
    )

    data class Search(
        val query: String? = null,
        val hits: List<Hit>? = null
    )

    data class Hit(
        val confidence: Double? = null,
        val start: Double? = null,
        val end: Double? = null,
        val snippet: String? = null
    )

    data class Summary(
        val result: String? = null,
        val short: String? = null
    )

    data class Topics(
        val results: TopicResults? = null
    )

    data class TopicResults(
        val topics: TopicSegments? = null
    )

    data class TopicSegments(
        val segments: List<TopicSegment>? = null
    )

    data class TopicSegment(
        val text: String? = null,
        val start_word: Int? = null,
        val end_word: Int? = null,
        val topics: List<Topic>? = null
    )

    data class Topic(
        val topic: String? = null,
        val confidence_score: Double? = null
    )

    data class Intents(
        val results: IntentResults? = null
    )

    data class IntentResults(
        val intents: IntentSegments? = null
    )

    data class IntentSegments(
        val segments: List<IntentSegment>? = null
    )

    data class IntentSegment(
        val text: String? = null,
        val start_word: Int? = null,
        val end_word: Int? = null,
        val intents: List<Intent>? = null
    )

    data class Intent(
        val intent: String? = null,
        val confidence_score: Double? = null
    )

    data class Sentiments(
        val segments: List<SentimentSegment>? = null,
        val average: SentimentAverage? = null
    )

    data class SentimentSegment(
        val text: String? = null,
        val start_word: Int? = null,
        val end_word: Int? = null,
        val sentiment: String? = null,
        val sentiment_score: Double? = null
    )

    data class SentimentAverage(
        val sentiment: String? = null,
        val sentiment_score: Double? = null
    )

    data class Utterance(
        val start: Double? = null,
        val end: Double? = null,
        val confidence: Double? = null,
        val channel: Int? = null,
        val transcript: String? = null,
        val words: List<WordItem>? = null,
        val speaker: Int? = null,
        val id: String? = null
    )

    data class WordItem(
        val word: String? = null,
        val start: Double? = null,
        val end: Double? = null,
        val confidence: Double? = null,
        val speaker: Int? = null,
        val speaker_confidence: Int? = null,
        val punctuated_word: String? = null
    )

    data class SummaryItem(
        val summary: String? = null,
        val start_word: Int? = null,
        val end_word: Int? = null
    )

    data class TopicItem(
        val text: String? = null,
        val start_word: Int? = null,
        val end_word: Int? = null,
        val topics: List<String>? = null
    )
}
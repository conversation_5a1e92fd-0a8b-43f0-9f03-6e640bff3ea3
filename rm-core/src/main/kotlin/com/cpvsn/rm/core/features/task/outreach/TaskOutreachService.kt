package com.cpvsn.rm.core.features.task.outreach

import com.cpvsn.core.model.BusinessException
import com.cpvsn.core.model.email.EmailRequest
import com.cpvsn.core.svc.spring.ext.extExecute
import com.cpvsn.core.util.RegExps
import com.cpvsn.core.util.extension.assert_not_null
import com.cpvsn.core.util.extension.assert_valid_id
import com.cpvsn.core.util.extension.biz_error
import com.cpvsn.crud.model.Includes
import com.cpvsn.crud.model.Patch
import com.cpvsn.crud.model.dot
import com.cpvsn.crud.spring.model.JacksonPatch
import com.cpvsn.crud.util.ids
import com.cpvsn.rm.core.extensions.assert_exist
import com.cpvsn.rm.core.features.advisor.Advisor
import com.cpvsn.rm.core.features.advisor.AdvisorCreationService
import com.cpvsn.rm.core.features.advisor.AdvisorService
import com.cpvsn.rm.core.features.advisor.job.AdvisorJob
import com.cpvsn.rm.core.features.compliance.tc.Tc
import com.cpvsn.rm.core.features.core.app_config.AppConfigEntry
import com.cpvsn.rm.core.features.core.app_config.AppConfigKey
import com.cpvsn.rm.core.features.email.EmailRecord
import com.cpvsn.rm.core.features.email.EmailRecordTracking
import com.cpvsn.rm.core.features.email.pojo.EmailRequestPojo
import com.cpvsn.rm.core.features.email.validation.EmailValidationRecord
import com.cpvsn.rm.core.features.email.validation.EmailValidationRecordService
import com.cpvsn.rm.core.features.email_template.EmailAddressModel.Companion.to_email_address_model
import com.cpvsn.rm.core.features.email_template.EmailTemplate
import com.cpvsn.rm.core.features.email_template.EmailTemplateResult
import com.cpvsn.rm.core.features.email_template.PlaceholderBasedEmailTemplateService
import com.cpvsn.rm.core.features.email_template.constant.EmailContentType
import com.cpvsn.rm.core.features.email_template.constant.EmailTemplateBuiltInTag
import com.cpvsn.rm.core.features.email_template.models.PlaceholderBasedModelIds
import com.cpvsn.rm.core.features.misc.communication.CommunicationRecord
import com.cpvsn.rm.core.features.misc.communication.CommunicationRecordService
import com.cpvsn.rm.core.features.misc.linkedin.AdvisorLinkedinLastRecord
import com.cpvsn.rm.core.features.portal.common.Portal
import com.cpvsn.rm.core.features.portal.common.payload.AdvisorUnsubscribeOutreachPayload
import com.cpvsn.rm.core.features.project.Project
import com.cpvsn.rm.core.features.task.Task
import com.cpvsn.rm.core.features.task.TaskCreationService
import com.cpvsn.rm.core.features.task.constant.TaskCreationSource
import com.cpvsn.rm.core.features.task.constant.TaskStatus
import com.cpvsn.rm.core.features.task.constant.TaskWillingnessType
import com.cpvsn.rm.core.features.task.decline.TaskAdvisorDeclineService
import com.cpvsn.rm.core.features.task.event.TaskEvent
import com.cpvsn.rm.core.features.task.event.TaskEventService
import com.cpvsn.rm.core.features.task.event.TaskEventType
import com.cpvsn.rm.core.features.task.outreach.ipwarmup.OutreachIPWarmUpService
import com.cpvsn.rm.core.features.task.pojo.TaskCreateRequest
import com.cpvsn.rm.core.features.thirdparty.bcg.entity.BcgHubEmailTemplate
import com.cpvsn.rm.core.features.user.User
import com.cpvsn.rm.core.util.biz_require
import com.cpvsn.rm.core.util.biz_required_not_null
import com.cpvsn.rm.core.util.global_executor
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.scheduling.annotation.Async
import org.springframework.scheduling.annotation.AsyncResult
import org.springframework.stereotype.Service
import org.springframework.transaction.support.TransactionTemplate
import java.util.concurrent.Future
import kotlin.random.Random

@Service
class TaskOutreachService {

    companion object {
        val DEFAULT_EMAIL_REGION_REGEX =
            "@(?:microsoft|microsoftonline|office|office365|hotmail|outlook|live|msn|passport|windowslive)\\.[a-z.]+"
    }

    private val logger = LoggerFactory.getLogger(this::class.java)

    @Autowired
    private lateinit var transactionTemplate: TransactionTemplate

    @Autowired
    private lateinit var taskEventService: TaskEventService

    @Autowired
    private lateinit var communicationRecordService: CommunicationRecordService

    @Autowired
    private lateinit var placeholderBasedEmailTemplateService: PlaceholderBasedEmailTemplateService

    @Autowired
    private lateinit var taskOutreachUnsubscribeService: TaskOutreachUnsubscribeService

    @Autowired
    private lateinit var taskSurveyOutreachService: TaskSurveyOutreachService

    @Autowired
    private lateinit var taskAdvisorDeclineService: TaskAdvisorDeclineService

    @Autowired
    private lateinit var outreachIPWarmUpService: OutreachIPWarmUpService

    @Autowired
    private lateinit var taskCreationService: TaskCreationService

    @Autowired
    private lateinit var advisorCreationService: AdvisorCreationService

    @Autowired
    private lateinit var advisorService: AdvisorService

    @Autowired
    private lateinit var emailValidationRecordService: EmailValidationRecordService

    private val random = Random(System.currentTimeMillis())

    fun check_outreach_all_Request(
        request: OutreachAllRequest,
        user_id: Int,
    ): CheckOutreachResponse {
        val project = Project.get(request.project_id.assert_valid_id())
        val tasks = Task.findAll(
            Task.Query(
                project_id = request.project_id.assert_valid_id(),
                angle_id = request.angle_id,
                angle_id_is_null = request.only_leads,
                lead_group_id = request.lead_group_id,
            ), Includes.setOf(
                Task::advisor dot Advisor::email_address_score,
                Task::advisor dot Advisor::email_validation_records,
                Task::advisor dot Advisor::jobs dot AdvisorJob::legal_validate_result,
                Task::advisor_decline,
            )
        )

        val (valid_status, invalid_status) = tasks.partition {
            it.general_status == TaskStatus.General.INITIAL
                    && it.ca_status == TaskStatus.CA.INITIAL
                    && it.sq_status == TaskStatus.SQ.INITIAL
                    && it.advisor_schedule_status == TaskStatus.AdvisorSchedule.INITIAL
        }

        val employees_conflict_investment_target = valid_status.filter { task ->
            task.advisor?.jobs?.filter { it.is_current }
                ?.any { it.company_id in project.investment_target_company_ids } == true
        }
        val have_dnc_experiences = valid_status.filter { task ->
            task.advisor?.jobs.orEmpty()
                .any { it.legal_validate_result?.valid == false }
        }
        val have_dnc_experiences_warning = valid_status.filter { task ->
            task.advisor?.jobs.orEmpty()
                .any { it.legal_validate_result?.warn_companies?.isNotEmpty() == true }
        }
        val in_blacklist_status = valid_status.filter { task ->
            task.advisor?.status_enum == Advisor.Status.BLACKLIST
        }
        val advisor_declined = valid_status.filter { task ->
            task.advisor_decline?.is_valid == true
        }
        val have_invalid_email_address = valid_status.filter { task ->
            !RegExps.EMAIL.matches(task.advisor?.email.orEmpty())
        }
        val list_wise_validation_failed = valid_status.filter { task ->
            val latest_validation_record = task.advisor
                ?.email_validation_records
                .orEmpty()
                .filter { it.provider == EmailValidationRecord.Provider.LIST_WISE }
                .maxByOrNull { it.id }
            latest_validation_record?.list_wise_email_status?.invalid == true
        }
        val email_address_score_too_low = valid_status.filter {
            it.advisor?.email_address_score?.invalid == true
        }
        val email_with_sensitive_domain = valid_status.filter {
            it.advisor?.email?.substringAfterLast('@')?.split('.')?.any { splited_domain ->
                splited_domain == "gov" || splited_domain == "go"
            } ?: false
        }

        val valid = valid_status.asSequence()
            .minus(employees_conflict_investment_target)
            .minus(have_dnc_experiences)
            .minus(in_blacklist_status)
            .minus(have_invalid_email_address)
            .minus(advisor_declined)
            .minus(list_wise_validation_failed)
            .minus(email_address_score_too_low)
            .minus(email_with_sensitive_domain)
            .toList()

        val email_preview = valid.firstOrNull()?.let {
            get_outreach_emails(
                email_template_id = request.email_template_id,
                tasks = listOf(it),
                user_id = user_id,
                represent_user_id = request.represent_user_id,
            )
        }

        return CheckOutreachResponse(
            total = tasks.size,
            valid_tasks = valid,
            incorrect_status_tasks = invalid_status.map {
                CheckOutreachResponse.simplify_task(
                    it,
                    set_jobs = false
                )
            },
            advisors_have_invalid_email_address = have_invalid_email_address.map {
                CheckOutreachResponse.simplify_task(
                    it,
                    set_jobs = false
                )
            },
            email_address_score_too_low = email_address_score_too_low.map {
                CheckOutreachResponse.simplify_task(
                    it,
                    set_jobs = false
                )
            },
            email_with_sensitive_domain = email_with_sensitive_domain.map {
                CheckOutreachResponse.simplify_task(
                    it,
                    set_jobs = false
                )
            },
            list_wise_validation_failed = list_wise_validation_failed.map {
                CheckOutreachResponse.simplify_task(
                    it,
                    set_jobs = false,
                )
            },
            advisors_have_dnc_experiences = have_dnc_experiences.map {
                CheckOutreachResponse.simplify_task(
                    it,
                    set_jobs = true
                )
            },
            employees_conflict_investment_target = employees_conflict_investment_target.map {
                CheckOutreachResponse.simplify_task(
                    it,
                    set_jobs = true
                )
            },
            advisors_have_dnc_experiences_warning = have_dnc_experiences_warning.map {
                CheckOutreachResponse.simplify_task(
                    it,
                    set_jobs = true
                )
            },
            advisors_in_blacklist_status = in_blacklist_status.map {
                CheckOutreachResponse.simplify_task(
                    it,
                    set_jobs = false
                )
            },
            advisors_declined = advisor_declined.map {
                CheckOutreachResponse.simplify_task(
                    it,
                    set_jobs = false
                )
            },
            email_preview = email_preview?.first(),
        )
    }

    @Async
    fun outreach_all_async(
        request: OutreachAllRequest,
        user_id: Int,
    ): Future<List<Task>> {
        // currently @Async method ignore all uncaught exceptions
        // so we have to handle exceptions here
        val res = try {
            outreach_all(request, user_id)
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
        return AsyncResult(res)
    }

    fun outreach_all(
        request: OutreachAllRequest,
        user_id: Int,
    ): List<Task> {
        val response = check_outreach_all_Request(request, user_id)
        val tasks = response.valid_tasks
        val project = Project.get(
            request.project_id,
            Includes.setOf(Project::bcg_hub_project),
        )
        val emails = if (project.bcg_hub_project != null) {
            // this is a bcg eeh requested project, we need to select an approved template
            val approved_template = request.bcg_template_id?.let {
                BcgHubEmailTemplate.get(it)
            } ?: BcgHubEmailTemplate.findAll(
                query = BcgHubEmailTemplate.Query(
                    status = BcgHubEmailTemplate.Status.APPROVED,
                    project_id = project.id
                )
            ).sortedByDescending { it.create_at }.firstOrNull() ?: biz_error("approved bcg template not found")
            get_bcg_outreach_emails(
                bcg_template = approved_template,
                tasks = tasks,
                user_id = user_id,
                represent_user_id = request.represent_user_id
            )
        } else {
            get_outreach_emails(
                request.email_template_id,
                tasks,
                user_id,
                request.represent_user_id,
            )
        }

        val batch = tasks.mapIndexed { index, task ->
            val email_request =
                EmailRequestPojo.from_template_result(emails[index])

            // Only emails to leads from the leads tab should be going through Postmark.
            // Please also confirm that all emails that are sent from Client Tasks are routed through Sendgrid.
            // I Just wanted to triple confirm.   -- Chris
            //
            // the leads tab may contain advisors, I'm told to
            // send these emails via SEND_GRID.
            email_request.prefer_vendor =
                if (task.advisor?.type == Advisor.Type.LEAD) {
                    val select_via_region = AppConfigEntry.firstOrNull(
                        query = AppConfigEntry.Query(
                            key_enum = AppConfigKey.SELECT_TO_LEADS_EMAIL_VENDOR_VIA_REGION,
                        )
                    )
                    val regex = AppConfigEntry.firstOrNull(
                        query = AppConfigEntry.Query(
                            key_enum = AppConfigKey.MAILJET_EMAIL_REGION,
                        )
                    )?.let {
                        val regions = it.value?.split(",").takeUnless { it.isNullOrEmpty() } ?: return@let null
                        "@(?:${regions.joinToString("|")})\\.[a-z.]+"
                    } ?: DEFAULT_EMAIL_REGION_REGEX
                    if (select_via_region?.value?.toLowerCase()?.trim() == "true") {
                        val email_regex = Regex(regex)
                        var contains_match = false
                        email_request.to_list.forEach {
                            if (email_regex.containsMatchIn(it)) {
                                contains_match = true
                            }
                        }
                        if (contains_match) {
                            EmailRecordTracking.Provider.MAILJET.name
                        } else {
                            EmailRecordTracking.Provider.POSTMARK.name
                        }

                    } else {
                        when (request.to_leads_vendor) {
                            EmailRecordTracking.Provider.POSTMARK -> EmailRecordTracking.Provider.POSTMARK.name
                            EmailRecordTracking.Provider.RESEND -> EmailRecordTracking.Provider.RESEND.name
                            EmailRecordTracking.Provider.MAILGUN -> EmailRecordTracking.Provider.MAILGUN.name
                            EmailRecordTracking.Provider.MAILJET -> EmailRecordTracking.Provider.MAILJET.name
                            EmailRecordTracking.Provider.POSTMARK_MAILGUN -> {
                                if (random.nextBoolean()) {
                                    EmailRecordTracking.Provider.POSTMARK.name
                                } else {
                                    EmailRecordTracking.Provider.MAILGUN.name
                                }
                            }

                            EmailRecordTracking.Provider.POSTMARK_RESEND -> {
                                if (random.nextBoolean()) {
                                    EmailRecordTracking.Provider.POSTMARK.name
                                } else {
                                    EmailRecordTracking.Provider.RESEND.name
                                }
                            }

                            EmailRecordTracking.Provider.POSTMARK_MAILJET -> {
                                if (random.nextBoolean()) {
                                    EmailRecordTracking.Provider.POSTMARK.name
                                } else {
                                    EmailRecordTracking.Provider.MAILJET.name
                                }
                            }

                            else -> EmailRecordTracking.Provider.POSTMARK.name
                        }
                    }
                } else
                    EmailRequest.Vendor.SEND_GRID.name

            OutreachBatchRequest.Request(
                task_id = task.id,
                email = email_request,
                internal_task = task,
            )
        }

        return batch.groupBy {
            it.email.prefer_vendor.assert_exist()
        }.map {
            val vendor = it.key
            val email_requests = it.value
            outreach_batch(
                OutreachBatchRequest(
                    batch = email_requests,
                    prefer_vendor = vendor
                ),
                User.get(user_id),
                send_mq_bulk_message = true,
            )
        }.flatten()

    }


    private fun get_bcg_outreach_emails(
        tasks: List<Task>,
        user_id: Int,
        represent_user_id: Int? = null,
        bcg_template: BcgHubEmailTemplate,
    ): List<EmailTemplateResult> {
        val res = placeholderBasedEmailTemplateService
            .process_template_by_data_id_batch(
                template = EmailTemplate.get(bcg_template.original_template_id).apply {
                    this.content_template = bcg_template.content_template ?: biz_error("can't get bcg template content")
                },
                tasks.map { task ->
                    PlaceholderBasedModelIds(
                        task_id = task.id,
                        project_id = task.project_id,
                        advisor_id = task.advisor_id,
                        client_id = task.client_id,
                        user_id = represent_user_id ?: user_id,
                    )
                }
            )
        return if (represent_user_id != null) {
            process_emails_on_behalf(res, user_id, represent_user_id)
        } else {
            res
        }
    }

    private fun get_outreach_emails(
        email_template_id: Int,
        tasks: List<Task>,
        user_id: Int,
        represent_user_id: Int? = null
    ): List<EmailTemplateResult> {
        val res = placeholderBasedEmailTemplateService
            .process_by_data_id_batch(
                email_template_id,
                tasks.map { task ->
                    PlaceholderBasedModelIds(
                        task_id = task.id,
                        project_id = task.project_id,
                        advisor_id = task.advisor_id,
                        client_id = task.client_id,
                        user_id = represent_user_id ?: user_id,
                    )
                }
            )

        return if (represent_user_id != null) {
            process_emails_on_behalf(res, user_id, represent_user_id)
        } else {
            res
        }
    }

    /**
     * https://www.notion.so/capvision/Leads-Send-on-Behalf-3166e7eeaf3347c68816631cbf1e9c64
     */
    private fun process_emails_on_behalf(
        emails: List<EmailTemplateResult>,
        user_id: Int,
        represent_user_id: Int,
    ): List<EmailTemplateResult> {
        val represent_user = User.get(represent_user_id)
        val user = User.get(user_id)
        return emails.map { email_template_result ->
            email_template_result.copy(
                // bcc current_user and represent user
                bcc_addresses = email_template_result.bcc_addresses.orEmpty()
                    .plus(
                        listOfNotNull(
                            represent_user.email.to_email_address_model(
                                represent_user.name
                            )
                        )
                    ).distinctBy {
                        it.email
                    },
            )
        }
    }

    private fun outreach(
        task_id: Int,
        user: User,
        email: EmailRequestPojo,
    ): Task {
        biz_require(email.to_list.isNotEmpty()) {
            "email to list cannot be empty"
        }

        return transactionTemplate.extExecute {
            val task = Task.get(task_id, include = setOf(Task::advisor.name))
            // trigger event, update task status
            taskEventService.trigger_event(TaskEvent {
                this.task_id = task_id
                type = TaskEventType.OUTREACH
            })

            process_and_send_email(email, task, user)

            task
        }
    }


    private fun mq_bulk_outreach(
        request: OutreachBatchRequest,
        user: User,
        prefer_vendor: String? = null
    ): List<Task> {
        return transactionTemplate.extExecute {
            val task_mappped_by_id = Task.findAll(
                query = Task.Query(
                    ids = request.batch.map { it.task_id }.toSet()
                )
            ).associateBy { it.id }
            val portals = mutableListOf<Portal>()
            val responses = request.batch.mapNotNull {
                val cur_task = task_mappped_by_id[it.task_id] ?: return@mapNotNull null
                // process email
                val cur_response = process_email(it.email, cur_task, user)
                // the persistence process of portal should be proceeded after email sent, so save it temporarily
                portals.add(cur_response.portal)
                // here is the process result, we use this to send real email
                OutreachBatchRequest.Request(
                    task_id = cur_task.id,
                    email = cur_response.email,
                    internal_task = cur_task,
                )
            }
            // get communication records to persist
            val communication_records = communicationRecordService.send_mq_bulk_outreach_email(
                OutreachBatchRequest(
                    batch = responses
                ),
                prefer_vendor = prefer_vendor,
            )
            // persist record info just like previous logic, note that the function rely on the list order
            portals.forEachIndexed { index, portal ->
                val cur_record = communication_records[index]
                Patch.fromMutator(portal) {
                    payload_obj = AdvisorUnsubscribeOutreachPayload(
                        id = biz_required_not_null(task_mappped_by_id[cur_record.task_id]?.advisor_id),
                        user_id = user.id,
                        message_id = cur_record.email_record?.message_id,
                        outreach_subject = cur_record.email_record?.subject,
                    )
                }.patch()
            }
            task_mappped_by_id.values.toList()
        }
    }

    data class ProcessResponse(
        val email: EmailRequestPojo,
        val portal: Portal,
    )

    private fun process_email(
        email: EmailRequestPojo,
        task: Task,
        user: User,
    ): ProcessResponse {
        val user_id = user.id

        var mail = outreachIPWarmUpService
            .process_outreach_email(email, user.email, user.outreach_email)

        // create survey portal and resolve portal token if it is needed
        var tmp = taskSurveyOutreachService
            .process_email_request(
                email = mail,
                task = task,
                user_id = user_id,
            )
        mail = tmp.first
        // create unsubscribe portal and resolve portal token if it is needed
        tmp = taskOutreachUnsubscribeService
            .process_email_request(
                email = mail,
                task = task,
                user_id = user_id,
            )
        mail = tmp.first
        val unsubscribe_portal = tmp.second
        // create advisor decline portal and resolve portal token if it is needed
        tmp = taskAdvisorDeclineService.process_email_request(
            email = mail,
            project_id = task.project_id,
            task_id = task.id,
            advisor_id = task.advisor_id,
        )
        mail = tmp.first
        return ProcessResponse(
            email = mail,
            portal = unsubscribe_portal,
        )
    }

    private fun process_and_send_email(
        email: EmailRequestPojo,
        task: Task,
        user: User,
    ) {
        val response = process_email(email, task, user)
        val res = communicationRecordService.send_outreach_email(response.email, task)
        // update portal payload
        // set message_id and outreach_subject
        Patch.fromMutator(response.portal) {
            payload_obj = AdvisorUnsubscribeOutreachPayload(
                id = biz_required_not_null(task.advisor_id),
                user_id = user.id,
                message_id = res.email_record?.message_id,
                outreach_subject = res.email_record?.subject,
            )
        }.patch()
    }

    fun outreach_batch(
        request: OutreachBatchRequest,
        user: User,
        is_linkedin: Boolean = false,
        send_mq_bulk_message: Boolean = false,
    ): List<Task> {
        biz_require(request.batch.isNotEmpty())

        // if template contains 'SURVEY_OUTREACH' tag, we treat it as survey outreach
        val template =
            EmailTemplate.get(biz_required_not_null(request.batch.first().email.template_id))
        val is_survey_outreach =
            EmailTemplateBuiltInTag.SURVEY_OUTREACH.name in template.tags.orEmpty()

        if (is_linkedin) {
            val tasks = request.batch
                .mapNotNull { it.internal_task }
                .takeIf { it.isNotEmpty() }
            // if the method is called by outreach_all, the task will be set
            // there's no need to retrieve
                ?: Task.findAll(
                    query = Task.Query(
                        ids = request.batch.map { it.task_id }.toSet()
                    )
                )
            tasks.forEach { task ->
                val found_record = AdvisorLinkedinLastRecord.firstOrNull(
                    AdvisorLinkedinLastRecord.Query(
                        advisor_id = task.advisor_id.assert_valid_id(),
                        project_id = task.project_id.assert_valid_id(),
                    )
                )
                if (found_record != null) {
                    Patch.fromMutator(found_record) {
                        this.last_outreach_email_tpl_id = template.id
                        this.is_email_ai_refined = request.is_email_ai_refined
                    }.patch()
                } else {
                    AdvisorLinkedinLastRecord.save(
                        AdvisorLinkedinLastRecord().apply {
                            this.advisor_id = task.advisor_id.assert_valid_id()
                            this.project_id = task.project_id.assert_valid_id()
                            this.last_outreach_email_tpl_id = template.id
                            this.is_email_ai_refined = request.is_email_ai_refined
                        }
                    )
                }
            }
            val project_ids = tasks.ids(Task::project_id)
            project_ids.forEach { project_id ->
                AdvisorLinkedinLastRecord.firstOrNull(
                    AdvisorLinkedinLastRecord.Query(
                        user_id = user.id.assert_valid_id(),
                        project_id = project_id.assert_valid_id(),
                    )
                )?.let {
                    Patch.fromMutator(it) {
                        this.last_outreach_email_tpl_id = template.id
                        this.is_email_ai_refined = request.is_email_ai_refined
                    }.patch()
                } ?: let {
                    AdvisorLinkedinLastRecord.save(
                        AdvisorLinkedinLastRecord().apply {
                            this.user_id = user.id.assert_valid_id()
                            this.project_id = project_id.assert_valid_id()
                            this.last_outreach_email_tpl_id = template.id
                            this.is_email_ai_refined = request.is_email_ai_refined
                        }
                    )
                }
            }
        }

        if (is_survey_outreach) {
            // validate tc exist
            biz_required_not_null(
                Tc.firstOrNull(
                    Tc.Query(
                        type = Tc.Type.SURVEY,
                        is_current = true,
                    )
                )
            ) {
                "require 1 TC(with type = ${Tc.Type.SURVEY}, is_current = true)"
            }
        }
        if (send_mq_bulk_message) {
            return mq_bulk_outreach(
                request, user, request.prefer_vendor
            )
        } else {
            return request.batch.mapNotNull { req ->
                req.email.from = req.email.from.takeUnless { it.isBlank() } ?: user.email
                req.email.from_name = req.email.from_name ?: user.name
                try {
                    outreach(
                        req.task_id,
                        user,
                        req.email,
                    )
                } catch (e: Exception) {
                    if (e is BusinessException) {
                        // todo: we need a way to tell frontend app what happened here.
                    } else {
                        e.printStackTrace()
                    }
                    null
                }
            }
        }
    }


    /**
     * 1. If task_id passed, we will render the email template directly by task_id.
     * 2. If task_id is null and advisor_id passed, we will create the lead task for rendering by advisor_id.
     * 3. If advisor_id is also null, we will try to create the advisor by advisor info and then create the task for rendering.
     * 4. Additionally, if advisor_id and advisor info both passed, we will update the advisor info by advisor id.
     */

    data class HandleOutreachEmailRequest(
        val project_id: Int,
        val advisor: JacksonPatch<Advisor>?,
        val advisor_id: Int?,
        val task_id: Int?,
        val email: EmailRequestPojo,
        val template_id: Int? = null,
        val with_compliance_section: Boolean = true,
    )

    data class HandleOutreachEmailResponse(
        val email: EmailRequestPojo,
        val task: Task,
    )

    // this function is for sending email in linkedin
    fun process_outreach_email_content_to_plain_text(
        email: EmailRequestPojo,
        project_id: Int,
        advisor_id: Int?,
        advisor_patch: JacksonPatch<Advisor>?,
        task_id: Int?,
        user_id: Int,
        with_compliance_section: Boolean = true,
    ): HandleOutreachEmailResponse {
        val task = task_id?.let {
            Task.get(it, include = setOf(Task::advisor.name))
        } ?: let {
            // if the task not exist or task id not passed, we need to render the email template by ourselves
            var task_creation_source = TaskCreationSource.SCRAPER_IMPORT
            val target_advisor_id = advisor_id ?: let {
                task_creation_source = TaskCreationSource.SCRAPER_IMPORT_FRESH
                advisorCreationService.create_v1(
                    advisor_patch?.entity.assert_exist()
                ).id
            }
            val found_or_created_task = Task.firstOrNull(
                query = Task.Query(
                    project_id = project_id,
                    advisor_id = target_advisor_id,
                    general_status_not_in = setOf(
                        TaskStatus.General.ARRANGED,
                        TaskStatus.General.COMPLETED,
                    ),
                ),
                include = Includes.setOf(Task::advisor)
            ) ?: taskCreationService.create_batch(
                project_id = project_id,
                requests = listOf(
                    TaskCreateRequest(
                        task = Task {
                            this.advisor_id = target_advisor_id
                            this.angle_id = null
                            this.willingness = TaskWillingnessType.UNKNOWN
                            this.creation_source = task_creation_source
                        }
                    )
                ),
                user_id = user_id,
            ).first().let {
                Task.joinOnce(it, Includes.setOf(Task::advisor))
            }
            val redered_email = placeholderBasedEmailTemplateService.process_by_data(
                template = EmailTemplate.get(
                    email.template_id ?: biz_error("template id should be passed if task not created")
                ),
                PlaceholderBasedModelIds(
                    project_id = found_or_created_task.project_id,
                    advisor_id = found_or_created_task.advisor_id,
                    task_id = found_or_created_task.id,
                    client_id = found_or_created_task.client_id,
                    user_id = user_id,
                ).fetch_data()
            )
            email.content = redered_email.content
            found_or_created_task
        }
        val user = User.get(user_id)
        email.from = email.from.takeUnless { it.isBlank() } ?: user.email
        email.from_name = email.from_name ?: user.name
        val step1_processed_mail = outreachIPWarmUpService
            .process_outreach_email(email, user.email, user.outreach_email)
        val step2_processed_mail = taskSurveyOutreachService
            .process_email_request(
                email = step1_processed_mail,
                task = task,
                user_id = user.id,
            ).first
        val step3_processed = taskOutreachUnsubscribeService
            .process_email_request(
                email = step2_processed_mail,
                task = task,
                user_id = user.id,
                plain_text = true,
                with_compliance_section = with_compliance_section,
            )
        val step3_processed_mail = step3_processed.first
        val unsubscribe_portal = step3_processed.second
        val step4_proceed_mail = taskAdvisorDeclineService
            .process_email_request(
                email = step3_processed_mail,
                project_id = task.project_id,
                task_id = task.id,
                advisor_id = task.advisor_id,
            ).first

        val email_record = EmailRecord.save(step4_proceed_mail.to_record())

        val record = CommunicationRecord.save(CommunicationRecord {
            this.project_id = task.project_id
            this.task_id = task.id
            this.advisor_id = task.advisor_id
            this.email_content_type = EmailContentType.PROJECT_OUTREACH
            this.email_record_id = email_record.id
            this.email_template_id = email_record.template_id
            // this is important, we need to access this property later
            this.email_record = email_record
            this.via_linkedin = true
        })

        // record latest outreach record id and latest task communication time
        Patch.fromMutator(task) {
            latest_outreach_record_id = record.id
            latest_task_communication_time = record.create_at
        }.patch()

        Patch.fromMutator(unsubscribe_portal) {
            payload_obj = AdvisorUnsubscribeOutreachPayload(
                id = biz_required_not_null(task.advisor_id),
                user_id = user.id,
                message_id = email_record.message_id,
                outreach_subject = email_record.subject,
            )
        }.patch()
        if (advisor_id != null && advisor_patch != null) {
            advisor_patch.entity.id = advisor_id
            advisorService.patch_cascade(patch = advisor_patch, include = advisor_patch.fields)
        }
        Task.join(
            task,
            Includes.setOf(
                Task::advisor dot Advisor::latest_task dot Task::project,
                Task::advisor dot Advisor::jobs,
                Task::advisor dot Advisor::contact_infos_without_mosaic,
                Task::advisor dot Advisor::email_address_score,
                Task::advisor dot Advisor::contact_infos,
            ),
        )
        return HandleOutreachEmailResponse(
            email = step4_proceed_mail,
            task = task,
        )
    }

    fun set_task_outreach_status(
        task_ids: Set<Int>
    ) {
        task_ids.forEach {
            taskEventService.trigger_event(TaskEvent {
                this.task_id = it
                type = TaskEventType.OUTREACH
            })
        }
    }

    fun outreach_batch_async(
        request: OutreachBatchRequest,
        user_id: Int,
        send_mq_bulk_message: Boolean = false,
    ): OutreachProcessRecord {
        biz_require(request.batch.isNotEmpty())

        // if template contains 'SURVEY_OUTREACH' tag, we treat it as survey outreach
        val template =
            EmailTemplate.get(request.batch.first().email.template_id.assert_not_null())

        if (EmailTemplateBuiltInTag.SURVEY_OUTREACH.name in template.tags.orEmpty()) {
            // validate tc exist
            biz_required_not_null(
                Tc.firstOrNull(
                    Tc.Query(
                        type = Tc.Type.SURVEY,
                        is_current = true,
                    )
                )
            ) {
                "require 1 TC(with type = ${Tc.Type.SURVEY}, is_current = true)"
            }
        }
        val cur_user = User.get(user_id)
        val process_record = OutreachProcessRecord.save(
            OutreachProcessRecord {
                this.total_count = request.batch.count()
                this.outreach_task_ids = request.batch.map {
                    it.task_id
                }
            }
        )
        global_executor.execute {
            outreach_batch_async_process(
                request,
                process_record,
                cur_user,
                send_mq_bulk_message = send_mq_bulk_message
            )
        }
        return process_record
    }

    fun outreach_batch_async_process(
        request: OutreachBatchRequest,
        record: OutreachProcessRecord,
        user: User,
        send_mq_bulk_message: Boolean = false,
    ) {
        var mutable_record = record
        var cur_succeed_tasks = mutableListOf<OutreachProcessRecord.TaskOutreachResult>()
        var cur_failed_tasks = mutableListOf<OutreachProcessRecord.TaskOutreachResult>()
        if (send_mq_bulk_message) {
            request.batch.chunked(100).forEach { cur_request_batch ->
                cur_request_batch.forEach {
                    it.email.from = it.email.from.takeUnless { cur_request -> cur_request.isBlank() } ?: user.email
                    it.email.from_name = it.email.from_name ?: user.name
                }
                try {
                    outreach_batch(
                        OutreachBatchRequest(
                            batch = cur_request_batch,
                            prefer_vendor = request.prefer_vendor
                        ),
                        user,
                        send_mq_bulk_message = true
                    )
                    cur_request_batch.forEach {
                        cur_succeed_tasks.add(
                            OutreachProcessRecord.TaskOutreachResult(
                                task_id = it.task_id,
                                succeed = true,
                            )
                        )
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                    cur_request_batch.forEach {
                        cur_failed_tasks.add(
                            OutreachProcessRecord.TaskOutreachResult(
                                task_id = it.task_id,
                                succeed = false,
                                reason = e.message.orEmpty()
                            )
                        )
                    }

                }
                mutable_record = Patch.fromMutator(record) {
                    this.succeed_count = this.succeed_count + cur_succeed_tasks.size
                    this.failed_count = this.failed_count + cur_failed_tasks.size
                    this.failed_tasks = this.failed_tasks + cur_failed_tasks
                }.patchThenGet()
                cur_failed_tasks = mutableListOf()
                cur_succeed_tasks = mutableListOf()
            }
        } else {
            request.batch.forEachIndexed { index, cur_request ->
                cur_request.email.from = cur_request.email.from.takeUnless { it.isBlank() } ?: user.email
                cur_request.email.from_name = cur_request.email.from_name ?: user.name
                try {
                    outreach(
                        cur_request.task_id,
                        user,
                        cur_request.email,
                    )
                    cur_succeed_tasks.add(
                        OutreachProcessRecord.TaskOutreachResult(
                            task_id = cur_request.task_id,
                            succeed = true,
                        )
                    )

                } catch (e: Exception) {
                    e.printStackTrace()
                    cur_failed_tasks.add(
                        OutreachProcessRecord.TaskOutreachResult(
                            task_id = cur_request.task_id,
                            succeed = false,
                            reason = e.message.orEmpty()
                        )
                    )
                }
                if (index % 10 == 0) {
                    mutable_record = Patch.fromMutator(record) {
                        this.succeed_count = this.succeed_count + cur_succeed_tasks.size
                        this.failed_count = this.failed_count + cur_failed_tasks.size
                        this.failed_tasks = this.failed_tasks + cur_failed_tasks
                    }.patchThenGet()
                    cur_failed_tasks = mutableListOf()
                    cur_succeed_tasks = mutableListOf()
                }
            }
        }
        Patch.fromMutator(mutable_record) {
            this.succeed_count = this.succeed_count + cur_succeed_tasks.size
            this.failed_count = this.failed_count + cur_failed_tasks.size
            this.failed_tasks = this.failed_tasks + cur_failed_tasks
            this.finished = true
        }.patch()
    }

    fun get_async_outreach_status(
        record_id: Int
    ): OutreachProcessRecord {
        return OutreachProcessRecord.get(record_id)
    }

}

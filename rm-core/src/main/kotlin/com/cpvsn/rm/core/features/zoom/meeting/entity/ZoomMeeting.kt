package com.cpvsn.rm.core.features.zoom.meeting.entity

import com.cpvsn.core.base.entity.BaseQuery
import com.cpvsn.crud.orm.annotation.Column
import com.cpvsn.crud.orm.annotation.Relation
import com.cpvsn.crud.query.Criteria
import com.cpvsn.rm.core.base.entity.RmCompanion
import com.cpvsn.rm.core.base.entity.RmEntity
import com.cpvsn.rm.core.features.task.arrange.TaskArrangement
import com.cpvsn.rm.core.features.zoom.meeting.base.ZoomAppEnum
import com.cpvsn.rm.core.features.zoom.meeting.pojo.ReadableMeetingEvent
import com.cpvsn.rm.core.features.zoom.meeting.pojo.ScheduledCallTabParticipantZoom
import com.cpvsn.rm.core.features.zoom.meeting.pojo.ZoomMeetingInsight
import com.cpvsn.rm.core.features.zoom.meeting.pojo.ZoomMeetingStateTimeTravel
import java.time.Instant

class ZoomMeeting : RmEntity() {
    companion object : RmCompanion<ZoomMeeting>()

    @Column
    var meeting_id: Long = 0L

    @Column
    var app_enum: ZoomAppEnum? = null

    @Column
    var account_email: String? = null

    @Column
    var topic: String = ""

    /**
     * expected start/end time when arrange
     */
    @Column
    var start_time: Instant = Instant.EPOCH

    @Column
    var end_time: Instant? = Instant.EPOCH

    @Column
    var password: String = ""

    /**
     * 是否启用录制
     */
    @Column
    var enable_record: Boolean = false

    /**
     * 是否转写
     */
    @Column
    var enable_transcription: Boolean = false

    @Column
    var recording_expired: Boolean = false

    @Column
    var transcript_expired: Boolean = false

    @Column
    var status: Status = Status.CREATED

    @Column
    var join_url: String = ""

    @Column
    var alternative_host_join_url: String = ""

    @Column
    var alternative_hosts: String = ""

    @Column
    var enable_waiting_room: Boolean? = null

    @Column
    var send_ai_summary: Boolean? = null

    //region +
    val display_meeting_id: String
        get() = this.meeting_id.toString().replace(Regex("(\\d{1,4})(?=(\\d{4})*(\\D|$))"), " $1")

    var instances: List<ZoomMeetingInstance>? = null

    var latest_instance: ZoomMeetingInstance? = null

    @Relation(backReference = "meeting_entity_id")
    var participants: List<ZoomMeetingParticipant>? = null

    @Relation(backReference = "meeting_entity_id")
    var meeting_entries: List<ZoomMeetingEntry>? = null

    var meeting_events: List<ZoomMeetingEvent>? = null

    @Relation(backReference = "meeting_entity_id")
    var registrants: List<ZoomMeetingRegistrant>? = null

    // computed
    var insight: ZoomMeetingInsight? = null

    var scheduled_call_tab_participants: List<ScheduledCallTabParticipantZoom>? = null

    var readable_events: List<ReadableMeetingEvent>? = null

    var state_time_travel: ZoomMeetingStateTimeTravel? = null

    val asset_completed: Boolean
        get() = (status == Status.ENDED) && (latest_instance != null) && (enable_record)

    //endregion

    enum class Status {
        CREATED,
        STARTED,
        ENDED,
        DELETED
    }

    data class Query(
        @Criteria.Eq
        val id: Int? = null,
        @Criteria.IdsIn
        val ids: Set<Int>? = null,

        @Criteria.Eq
        val meeting_id: Long? = null,
        @Criteria.IdsIn
        val meeting_ids: Set<Long>? = null,

        @Criteria.Eq
        val recording_expired: Boolean? = null,
        @Criteria.Eq
        val transcript_expired: Boolean? = null,
        @Criteria.Gte
        val create_at_gte: Instant? = null,

        @Criteria.Eq
        val status: Status? = null,

        @Criteria.Eq
        val enable_record: Boolean? = null,

        @Criteria.Eq
        val app_enum: ZoomAppEnum? = null,

        @Criteria.Join("{this}.id = {that}.zoom_meeting_entity_id")
        val task_arrangement: TaskArrangement.Query? = null,
    ) : BaseQuery<ZoomMeeting>()
}

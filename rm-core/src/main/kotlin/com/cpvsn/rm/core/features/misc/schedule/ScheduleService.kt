package com.cpvsn.rm.core.features.misc.schedule

import com.cpvsn.core.util.extension.assert_valid_id
import com.cpvsn.crud.util.EntityClassificationUtil
import com.cpvsn.rm.core.base.taskscoped.TaskScopedEntityService
import com.cpvsn.rm.core.extensions.assert_exist
import com.cpvsn.rm.core.extensions.toDefaultInstant
import com.cpvsn.rm.core.features.task.Task
import com.cpvsn.rm.core.features.task.TaskService
import com.cpvsn.rm.core.features.task.arrange.TaskArrangeService
import com.cpvsn.rm.core.features.task.event.TaskEvent
import com.cpvsn.rm.core.features.task.event.TaskEventService
import com.cpvsn.rm.core.features.task.event.TaskEventType
import com.cpvsn.rm.core.features.task.transfer.TaskTransferUtil
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDateTime

@Service
class ScheduleService: TaskScopedEntityService {

    //region @
    @Autowired
    private lateinit var taskEventService: TaskEventService
    @Autowired
    private lateinit var calendarService: CalendarService
    @Autowired
    private lateinit var taskService: TaskService
    @Autowired
    private lateinit var repo: ScheduleRepo
    @Autowired
    private lateinit var taskArrangeService: TaskArrangeService
    //endregion

    @Transactional
    fun create_pm_schedule(
            pm_id: Int,
            task_id: Int,
            schedule: Schedule
    ): Schedule {
        val task = Task.get(task_id, setOf(Task::schedule.name))

        schedule.pm_id = pm_id
        schedule.advisor_id = task.advisor_id
        schedule.project_id = task.project_id
        schedule.task_id = task_id
        schedule.creator_type = Schedule.Role.PM
        schedule.status = Schedule.Status.CALENDAR_SENT // later we'll send calendar

        if (task.schedule == null) {
            Schedule.save(schedule)
        } else {
            // we got to reuse the id
            // so that we can find the calendars corresponds to previous schedule
            // and increase the sequence
            schedule.id = task.schedule!!.id
            Schedule.update(schedule)
        }

        return schedule
    }

    @Transactional
    fun create_advisor_schedule(
            advisor_id: Int,
            task_id: Int,
            schedules: List<Schedule>
    ): List<Schedule> {
        schedules.forEach {
            it.task_id = task_id
            it.creator_type = Schedule.Role.ADVISOR
        }

        taskEventService.trigger_event(TaskEvent().apply {
            this.task_id = task_id
            this.type = TaskEventType.ADVISOR_SCHEDULE_RESPOND
        })
        val old_list = Schedule.findAll(Schedule.Query(
                advisor_id = advisor_id,
                creator_type = Schedule.Role.ADVISOR
        ))
        return overwrite(old_list, schedules)
    }

    @Transactional
    fun create_contact_schedule(
            contact_id: Int?,
            schedules: List<Schedule>
    ): List<Schedule> {
        schedules.forEach {
            it.creator_type = Schedule.Role.CLIENT_CONTACT
            if (it.is_new) {
                it.save()
            } else {
                it.update()
            }
            taskEventService.trigger_event(TaskEvent().apply {
                this.task_id = it.task_id.assert_valid_id { "missing task_id property" }
                this.type = TaskEventType.CONTACT_SCHEDULE_RESPOND
                this.payload_id = it.id
            })
        }
        return schedules
    }

    @Transactional
    fun cancel_by_task(task_id: Int): Schedule {
        val task = Task.get(task_id, setOf(Task::schedule.name))
        val schedule = task.schedule
                .assert_exist(message = "no available schedule for task(id=${task.id}), please ensure that task has been arranged correctly")
        val res = cancel(schedule.id)
        taskArrangeService.cancel_scheduled_task(task_id)
        return res
    }

    @Transactional
    fun cancel(id: Int): Schedule {
        val schedule = Schedule.get(id, setOf(Schedule::calendars.name))

        // cancel calendar
        (schedule.calendars ?: emptyList())
                .forEach {
                    // here we create a bi-directional reference
                    it.schedule = schedule
                    calendarService.cancel(it)
                }

        schedule.status = Schedule.Status.CANCELLED
        Schedule.update(schedule)
        return schedule
    }

    private fun overwrite(
            old_list: List<Schedule>,
            new_list: List<Schedule>
    ): List<Schedule> {
        val (to_save, to_update, to_remove)
                = EntityClassificationUtil.classify(old_list, new_list)
        Schedule.save(to_save)
        Schedule.update(to_update)
        to_remove.forEach {
            Schedule.delete(it)
        }
        return new_list
    }

    override fun handle_move_task(
        tasks: List<Task>,
        to_project_id: Int,
        operator: Int?
    ) {
        TaskTransferUtil.transfer1toN(
            tasks,
            to_project_id,
            operator,
            Task::schedules,
            repo,
        )
    }

    /**
     * https://www.notion.so/capvision/Arrange-Show-all-scheduled-calls-on-calendar-when-scheduling-18c29e222a774007bc049dfce58ae1fc
     */
    fun show_all_available_and_scheduled_time_slots(
        query: Schedule.Query,
    ): Schedule.Query {
        // build or query
        val or_query = mutableListOf<Schedule.Query>()

        query.task_id?.let {
            // query all available time slots related to the client for the specified task
            or_query.add(
                Schedule.Query(
                    task_id = query.task_id,
                    creator_type = Schedule.Role.CLIENT_CONTACT
                )
            )
        }
        query.task?.client_id?.let {
            // query all scheduled time slots for the client
//            or_query.add(
//                Schedule.Query(
//                    task = query.task,
//                    status = Schedule.Status.CALENDAR_SENT
//                )
//            )
        }

        query.advisor_id?.let {
            // query all available time slots for the advisor
            or_query.add(Schedule.Query(advisor_id = it, creator_type = Schedule.Role.ADVISOR))
            // query all already scheduled time slots for the advisor
            or_query.add(
                Schedule.Query(advisor_id = it, status = Schedule.Status.CALENDAR_SENT)
            )
        }

        return Schedule.Query(
            // to avoid conflicts during the arrangement, 30 days is enough
            start_time_gte = LocalDateTime.now().minusDays(30).toDefaultInstant(),
            or = or_query
        )
    }

}

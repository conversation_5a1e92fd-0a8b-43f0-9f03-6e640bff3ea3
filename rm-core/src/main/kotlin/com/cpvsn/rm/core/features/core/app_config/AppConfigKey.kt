package com.cpvsn.rm.core.features.core.app_config

import com.cpvsn.rm.core.annotation.DocConstant

@DocConstant
enum class AppConfigKey(
    val category: AppConfigCategory,
    val value_type: AppConfigValueType,
    val value_hint: AppConfigValueHint,
    val multiple: <PERSON><PERSON><PERSON>,
    val description: String = "",
) {
    CAPVISION_COMPLIANCE_OFFICER_EMAIL_GROUP(
        category = AppConfigCategory.EMAIL_ADDRESS,
        value_type = AppConfigValueType.STRING,
        value_hint = AppConfigValueHint.EMAIL_ADDRESS,
        multiple = true,
        "capvision legal email group",
    ),
    CAPVISION_COMPLIANCE_LIST_EMAIL(
        category = AppConfigCategory.EMAIL_ADDRESS,
        value_type = AppConfigValueType.STRING,
        value_hint = AppConfigValueHint.EMAIL_ADDRESS,
        multiple = false,
        description = "compliance list sender email",
    ),
    CAPVISION_FINANCE_EMAIL_GROUP(
        category = AppConfigCategory.EMAIL_ADDRESS,
        value_type = AppConfigValueType.STRING,
        value_hint = AppConfigValueHint.EMAIL_ADDRESS,
        multiple = true,
        "capvision finance email group",
    ),
    CAPVISION_KM_EMAIL_GROUP(
        category = AppConfigCategory.EMAIL_ADDRESS,
        value_type = AppConfigValueType.STRING,
        value_hint = AppConfigValueHint.EMAIL_ADDRESS,
        multiple = true,
        "capvision km email group",
    ),
    CAPVISION_SURVEY_REPORT_GROUP(
        category = AppConfigCategory.EMAIL_ADDRESS,
        value_type = AppConfigValueType.STRING,
        value_hint = AppConfigValueHint.EMAIL_ADDRESS,
        multiple = false,
        "capvision survey report email group",
    ),
    CAPVISION_SALES_USER_REPORT_GROUP(
        category = AppConfigCategory.EMAIL_ADDRESS,
        value_type = AppConfigValueType.STRING,
        value_hint = AppConfigValueHint.EMAIL_ADDRESS,
        multiple = true,
        "capvision sales user report email group",
    ),
    CAPVISION_VIKING_REPORT_GROUP(
        category = AppConfigCategory.EMAIL_ADDRESS,
        value_type = AppConfigValueType.STRING,
        value_hint = AppConfigValueHint.EMAIL_ADDRESS,
        multiple = true,
        description = "A report will be sent to these users with informations about calls made by Viking"
    ),
    CAPVISION_PAYMENT_EMAIL(
        category = AppConfigCategory.EMAIL_ADDRESS,
        value_type = AppConfigValueType.STRING,
        value_hint = AppConfigValueHint.EMAIL_ADDRESS,
        multiple = true,
        description = "Automatic payment email comes from this address."
    ),

    /**
     * User IDs for the sales user report cron job
     * A report will be sent for each of these users.
     */
    SALES_USER_REPORT_IDS(
        category = AppConfigCategory.OTHER,
        value_type = AppConfigValueType.STRING,
        value_hint = AppConfigValueHint.OTHER,
        multiple = true,
        description = "A sales user report will be sent to the CAPVISION_SALES_USER_REPORT_GROUP for each comma-separated ID."
    ),
    CAPVISION_AR_TEAM_EMAIL_GROUP(
        category = AppConfigCategory.EMAIL_ADDRESS,
        value_type = AppConfigValueType.STRING,
        value_hint = AppConfigValueHint.EMAIL_ADDRESS,
        multiple = true,
        "应收账款小组与其他部门沟通",
    ),

    /**
     * At present, only frontend program actually read this value
     */
    LEGAL_TO_CLIENT_BEFORE_SIGN_TC_ALLOW_CLIENT_TYPES(
        category = AppConfigCategory.GLOBAL_LEGAL_SETTING,
        value_type = AppConfigValueType.STRING,
        value_hint = AppConfigValueHint.CLIENT_TYPE,
        multiple = true,
        "设置哪些 client type 允许在专家签 TC 前将 advisor 信息发给客户, 值为 Client.Type 逗号分隔，如果值为 'ALL' 则代表允许所有",
    ),

    /**
     * At present, only frontend program actually read this value
     * 与 LEGAL_TO_CLIENT_BEFORE_SIGN_TC_BLOCK_ADVISOR_LOCATIONS 搭配使用
     */
    LEGAL_TO_CLIENT_BEFORE_SIGN_TC_ADVISOR_LOCATION_RESTRICTED_CLIENT_TYPES(
        category = AppConfigCategory.GLOBAL_LEGAL_SETTING,
        value_type = AppConfigValueType.STRING,
        value_hint = AppConfigValueHint.CLIENT_TYPE,
        multiple = true,
        "设置哪些 client type 需要根据专家 location 来判定是否允许在专家签 TC 前将 advisor 信息发给客户"
    ),

    /**
     * At present, only frontend program actually read this value
     * 与 LEGAL_TO_CLIENT_BEFORE_SIGN_TC_ADVISOR_LOCATION_RESTRICTED_CLIENT_TYPES 搭配使用
     */
    LEGAL_TO_CLIENT_BEFORE_SIGN_TC_BLOCK_ADVISOR_LOCATIONS(
        category = AppConfigCategory.GLOBAL_LEGAL_SETTING,
        value_type = AppConfigValueType.NUMBER,
        value_hint = AppConfigValueHint.LOCATION,
        multiple = true,
        "设置哪些地区的 advisor 不允许在专家签 TC 前将 advisor 信息发给专家, 值为 location id 逗号分隔"
    ),

    PROJECT_GOOGLE_SHEET_TEMPLATE_ID(
        category = AppConfigCategory.SYSTEM,
        value_type = AppConfigValueType.STRING,
        value_hint = AppConfigValueHint.OTHER,
        multiple = false,
        "设置用来创建新的 project google sheet 的模板 id"
    ),
    PROJECT_GOOGLE_SHEET_FOLDER_ID(
        category = AppConfigCategory.SYSTEM,
        value_type = AppConfigValueType.STRING,
        value_hint = AppConfigValueHint.OTHER,
        multiple = false,
        "project google sheet 存放的文件夹 id"
    ),

    ENABLE_W9(
        category = AppConfigCategory.SYSTEM,
        value_type = AppConfigValueType.BOOLEAN,
        value_hint = AppConfigValueHint.OTHER,
        multiple = false,
        "设置是否开启 W9 功能"
    ),

    SURVEY_PROJECT_TEST_ANGLE_DEFAULT_MEMBERS(
        category = AppConfigCategory.OTHER,
        value_type = AppConfigValueType.BOOLEAN,
        value_hint = AppConfigValueHint.USER,
        multiple = true,
        "创建 SURVEY 项目时，'test' angle 的成员"
    ),

    /**
     * if value is null, we DO NOT use warm up IP to send email.
     */
    OUTREACH_IP_WARM_UP_START_DATE(
        category = AppConfigCategory.SYSTEM,
        value_type = AppConfigValueType.DATE,
        value_hint = AppConfigValueHint.OTHER,
        multiple = false,
        "Outreach Email IP warm up 的起始日期，系统根据该日期与当前日期的差值计算当天使用该 IP 发送 Outreach 邮件的数量."
    ),

    ENABLE_PAYGO_BILLING_JOB(
        category = AppConfigCategory.SYSTEM,
        value_type = AppConfigValueType.BOOLEAN,
        value_hint = AppConfigValueHint.OTHER,
        multiple = false,
        "是否执行 paygo billing job, 即是否需要每月一号自动生成 invoice"
    ),

    AUTO_GENERATE_NAME_IN_ZOOM_CLIENTS(
        category = AppConfigCategory.OTHER,
        value_type = AppConfigValueType.NUMBER,
        value_hint = AppConfigValueHint.OTHER,
        multiple = true,
        description = "Should generate in-meeting display name for these clients if zoom is used as bridge"
    ),

    SKIP_INPUT_DISPLAY_NAME_IN_ZOOM_CLIENTS(
        category = AppConfigCategory.OTHER,
        value_type = AppConfigValueType.NUMBER,
        value_hint = AppConfigValueHint.OTHER,
        multiple = true,
        description = "Skip input display name for these clients if zoom is used as bridge"
    ),

    USING_ZOOM_E3_FOR_ALL_FI_CLIENTS(
        category = AppConfigCategory.OTHER,
        value_type = AppConfigValueType.BOOLEAN,
        value_hint = AppConfigValueHint.OTHER,
        multiple = false,
        description = "if true, all FI clients will be using Zoom E3"
    ),

    USING_ZOOM_E3_FI_CLIENT_IDS(
        category = AppConfigCategory.OTHER,
        value_type = AppConfigValueType.NUMBER,
        value_hint = AppConfigValueHint.OTHER,
        multiple = true,
        description = "client ids which will use zoom enginee3 account"
    ),

    TWILIO_COMPLIANCE_ANNOUNCEMENT_FOR_CLIENT_IDS(
        category = AppConfigCategory.OTHER,
        value_type = AppConfigValueType.NUMBER,
        value_hint = AppConfigValueHint.OTHER,
        multiple = true,
        description = "will announce compliance message in twilio conferences these clients"
    ),

    CALL_TRACK_SLACK_CHANNEL_ID(
        category = AppConfigCategory.OTHER,
        value_type = AppConfigValueType.STRING,
        value_hint = AppConfigValueHint.OTHER,
        multiple = false,
        description = "Slack channel ID where call tracker updates will be sent"
    ),

    CLIENTS_WITH_RESCREEN_WORKFLOW_ENABLED(
        category = AppConfigCategory.OTHER,
        value_type = AppConfigValueType.NUMBER,
        value_hint = AppConfigValueHint.OTHER,
        multiple = true,
        description = "Client ID that enable rescreen function"
    ),

    TROLLEY_PAYMENT_FORM_DD(
        category = AppConfigCategory.OTHER,
        value_type = AppConfigValueType.STRING,
        value_hint = AppConfigValueHint.OTHER,
        multiple = false,
        description = "controls whether the payment form displays the Direct Deposit form, or the Trolley form"
    ),

    SELECT_TO_LEADS_EMAIL_VENDOR_VIA_REGION(
        category = AppConfigCategory.OTHER,
        value_type = AppConfigValueType.BOOLEAN,
        value_hint = AppConfigValueHint.OTHER,
        multiple = false,
        description = "select to leads outreach email vendor via the to list emails region"
    ),
    MAILJET_EMAIL_REGION(
        category = AppConfigCategory.OTHER,
        value_type = AppConfigValueType.BOOLEAN,
        value_hint = AppConfigValueHint.OTHER,
        multiple = false,
        description = "mailget email region",
    ),

    USING_CALL_CONSENT_SQ_CLIENTS(
        category = AppConfigCategory.OTHER,
        value_type = AppConfigValueType.STRING,
        value_hint = AppConfigValueHint.OTHER,
        multiple = true,
        description = "clients that require a recording consent question in their screening question branches"
    ),
    OUTSOURCE_STATISTIC_ADVISOR_IDS(
        category = AppConfigCategory.OTHER,
        value_type = AppConfigValueType.NUMBER,
        value_hint = AppConfigValueHint.OTHER,
        multiple = true,
        description = "outsource statistic advisor ids"
    ),
    AI_COLD_EMAIL_PERSONALIZATION_BY_EMPLOYEE(
        category = AppConfigCategory.OTHER,
        value_type = AppConfigValueType.NUMBER,
        value_hint = AppConfigValueHint.OTHER,
        multiple = true,
        description = "AI cold email personalization by employee"
    ),
    BCG_PAYMENT_NOTE(
        category = AppConfigCategory.OTHER,
        value_type = AppConfigValueType.STRING,
        value_hint = AppConfigValueHint.LOCATION,
        multiple = false,
        description = "BCG note added after payment emails",
    )
    ;

}

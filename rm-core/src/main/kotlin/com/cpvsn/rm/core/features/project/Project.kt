package com.cpvsn.rm.core.features.project

import com.cpvsn.bridge.sdk.model.ProjectType
import com.cpvsn.core.base.entity.BaseQuery
import com.cpvsn.core.util.delegates.PropDelegates
import com.cpvsn.core.util.extension.add_prefix
import com.cpvsn.crud.orm.annotation.*
import com.cpvsn.crud.query.Criteria
import com.cpvsn.crud.tsid.GeneratedTsId
import com.cpvsn.rm.core.annotation.DocConstant
import com.cpvsn.rm.core.base.agg.AggregationQuery
import com.cpvsn.rm.core.base.entity.ColumnDefinitions
import com.cpvsn.rm.core.base.entity.RmCompanion
import com.cpvsn.rm.core.base.entity.RmEntity
import com.cpvsn.rm.core.base.model.*
import com.cpvsn.rm.core.features.bridge.Region
import com.cpvsn.rm.core.features.client.Client
import com.cpvsn.rm.core.features.client.ClientOffice
import com.cpvsn.rm.core.features.client.accountnote.ClientAccountNote
import com.cpvsn.rm.core.features.client.contact.ClientContact
import com.cpvsn.rm.core.features.contract.entity.Contract
import com.cpvsn.rm.core.features.finance.revenue.Revenue
import com.cpvsn.rm.core.features.inquiry.model.Inquiry
import com.cpvsn.rm.core.features.task.lead_group.LeadGroup
import com.cpvsn.rm.core.features.misc.company.Company
import com.cpvsn.rm.core.features.misc.constant.BuiltInCurrency
import com.cpvsn.rm.core.features.misc.constant.Industry
import com.cpvsn.rm.core.features.outsource.pojo.OutsourceProjectApproval
import com.cpvsn.rm.core.features.project.legal.ProjectCompliance
import com.cpvsn.rm.core.features.record.MoreInfoRequestRecord
import com.cpvsn.rm.core.features.task.Task
import com.cpvsn.rm.core.features.task.angle.TaskAngle
import com.cpvsn.rm.core.features.task.event.TaskEvent
import com.cpvsn.rm.core.features.thirdparty.bcg.entity.BcgHubProject
import com.fasterxml.jackson.annotation.JsonIgnore
import java.math.BigDecimal
import java.time.Instant
import java.time.LocalDate

@TableDefinition(
    indices = [
        TableDefinition.IndexDefinition(
            columns = ["tsid"],
            type = TableDefinition.IndexType.UNIQUE
        ),
    ]
)
class Project
    : RmEntity(),
    MaybeHasNdbID,
    Zoned,
    HasTsId,
    EntityCloneable<Project>,
    HasClient {

    companion object : RmCompanion<Project>() {
        val CLIENT_CONTACT_EXTRA = setOf(
            Project::project_client_contacts.name,
            *setOf(
                ProjectClientContact::client_contact.name,
                *setOf(
                    ClientContact::contact_infos.name
                ).add_prefix(ProjectClientContact::client_contact.name)
            ).add_prefix(Project::project_client_contacts.name)
        )
    }

    @Column
    override var ndb_id: Int? = null

    @Column
    @GeneratedTsId
    @ColumnDefinition(type = ColumnDefinitions.TSID)
    override var tsid: String? = null

    @Column
    var name: String? = null

    @Column
    var code: String = ""

    @Deprecated("已改成关联company_id")
    @Column
    var investment_target: String? = null

    @Column
    var investment_target_company_ids_str: String? = null

    @Column
    override var client_id: Int? = null

    // https://www.notion.so/capvision/Create-Edit-Project-Case-Type-b1a6a6c046d54421ac06e671b64a0d13
    @Column
    var case_type: CaseType? = null

    @Column
    var expected_n: Int? = null

    @Column
    var actual_n: Int? = null

    @Column
    var type: Type = Type.CONSULTATION

    @Column
    var sub_type: SubType = type.available_sub_types.first()

    @Column
    var industry: Industry? = null

    @Column
    var request_content: String = ""

    @Column
    var sq_id: Int? = null

    /**
     * this property has value only when contract.payway is project_base?
     */
    @Column
    var contract_id: Int? = null

    @Column
    var status: Status = Status.ACTIVE

    /**
     * "Project Information -> Project Label ->
     * Proactive Project: Would it be possible to create a new "Project Label" as proactive?
     * That way we can track proactive work we launch with our clients"
     * -- email 2022.01.05
     */
    @Column
    var is_proactive: Boolean = false

    @Column
    var start_date: LocalDate? = null

    @Column
    var end_date: LocalDate? = null

    /**
     * Note that rate and rate_currency can only either be all null
     * or all not null!
     * this rate is the advisor_rate and only effective when the project subtype is `Survey`
     */
    @Column
    var rate: BigDecimal? = null

    @Column
    var rate_currency: String? = null

    /**
     *     This is Decipher's internal project ID.
     */
    @Column
    var decipher_id: String? = null //This is Decipher's internal project ID.

    @Column
    var decipher_statuses_last_update: Instant? = null

    @get: JsonIgnore
    @PatchFields(["rate_currency"])
    var rate_currency_enum: BuiltInCurrency? by PropDelegates.enum_nullable(this::rate_currency)

    //https://www.notion.so/capvision/Projects-Client-Charge-Override-ff2dbbaf0df54bc4921ca848b3d87806
    @Column
    var client_rate: BigDecimal? = null

    @Column
    var client_rate_currency: String? = null

    @get: JsonIgnore
    @PatchFields(["rate_currency"])
    var client_rate_currency_enum: BuiltInCurrency? by PropDelegates.enum_nullable(this::client_rate_currency)

    @Column
    @JsonIgnore
    var client_custom_fields_json: String? = null

    /**
     * see com.cpvsn.rm.core.features.client.custom.ClientCustomFields
     */
    @PatchFields(["client_custom_fields_json"])
    var client_custom_fields: Map<String, Any?>? by PropDelegates.json_nullable(this::client_custom_fields_json)

    @Column
    override var zone_id_string: String? = null

    /**
     * third party survey url
     *
     * see also:
     * https://www.notion.so/Survey-MVP-DB-Decipher-a86813cccdc34d37ab81f06da36a26b2
     * https://decipher.zendesk.com/hc/en-us/articles/360010274273-Getting-Started-with-Decipher
     */
    @Column
    var third_party_survey_url: String? = null

    @Column
    var google_sheet_id: String? = null

    val google_sheet_url: String?
        get() = google_sheet_id?.let { "https://docs.google.com/spreadsheets/d/$it/edit" }

    // https://www.notion.so/capvision/Projects-External-Recording-Access-2b37b3922eae453cbc8cfc6517deef5f
    @Column
    @JsonIgnore
    var external_contacts_for_recording: String = ""

    @PatchFields(["external_contacts_for_recording"])
    var external_contacts_for_recording_list: List<String> by PropDelegates.comma_list(this::external_contacts_for_recording) { it }

    @Column
    var client_provided_bridge_link: String? = null

    @Column
    override var clone_from_id: Int? = null

    @Column
    var slack_channel_name: String = ""

    @Column
    var slack_channel_id: String? = null

    @Column
    var survey_total_charge: BigDecimal? = null

    @Column
    var survey_total_charge_currency: BuiltInCurrency? = null

    /**
     * 适用于不单独task向客户收费，而是整个project合计收费的情形（Survey, Patients）
     */
    @Column
    var total_charge: BigDecimal? = null

    @Column
    var total_charge_currency: BuiltInCurrency? = null

    @Column
    var client_charge_per_complete: BigDecimal? = null

    @Column
    var client_charge_per_complete_currency: BuiltInCurrency? = null

    @Column
    var exported_to: Region? = null

    val is_exported_project: Boolean
        get() = exported_to != null

    val outsource_project_approve_message: String?
        get() {
            return if (exported_to == Region.CN) {
                outsource_project_consumer_approve_message
            } else {
                outsource_project_supplier_approve_message
            }
        }

    @Column
    var outsource_project_supplier_approve_message: String? = null


    // not only approve message, but latest consumer event message
    @Column
    var outsource_project_consumer_approve_message: String? = null

    @Column
    var outsource_request_content: String? = null

    @Column
    var outsource_client_compliance_rules: String? = null

    @Column
    var outsource_client_name: String? = null

    @JsonIgnore
    @Column
    var outsource_project_type: String? = null

    @PatchFields(["outsource_project_type"])
    var outsource_project_type_enum: ProjectType? by PropDelegates.enum_nullable(this::outsource_project_type)


    // https://www.notion.so/capvision/Projects-Project-level-blinding-5acac6938e7a4adf82bbbcc925f8af8b
    @Column
    var blind_expert_profiles: Boolean = false

    //region +
    @Relation
    override var clone_from: Project? = null

    @Relation(reference = "sq_id")
    var sq: Inquiry? = null

    @Relation(backReference = "project_id")
    var project_ca: List<Inquiry>? = null

    /**
     * Note that currently only consultation project has such relation
     */
    @Relation
    var project_client_contacts: List<ProjectClientContact>? = null

    var client_contacts: List<ClientContact>? = null

    @Relation
    var members: List<ProjectMember>? = null

    var manager: ProjectMember? = null

    @Relation
    var tags: List<ProjectTag>? = null

    @Relation
    var notes: List<ProjectNote>? = null

    @Relation
    var client_account_notes: List<ClientAccountNote>? = null

    @Relation
    var tickers: List<ProjectTicker>? = null

    @Relation
    var tasks: List<Task>? = null

    var leads: List<Task>? = null

    @Relation
    var client: Client? = null

    var contract: Contract? = null

    @Relation
    var angles: List<TaskAngle>? = null

    @Relation
    var lead_groups: List<LeadGroup>? = null

    var investment_target_companies: List<Company>? = null

    @Relation
    var advisor_tag_maps: List<ProjectAdvisorTagMap>? = null

    @Relation
    var events: List<ProjectEvent>? = null

    @Relation(backReference = "project_id")
    var task_events: List<TaskEvent>? = null

    @Relation
    var legal_rules: ProjectCompliance? = null

    var call_opportunity_sum_jit: Int? = null

    var db_info_page_url_jit: String? = null
    var db_client_task_page_url_jit: String? = null
    var db_leads_page_url_jit: String? = null

    @Deprecated("use revenues instead. this could be a list due to ADJ revenue")
    var survey_revenue: Revenue? = null

    @Relation(backReference = "project_id")
    var revenues: List<Revenue>? = null

    @Relation(backReference = "project_tsid", reference = "tsid")
    var outsource_approvals: List<OutsourceProjectApproval>? = null

    //endregion

    //region $
    /**
     * Note that this only make sense when project is consultation project
     */
    @PatchFields(["investment_target_company_ids_str"])
    var investment_target_company_ids: List<Int> by PropDelegates
        .comma_list(this::investment_target_company_ids_str) { it.toIntOrNull() }

    @Column
    var sent_close_email_advisor_ids_str: String? = null

    @Column
    var client_office_id: Int? = null

    @Column
    var tpa_project: Boolean = false

    @Column
    var client_google_sheet_id: String? = null

    val client_google_sheet_url: String?
        get() = client_google_sheet_id?.let { "https://docs.google.com/spreadsheets/d/$it/edit" }

    @Column
    var enable_rescreen_flow: Boolean = false

    @Column
    var enable_lead_group: Boolean = false

    @Relation(reference = "client_office_id")
    var client_office: ClientOffice? = null

    @Relation
    var more_info_requests: List<MoreInfoRequestRecord>? = null

    @Relation(backReference = "project_id")
    var bcg_hub_project: BcgHubProject? = null

    /**
     * "If we close a project, then reopen, can we set the system to not re-email the experts?" -- email 2022.01.07
     */
    @PatchFields(["sent_close_email_advisor_ids_str"])
    var sent_close_email_advisor_ids: Set<Int> by PropDelegates
        .comma_set(this::sent_close_email_advisor_ids_str) { it.toIntOrNull() }

    /**
     * RequireExtra Project::investment_target_companies
     */
    @get:JsonIgnore
    val investment_target_company_names: String?
        get() = investment_target_companies.orEmpty()
            .joinToString(", ") { it.name.orEmpty() }
            .takeIf { it.isNotBlank() } ?: investment_target

    @get:JsonIgnore
    val client_compliance_officers
        get() = project_client_contacts
            ?.filter { it.client_contact?.is_legal_user == true }
            ?.mapNotNull { it.client_contact }

    /**
     * Note that this only make sense when project is consultation project
     */
    @get:JsonIgnore
    val client_common_contacts
        get() = project_client_contacts
            ?.filter {
                it.client_contact_type in setOf(
                    ProjectClientContact.Type.GENERAL,
                    ProjectClientContact.Type.PRIMARY
                )
            }
            ?.filter { it.client_contact?.is_common_user == true }
            ?.mapNotNull { it.client_contact }
    //endregion

    @Suppress("RemoveRedundantBackticks")
    enum class Type(
        val available_sub_types: Set<SubType>
    ) {
        CONSULTATION(
            available_sub_types = setOf(
                SubType.Consultation,
                SubType.`Physician Consultation`,
            )
        ),
        CONFERENCE(
            available_sub_types = setOf(
                SubType.Newstalk,
                SubType.Tele60,
                SubType.`Capvision Live`,
                SubType.Events,
                SubType.`MF Tele60`,
                SubType.`MF Events`,
                SubType.Training
            )
        ),
        RESEARCH_SERVICE(
            available_sub_types = setOf(
                SubType.`GRM Customized Reports`,
                SubType.`GRM Customized Data Services`,
                SubType.`GRM DD Services`,
                SubType.`GRM Non-Customized Research`,
                SubType.`GRM FA`,
                SubType.`GRM Others`,

                SubType.`MF RM customized reports`,
                SubType.`MF RM regular reports`,
                SubType.`MF RM others`
            )
        ),
        GES_CONVEY(
            available_sub_types = setOf(
                SubType.`Investigative Due Diligence`,
                SubType.`GES Consultation`,
                SubType.`2B Project`,
                SubType.`2C Project`,
                SubType.Survey,
                SubType.`FA Opportunity`
            )
        ),
        SURVEY(
            available_sub_types = setOf(
                SubType.Survey
            )
        ),
        PATIENTS(
            available_sub_types = setOf(
                SubType.Patients
            )
        )
    }

    @Suppress("RemoveRedundantBackticks")
    enum class SubType {
        // Consultation
        `Consultation`,
        `Survey`,
        `Patients`,

        // https://www.notion.so/capvision/New-Project-Type-Physician-Consultation-23cd178a65374a2ca11d5640d2415dac
        `Physician Consultation`,

        // Origin https://www.notion.so/capvision/Project-Type-Sell-Side-Teleconference-d5c9a207451944e2b7a4168123f12d5c

        // Changed the type name from 'Sell Side Teleconference' to 'Investor Call' in this requirement: https://www.notion.so/capvision/SST-project-Change-name-label-and-add-vetting-call-column-4cdec85bb8d948dcbdde76480f657286
        `Investor Call`,

        // https://www.notion.so/capvision/Projects-new-project-type-Longer-Term-Engagement-ceeb3343c8774a468767816a6455ab53
        `Longer Term Engagement`,

        // Conference
        `Newstalk`,
        `Tele60`,
        `Capvision Live`,
        `Events`,
        `MF Tele60`,
        `MF Events`,
        `Training`,

        // Research Service
        `GRM Customized Reports`,
        `GRM Customized Data Services`,
        `GRM DD Services`,
        `GRM Non-Customized Research`,
        `GRM Others`,
        `GRM FA`,

        `MF RM regular reports`,
        `MF RM customized reports`,
        `MF RM others`,

        // GES Convey
        `2B Project`,
        `2C Project`,
        `FA Opportunity`,
        `Investigative Due Diligence`,
        `GES Consultation`
    }

    @DocConstant(displayNameFieldName = "value")
    enum class Status(val value: String) {
        //in use
        ON_HOLD("Hold"),
        CLOSED("Closed"),
        ACTIVE("Active"),

        //not in use
        ADVISORS_NEEDED("Advisors Needed"),
        SUFFICIENT_ADVISORS("Sufficient Advisors"),
        URGENT("Urgent"),
        IN_PROCESS("in process"),
        COMPLETE("complete")
    }

    // https://www.notion.so/capvision/Create-Edit-Project-Case-Type-b1a6a6c046d54421ac06e671b64a0d13
    enum class CaseType(val value: String) {
        DUE_DILIGENCE("Due Diligence"),
        STRATEGY("Strategy"),
        PROPOSAL_OR_LOP("Proposal/LOP"),
        KNOWLEDGE_EFFORT_OR_INTERNAL("Knowledge Effort/Internal"),
    }

    @Suppress("DEPRECATION")
    data class Query(
        @Criteria.Eq
        val id: Int? = null,
        @Criteria.IdsIn
        val ids: Set<Int>? = null,

        @Criteria.Eq
        val ndb_id: Int? = null,
        @Criteria.IdsIn
        val ndb_ids: Set<Int>? = null,

        @Criteria.Gte
        val id_gte: Int? = null,
        @Criteria.Lte
        val id_lte: Int? = null,

        @Criteria.Eq
        val tsid: String? = null,
        @Criteria.Ne
        val tsid_ne: String? = null,
        @Criteria.In
        val tsid_in: Set<String>? = null,
        @Criteria.Expr("({this}.tsid is null or {this}.tsid = '') = #{value}")
        val tsid_is_null_or_empty: Boolean? = null,

        @Criteria.IsNull
        val ndb_id_is_null: Boolean? = null,
        @Criteria.Eq
        val type: Type? = null,
        @Criteria.In
        val type_in: Set<Type>? = null,
        @Criteria.NotIn
        val type_not_in: Set<Type>? = null,

        @Criteria.Eq
        val case_type: CaseType? = null,
        @Criteria.In
        val case_type_in: Set<CaseType>? = null,
        @Criteria.NotIn
        val case_type_not_in: Set<CaseType>? = null,

        @Criteria.Eq
        val client_id: Int? = null,
        @Criteria.IdsIn
        val client_ids: Set<Int>? = null,
        @Criteria.Lte
        val create_at_lte: Instant? = null,
        @Criteria.Gte
        val create_at_gte: Instant? = null,
        @Criteria.Lte
        val start_date_lte: Instant? = null,
        @Criteria.Gte
        val start_date_gte: Instant? = null,
        @Criteria.Expr("{this}.client_id not in ({value})")
        val client_id_not_in: Set<Int>? = null,

        @Criteria.Eq
        val industry: Industry? = null,
        @Criteria.In
        val industry_in: Set<Industry>? = null,
        @Criteria.Eq
        val name: String? = null,
        @Criteria.Contains(columnName = "name")
        val name_like: String? = null,
        @Criteria.Eq
        val outsource_client_name: String? = null,
        @Criteria.Contains
        val outsource_client_name_contains: String? = null,

        @Criteria.Eq
        val code: String? = null,
        @Criteria.In
        val code_in: Set<String>? = null,
        @Criteria.Contains
        val code_contains: String? = null,
        @Criteria.Expr(expr = "({this}.code = '') = #{value}")
        val code_is_blank: Boolean? = null,

        @Criteria.Eq
        val status: Status? = null,
        @Criteria.In
        val status_in: Set<Status>? = null,
        @Criteria.Ne
        val status_ne: Status? = null,

        @Criteria.Eq
        val decipher_id: String? = null,
        @Criteria.Contains
        val external_contacts_for_recording_contains: String? = null,

        @Criteria.Expr(expr = "({this}.status != 'CLOSED') = #{value}")
        val is_active: Boolean? = null,
        @Criteria.Eq
        val is_proactive: Boolean? = null,

        @Criteria.Expr(
            expr = """
            {this}.id not in 
            (select distinct project_id from task_event where create_at >= #{value})
            """
        )
        val inactive_since: Instant? = null,

        @Criteria.Eq
        val sub_type: SubType? = null,
        @Criteria.In
        val sub_type_in: Set<SubType>? = null,
        @Criteria.NotIn
        val sub_type_not_in: Set<SubType>? = null,

        @Criteria.IsNull
        val rate_is_null: Boolean? = null,

        @Criteria.Join
        val client: Client.Query? = null,
        @Criteria.Join(on = "{this}.id = {that}.project_id and {that}.role = 'PROJECT_MANAGER' and {that}.delete_at = 0")
        val manager: ProjectMember.Query? = null,
        @Criteria.Join(on = "{this}.id = {that}.project_id and {that}.client_contact_type = 'PRIMARY'")
        val primary_contact: ProjectClientContact.Query? = null,

        @Criteria.Join
        val member: ProjectMember.Query? = null,
        @Criteria.Join
        val tasks: Task.Query? = null,

        @Criteria.Join
        val task_events: TaskEvent.Query? = null,

        @Criteria.Join
        val project_client_contacts: ProjectClientContact.Query? = null,
        @Criteria.Join(on = "{this.project_client_contacts}.client_contact_id = {that}.id or {this.tasks}.client_contact_id = {that}.id")
        val client_contacts: ClientContact.Query? = null,
        @Criteria.Join
        val outsource_approval: OutsourceProjectApproval.Query? = null,

        @Criteria.Expr(
            """
                {this}.id in (
                    select distinct project_id from project_client_contact 
                    where client_contact_id = #{value}
                    and (delete_at = 0 or delete_at is null)
                    union
                    select distinct project_id from task 
                    where client_contact_id = #{value}
                    and (delete_at = 0 or delete_at is null)
                )
            """
        )
        val client_contact_id: Int? = null,
        @Criteria.Expr(
            """
                {this}.id in (
                    select distinct project_id from project_client_contact 
                    where client_contact_id in ({value})
                    and (delete_at = 0 or delete_at is null)
                    union
                    select distinct project_id from task 
                    where client_contact_id in ({value})
                    and (delete_at = 0 or delete_at is null)
                )
            """
        )
        val client_contact_ids: Set<Int>? = null,

        // legacy
        @Deprecated(message = "use member.user_ids instead")
        val member_ids: Set<Int>? = null,
        @Deprecated(message = "use member.role instead")
        val member_role: ProjectMember.Role? = null,
        @Deprecated(message = "use task.advisor_id instead")
        val advisor_id: Int? = null,
        @Deprecated(message = "use task.advisor_ids instead")
        val advisor_ids: Set<Int>? = null,

        override val includeSoftDeletedRecords: Boolean = false,
        override val aggregation: Set<String>? = null,
        @Criteria.Or
        val or: List<Query>? = null,
    ) : BaseQuery<Project>(), AggregationQuery {

        @Criteria.Join
        val member_for_compatibility: ProjectMember.Query?
            get() {
                return if (member_ids == null && member_role == null) {
                    null
                } else {
                    ProjectMember.Query(
                        user_ids = member_ids,
                        role = member_role
                    )
                }
            }

        @Criteria.Join
        val task_for_compatibility: Task.Query?
            get() {
                return if (advisor_id == null && advisor_ids == null) {
                    null
                } else {
                    Task.Query(
                        advisor_id = advisor_id,
                        advisor_ids = advisor_ids,
                    )
                }
            }
    }
}

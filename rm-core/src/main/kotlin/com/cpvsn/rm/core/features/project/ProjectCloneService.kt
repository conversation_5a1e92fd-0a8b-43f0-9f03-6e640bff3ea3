package com.cpvsn.rm.core.features.project

import com.cpvsn.core.svc.spring.ext.extExecute
import com.cpvsn.core.util.experimental.BeanUtil
import com.cpvsn.crud.model.Includes
import com.cpvsn.crud.model.Patch
import com.cpvsn.rm.core.features.client.blacklist.ClientBlacklistService
import com.cpvsn.rm.core.features.inquiry.InquiryService
import com.cpvsn.rm.core.features.inquiry.model.Inquiry
import com.cpvsn.rm.core.features.inquiry.model.InquiryBranch
import com.cpvsn.rm.core.features.task.Task
import com.cpvsn.rm.core.features.task.TaskCreationService
import com.cpvsn.rm.core.features.task.angle.TaskAngle
import com.cpvsn.rm.core.features.task.angle.TaskAngleMember
import com.cpvsn.rm.core.features.task.angle.TaskAngleService
import com.cpvsn.rm.core.features.task.lead_group.LeadGroup
import com.cpvsn.rm.core.features.task.lead_group.LeadGroupService
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.transaction.support.TransactionTemplate

@Service
class ProjectCloneService {

    private val logger = LoggerFactory.getLogger(this::class.java)

    @Autowired
    private lateinit var transactionTemplate: TransactionTemplate

    @Autowired
    private lateinit var projectCreationService: ProjectCreationService

    @Autowired
    private lateinit var taskCreationService: TaskCreationService

    @Autowired
    private lateinit var taskAngleService: TaskAngleService

    @Autowired
    private lateinit var inquiryService: InquiryService
//
//    @Autowired
//    private lateinit var clientExpertExclusivityService: ClientExpertExclusivityService

    @Autowired
    private lateinit var clientBlacklistService: ClientBlacklistService

    @Autowired
    private lateinit var leadGroupService: LeadGroupService

    data class CloneRequest(
        val from_project: Project,
    )

    /**
     * to clone entities, we can either erase the raw id in the raw instance,
     * then reuse the same instance, pass them to corresponding save API to create new instance.
     * or we create new instances on our own.
     * here, we mix these strategies.
     */
    fun clone(
        from_project_id: Int,
        request: CloneRequest,
        user_id: Int,
    ): Project {
        return transactionTemplate.extExecute {
            val source = request.from_project
            val angles = request.from_project.angles.orEmpty()
            val lead_groups = request.from_project.lead_groups.orEmpty()
            reset_props_for_clone(from_project_id, source)
            // we erase angles here to ensure we won't cascade save angles
            // we will clone angles later
            // If there are no angles to copy, set angles to null in order to create default angles
            // If there are angles to copy, set angles to emptyList() in order to not create default angles
            source.angles = if (source.angles.isNullOrEmpty()) null else emptyList()
            // Do the same with lead_groups because we make them later as well
            source.lead_groups = if (source.lead_groups.isNullOrEmpty()) null else emptyList()
            val project = projectCreationService.save_cascade(source)

            // clone sq
            var cloned_sq_branches = emptyList<InquiryBranch>()
            source.sq?.let {
                val sq = clone_sq(it, user_id)
                Patch.fromMutator(project) {
                    this.sq_id = sq.id
                }.patch()
                cloned_sq_branches = sq.branches.orEmpty()
            }

            // check expert exclusivity to filter out advisors
            val task_advisor_ids = listOf(
                *source.leads.orEmpty().mapNotNull { it.advisor_id }.toTypedArray(),
                *angles.flatMap {
                    it.tasks.orEmpty().mapNotNull { task -> task.advisor_id }
                }.toTypedArray(),
                *lead_groups.flatMap {
                    it.tasks.orEmpty().mapNotNull { task -> task.advisor_id }
                }.toTypedArray()
            ).toSet()

//            val check_result = clientExpertExclusivityService.exclusivity_check(
//                target_client_id = source.client_id.assert_valid_id(),
//                advisor_ids = task_advisor_ids,
//                process = CheckExclusivityProcess.CLONE_PROJECT,
//                project_id = project.id
//            )

            val validate_task_blacklist = request.from_project.client_id?.let {
                clientBlacklistService.validate_task_and_remove(
                    it, task_advisor_ids
                )
            }?.map { it.advisor_id }?.toSet() ?: emptySet()

//            val violation_advisor_ids: Set<Int> = check_result.exclusivity_violations.map {
//                it.advisor_id
//            }.toSet().plus(validate_task_blacklist)
            val violation_advisor_ids = validate_task_blacklist

            request.from_project.leads = request.from_project.leads?.let {
                it.filter { task -> task.advisor_id !in violation_advisor_ids }
            }

            angles.forEach {
                it.tasks = it.tasks.orEmpty().filter { task ->
                    task.advisor_id !in violation_advisor_ids
                }
            }

            lead_groups.forEach {
                it.tasks = it.tasks.orEmpty().filter { task ->
                    task.advisor_id !in violation_advisor_ids
                }
            }

            // clone angles
            project.angles = clone_angles(
                project.id,
                angles,
                user_id,
                cloned_sq_branches,
                clone_tasks = true
            )


            // clone lead groups
            project.lead_groups = leadGroupService.clone_lead_groups(
                project.id,
                lead_groups,
            )

            // if the old project doesn't have lead groups enabled, and the new one does, create a default lead group and put the leads there
            if (project.enable_lead_group && project.lead_groups.isNullOrEmpty() && !request.from_project.leads.isNullOrEmpty()) {
                val new_lead_group = LeadGroup.save(LeadGroup().apply {
                    project_id = project.id
                    name = LeadGroupService.DEFAULT_LEAD_GROUP_NAME
                })
                project.lead_groups = listOf(new_lead_group)
                project.leads?.forEach {
                    it.lead_group_id = new_lead_group.id
                }
            }

            val new_lead_groups_mapped_by_name = project.lead_groups.orEmpty().associateBy { it.name }
            Task.join(
                project.leads.orEmpty(),
                Includes.setOf(Task::lead_group),
            )
            project.leads?.forEach { cur_lead ->
                val original_lead_group_name = cur_lead.lead_group?.name
                val new_lead_group = original_lead_group_name?.let {
                    new_lead_groups_mapped_by_name[it]
                }
                cur_lead.lead_group_id = new_lead_group?.id
            }

            // clone leads
            project.leads = taskCreationService.clone(
                to_project_id = project.id,
                to_angle_id = null,
                tasks = project.leads.orEmpty()
                    .filter { it.advisor_id !in violation_advisor_ids },
                user_id = user_id,
                use_passed_raw_lead_group_id = true,
            )

            project
        }
    }

    /**
     * to reuse the same instances for calling Save API
     * we erase several props here, especially id property, to ensure we only insert, not update
     */
    internal fun reset_props_for_clone(
        from_project_id: Int,
        project: Project,
    ) {
        project.id = 0
        project.tsid = null
        project.clone_from_id = from_project_id

        val list = listOfNotNull(
            *project.notes.orEmpty().toTypedArray(),
            *project.tags.orEmpty().toTypedArray(),
            *project.tickers.orEmpty().toTypedArray(),
            *project.project_client_contacts.orEmpty().toTypedArray(),
            *project.members.orEmpty().toTypedArray(),
        )
        list.forEach { it.id = 0 }

        project.sq_id = 0
        project.sent_close_email_advisor_ids = emptySet()
    }

    internal fun clone_sq(
        sq: Inquiry,
        user_id: Int
    ): Inquiry {
        sq.id = 0
        sq.branches.orEmpty().forEach {
            it.clone_from_id = it.id
            it.id = 0
            it.questions.orEmpty().forEach { inquiryQuestion ->
                inquiryQuestion.id = 0
            }
        }
        return inquiryService.save_cascade(sq)
    }

    fun clone_angles(
        project_id: Int,
        angles: List<TaskAngle>,
        user_id: Int,
        cloned_sq_branches: List<InquiryBranch>,
        clone_tasks: Boolean,
    ): List<TaskAngle> {
        if (angles.isEmpty()) return emptyList()
        return angles.map { raw ->
            val entity = TaskAngle {
                this.clone_from_id = raw.id
                this.project_id = project_id
                this.name = raw.name
                // we will get next rank value at com.cpvsn.rm.core.features.task.angle.TaskAngleRepo#save
                this.rank = 0
                this.rate = raw.rate
                this.rate_currency = raw.rate_currency

                this.inquiry_branch_id =
                    cloned_sq_branches.firstOrNull { inquiryBranch ->
                        inquiryBranch.clone_from_id == raw.inquiry_branch_id
                    }?.id ?: 0

                this.version = 0
                this.next_task_rank = 0

                this.members = raw.members.orEmpty().map {
                    // erase id, then save cascade later
                    val member = BeanUtil.assign(TaskAngleMember(), it)
                    member.id = 0
                    member
                }
            }
            taskAngleService.save_cascade(entity)
            if (clone_tasks) {
                entity.tasks = taskCreationService.clone(
                    project_id,
                    entity.id,
                    raw.tasks.orEmpty(),
                    user_id
                )
            }
            entity
        }
    }


}

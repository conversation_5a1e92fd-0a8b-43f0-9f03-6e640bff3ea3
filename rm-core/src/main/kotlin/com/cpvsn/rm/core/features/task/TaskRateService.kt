package com.cpvsn.rm.core.features.task

import com.cpvsn.core.svc.spring.ext.extExecute
import com.cpvsn.crud.model.Includes
import com.cpvsn.crud.model.Patch
import com.cpvsn.rm.core.features.task.constant.TaskStatus
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.transaction.support.TransactionTemplate

@Service
class TaskRateService {

    private val logger = LoggerFactory.getLogger(this::class.java)

    @Autowired
    private lateinit var taskRepository: TaskRepository

    @Autowired
    private lateinit var transactionTemplate: TransactionTemplate

    fun reevaluate_rate(
        project_id: Int,
    ): List<Task> {
        val tasks = Task.findAll(
            Task.Query(
                project_id = project_id
            )
        )
        return reevaluate_rate(project_id, tasks)
    }

    /**
     * @param angle_id if it is null, we change tasks which have angle_id equals null
     */
    fun reevaluate_rate(
        project_id: Int,
        angle_id: Int?,
    ): List<Task> {
        val tasks = Task.findAll(
            Task.Query(
                project_id = project_id,
                angle_id = angle_id,
                angle_id_is_null = angle_id == null
            )
        )
        return reevaluate_rate(project_id, tasks)
    }

    fun reevaluate_rate_lead_group(
        project_id: Int,
        lead_group_id: Int?,
    ): List<Task> {
        val tasks = Task.findAll(
            Task.Query(
                project_id = project_id,
                lead_group_id = lead_group_id,
                angle_id_is_null = true
            )
        )
        return reevaluate_rate(project_id, tasks)
    }

    fun reevaluate_rate(
        project_id: Int,
        task_ids: Set<Int>,
    ): List<Task> {
        val tasks = Task.findAll(
            Task.Query(
                project_id = project_id,
                ids = task_ids,
            )
        )
        return reevaluate_rate(project_id, tasks)
    }

    /**
     * if a task hasn't "completed" yet
     * its rate should be changed
     * whenever its angle's or project's rate is changed.
     *
     * https://www.notion.so/capvision/Leads-Additional-Sorting-d400f4747830427dba373429c414a0a2
     * https://www.notion.so/capvision/Surveys-Lock-honorarium-based-on-last-outreach-bf1c445afb2447528671bfb168d64e97
     */
    fun reevaluate_rate(
        project_id: Int,
        tasks: List<Task>,
    ): List<Task> {
        transactionTemplate.extExecute {
            val target_tasks = tasks.filter {
                it.general_status != TaskStatus.General.COMPLETED
            }
            Task.join(
                target_tasks, Includes.setOf(
                    Task::default_rate_jit,
                    Task::default_rate_currency_jit,
                )
            )
            val patches = target_tasks.map {
                Patch.fromMutator(it) {
                    it.rate = it.default_rate_jit ?: it.rate
                    it.rate_currency = it.default_rate_currency_jit ?: it.rate_currency
                }
            }

            if (logger.isDebugEnabled) {
                patches.forEach {
                    logger.debug(it.toDescription())
                }
            }

            taskRepository.batchPatch(
                entities = patches.map { it.entity },
                fields = Includes.setOf(
                    Task::rate,
                    Task::rate_currency,
                )
            )
        }
        return tasks
    }

}

package com.cpvsn.rm.core.features.inquiry.model

import com.cpvsn.rm.core.features.inquiry.pojo.DescType
import com.cpvsn.rm.core.features.inquiry.pojo.InquiryQuestionOption

object InquiryInstanceTextFormUtil {
    /**
     * InquiryInstance::plain_text
     */
    fun InquiryInstance.generalTxtForm(): String {
        return this.questions
            .filter { question ->
                !question.is_advisor_job_confirm &&
                        (this.client_publish_question_ids == null || this.client_publish_question_ids!!.contains(
                            question.id
                        ))
            }
            .sortedBy { it.sort_order }
            .mapIndexed { index, question ->
                val order = index + 1
                val answer = construct_answer_text(question, question.answer)
                """
                |Q${order}: ${question.title_text}
                |A${order}: $answer
                """.trimMargin()
            }.joinToString("\n")
    }

    fun InquiryInstance.mckExcelTxtForm(): String {
        return this.questions
            .filter { question ->
                !question.is_advisor_job_confirm &&
                        (this.client_publish_question_ids == null || this.client_publish_question_ids!!.contains(
                            question.id
                        ))
            }
            .sortedBy { it.sort_order }
            .mapIndexed { index, question ->
                val order = index + 1
                val answer = construct_answer_text(question, question.answer)
                "${order}. ${question.title_text} $answer"
            }.joinToString("\n")
    }

    /**
     * ClientGoogleSheetService.convert_qa_to_data
     */
    fun construct_answer_text(
        question: InquiryQuestion,
        answer: InquiryAnswer?,
        generate_html: Boolean = false
    ): String {
        val options = question.options?.map { it.text } ?: emptyList()
        val answer_text = when (question.type) {
            InquiryQuestion.Type.YES_NO -> answer?.radio_selected?.let { options[it] }
            InquiryQuestion.Type.RADIO -> {
                // add explanation to the chosen answer
                val desc = answer?.content?.get("desc")?.toString()
                val selected = answer?.radio_selected?.let { question.options.orEmpty().getOrNull(it) }
                val desc_prompt = selected?.desc_prompt
                selected?.text + if (desc.isNullOrBlank()) "" else {
                    if (selected?.desc_type == DescType.FOLLOW_UP_QUESTION_ANSWER_CHOICE) {
                        " ($desc_prompt => $desc)"
                    } else {
                        " ($desc)"
                    }
                }
            }

            InquiryQuestion.Type.CHECKBOX -> answer?.checkbox_selected?.joinToString(",") { num ->
                // add explanation to the chosen answer
                val desc = (answer.content?.get("desc") as? Map<*, *>)?.get(num.toString())?.toString()
                val selected = question.options.orEmpty().getOrNull(num)
                val desc_prompt = selected?.desc_prompt
                selected?.text + if (desc.isNullOrBlank()) "" else {
                    if (selected?.desc_type == DescType.FOLLOW_UP_QUESTION_ANSWER_CHOICE) {
                        " ($desc_prompt => $desc)"
                    } else {
                        " ($desc)"
                    }
                }
            }

            InquiryQuestion.Type.RANKING_SET -> (answer?.content?.get("result") as? ArrayList<*>)?.joinToString("\n") {
                val option = (it as? Map<*, *>)?.get("option")?.toString()
                val rank = (it as? Map<*, *>)?.get("rank")?.toString()
                val text = (it as? Map<*, *>)?.get("text")?.toString()
                "${option}: $rank $text"
            }.orEmpty()

            InquiryQuestion.Type.NO_RANKING_SET -> (answer?.content?.get("result") as? ArrayList<*>)?.joinToString("\n") {
                val option = (it as? Map<*, *>)?.get("option")?.toString()
                val text = (it as? Map<*, *>)?.get("text")?.toString()
                "${option}: $text"
            }.orEmpty()

            InquiryQuestion.Type.TEXT -> answer?.content?.get("result")?.toString() ?: ""

            InquiryQuestion.Type.MATRIX_CHECKBOX,
            InquiryQuestion.Type.MATRIX_RADIO,
            InquiryQuestion.Type.MATRIX_NUMERIC,
            InquiryQuestion.Type.MATRIX_PERCENTAGE -> {
                val answers = answer?.content?.get("result") as? List<Map<String, Any>> ?: return ""
                if (generate_html) {
                    build_html_matrix(question.options ?: emptyList(), answers, question.type)
                } else {
                    construct_matrix_string(question.options ?: emptyList(), answers, question.type)
                }
            }

            else -> answer?.content?.get("result")?.toString() ?: ""
        }
        return answer_text ?: ""
    }

    /**
     * test matrix radio
     *     column_1    column_2    column_3
     * row_1    Y    N    N
     * row_2    N    Y    N
     * row_3    N    Y    N
     * row_4    N    Y    N
     */
    fun construct_matrix_string(
        options: List<InquiryQuestionOption>,
        answers: List<Map<String, Any>>, // 示例: { row_number: 1, column_number: 1, value: true }
        type: InquiryQuestion.Type
    ): String {
        val matrixColumns = options.filter { it.sub_type == InquiryQuestion.OptionSubType.MATRIX_COLUMN }
            .sortedBy { it.order }

        val columnCount = matrixColumns.size
        val columnTitles = matrixColumns.joinToString("    ") { it.text }

        val matrixRows = options.filter { it.sub_type == InquiryQuestion.OptionSubType.MATRIX_ROW }
            .sortedBy { it.order }

        val answerMap = answers.associate {
            Pair(
                (it["row_number"] as? String)?.toIntOrNull(),
                (it["column_number"] as? String)?.toIntOrNull()
            ) to it["value"]
        }

        val matrixBuilder = StringBuilder()
        matrixBuilder.append("    ").append(columnTitles).append("\n") // 第一行: 列标题

        for (row in matrixRows) {
            val rowNumber = row.order
            // 在一行中，生成每一列的值
            val rowValues = (1..columnCount).joinToString("    ") { colIndex ->
                val res = answerMap[Pair(rowNumber, colIndex)]
                when (type) {
                    InquiryQuestion.Type.MATRIX_CHECKBOX -> {
                        if (res != null) "Y" else "N"
                    }

                    InquiryQuestion.Type.MATRIX_RADIO -> {
                        if (res != null) "Y" else "N"
                    }

                    InquiryQuestion.Type.MATRIX_NUMERIC -> {
                        res?.toString() ?: "_"
                    }

                    InquiryQuestion.Type.MATRIX_PERCENTAGE -> {
                        "${res?.toString() ?: " "}%"
                    }

                    else -> ""
                }
            }
            matrixBuilder.append(row.text).append("    ").append(rowValues).append("\n") // 行头 + 选项
        }

        return matrixBuilder.toString()
    }

    fun build_html_matrix(
        options: List<InquiryQuestionOption>,
        answers: List<Map<String, Any>>,
        type: InquiryQuestion.Type
    ): String {
        // 1. 准备矩阵数据
        val columns = options
            .filter { it.sub_type == InquiryQuestion.OptionSubType.MATRIX_COLUMN }
            .sortedBy { it.order }
            .map { it.text }

        val rows = options
            .filter { it.sub_type == InquiryQuestion.OptionSubType.MATRIX_ROW }
            .sortedBy { it.order }

        val answerMap = answers.associate {
            Pair(
                (it["row_number"] as? String)?.toIntOrNull(),
                (it["column_number"] as? String)?.toIntOrNull()
            ) to it["value"]
        }

        // 2. 构建安全隔离的HTML
        return buildString {
            append(
                """
            <div class='cv-matrix-container' style='
                font-family: Arial, sans-serif;
                margin: 10px 0;
            '>
            <table style='
                border-collapse: collapse;
                width: 100%;
                border: 1px solid #e0e0e0;
            '>
                <thead>
                    <tr>
                        <th style='
                            padding: 8px;
                            border: 1px solid #e0e0e0;
                            background-color: #f5f5f5;
                        '>Row/Column</th>
                        ${
                    columns.joinToString("") { col ->
                        "<th style='padding: 8px; border: 1px solid #e0e0e0; " +
                                "background-color: #f5f5f5;'>$col</th>"
                    }
                }
                    </tr>
                </thead>
                <tbody>
        """.trimIndent()
            )

            // 3. 动态生成行
            rows.forEach { row ->
                append("<tr>")
                append("<td style='padding: 8px; border: 1px solid #e0e0e0;'>${row.text}</td>")

                columns.indices.forEach { colIndex ->
                    val value = answerMap[Pair(row.order, colIndex + 1)]
                    append("<td style='padding: 8px; border: 1px solid #e0e0e0; text-align: center;'>")

                    when (type) {
                        InquiryQuestion.Type.MATRIX_CHECKBOX,
                        InquiryQuestion.Type.MATRIX_RADIO ->
                            append(if (value == true) "✓" else "✗")

                        InquiryQuestion.Type.MATRIX_NUMERIC ->
                            append(value?.toString() ?: "_")

                        InquiryQuestion.Type.MATRIX_PERCENTAGE ->
                            append("${value?.toString() ?: " "}%")

                        else -> append("")
                    }

                    append("</td>")
                }
                append("</tr>")
            }

            // 4. 闭合标签
            append(
                """
                </tbody>
            </table>
            </div>
        """.trimIndent()
            )
        }
    }

    fun construct_email_answer_text(
        question: InquiryQuestion,
        answer: InquiryAnswer
    ): String {
        val options = question.options?.map { it.text } ?: emptyList()
        val answer_text = when (question.type) {
            InquiryQuestion.Type.YES_NO -> answer.radio_selected?.let { options[it] }
            InquiryQuestion.Type.RADIO -> {
                // add explain
                val description = answer.content?.get("desc")?.toString()
                answer.radio_selected?.let { options[it] } + if (description.isNullOrBlank()) "" else ":$description"
            }

            InquiryQuestion.Type.CHECKBOX -> answer.checkbox_selected?.joinToString(",") { num ->
                // add explain
                val description =
                    (answer.content?.get("desc") as? Map<*, *>)?.get(num.toString())?.toString()
                options[num] + if (description.isNullOrBlank()) "" else ":$description"
            }

            InquiryQuestion.Type.TEXT -> answer.content?.get("result")?.toString() ?: ""
            InquiryQuestion.Type.NO_RANKING_SET, InquiryQuestion.Type.RANKING_SET -> (answer.content?.get(
                "result"
            ) as? List<Map<String, String>>)?.joinToString("<br>") { "${it["option"]}: ${it["rank"]?.let { cur_rank -> "${cur_rank} " }} ${it["text"]}" }

            InquiryQuestion.Type.MATRIX_CHECKBOX,
            InquiryQuestion.Type.MATRIX_RADIO,
            InquiryQuestion.Type.MATRIX_NUMERIC,
            InquiryQuestion.Type.MATRIX_PERCENTAGE -> {
                val answers = answer?.content?.get("result") as? List<Map<String, Any>> ?: return ""
                construct_matrix_string(question.options ?: emptyList(), answers, question.type).replace("\n", "<br>")
                    .replace("    ", "&nbsp;&nbsp;&nbsp;&nbsp;")
            }

            else -> ""
        }
        return answer_text.orEmpty()
    }
}

package com.cpvsn.rm.core.features.thirdparty.advisor_information

import com.cpvsn.rm.core.config.listwise.ListWiseEmailValidationRequest
import com.cpvsn.rm.core.config.listwise.ListWiseEmailValidationService
import com.cpvsn.rm.core.features.thirdparty.advisor_information.contact_out.ContactOutService
import com.cpvsn.rm.core.features.thirdparty.advisor_information.rocket_reach.RocketReachService
import com.cpvsn.rm.core.util.biz_error
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.runBlocking
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

// https://www.notion.so/capvision/Scraper-RocketReach-API-Integration-1c823199f41a80998b07f08a7c55ffca?d=1c923199f41a8081a56b001c563867da
@Service
class AdvisorInformationService {

    @Autowired
    private lateinit var contactOutService: ContactOutService

    @Autowired
    private lateinit var rocketReachService: RocketReachService

    @Autowired(required = false)
    private lateinit var listWiseEmailValidationService: ListWiseEmailValidationService

    private val logger = LoggerFactory.getLogger(this::class.java)

    companion object {
        val ALLOWED_RR_EMAIL_REGIONS = listOf(
            "gmail.com", //gmail.com (and international variations)
            "googlemail",
            "gmail",
            "google",
            "yahoo.com",
            "aol.com",
            "live.com",
            "hotmail.com",
            "outlook.com",
            "msn.com",
            "ymail.com",
            "me.com",
            "icloud.com",
            "me.com",
        )
    }

    data class AdvisorInfo(
        val rr_api_call: Boolean = false,
        val rr_domain_preview: List<String> = emptyList(),
        val rr_email_credit_used: Boolean = false,
        val rr_email_results: List<String> = emptyList(),
        val rr_filtered_email_results: List<String> = emptyList(),
        val rr_phone_numbers: List<String> = emptyList(),
        val co_api_call: Boolean = false,
        val co_credit_used: Boolean = false,
        val co_email_results: List<String> = emptyList(),
        val co_phone_results: List<String> = emptyList(),
    )


    fun get_email_and_phone_info_via_linkedin_profile(
        linkedin_profile_url: String
    ): AdvisorInfo {
        val rr_search_people_start = System.currentTimeMillis()
        val rocket_reach_search_result = rocketReachService.search_people(
            linkedin_profile_url = linkedin_profile_url,
        ).profiles.orEmpty().mapNotNull { it.teaser }
        val rr_search_people_end = System.currentTimeMillis()
        logger.info("RR Search People API 调用耗时: ${rr_search_people_end - rr_search_people_start} ms")
        if (rr_search_people_end - rr_search_people_start > 10000) {
            logger.info("too long time url: $linkedin_profile_url")
        }
        val rocket_reach_email_regions =
            (rocket_reach_search_result.map { it.emails.orEmpty() }.flatten()
                    + rocket_reach_search_result.map { it.personal_emails.orEmpty() }.flatten()
                    + rocket_reach_search_result.map { it.professional_emails.orEmpty() }.flatten())
        var contains_allowed_region = false
        rocket_reach_email_regions.forEach { cur_email_region ->
            ALLOWED_RR_EMAIL_REGIONS.forEach {
                if (cur_email_region.toLowerCase().contains(it)) {
                    contains_allowed_region = true
                }
            }
        }
        if (contains_allowed_region) {
            // if contains allowed region, we use rocket reach to get advisor contact info
            val rr_lookup_person_start = System.currentTimeMillis()
            val rocket_reach_lookup_result = rocketReachService.lookup_person(
                linkedin_profile_url = linkedin_profile_url,
            )
            val rr_lookup_person_end = System.currentTimeMillis()
            logger.info("RR Lookup Person API 调用耗时: ${rr_lookup_person_end - rr_lookup_person_start} ms")
            val emails = rocket_reach_lookup_result.emails.mapNotNull { it.email }.distinct()
            val phones = rocket_reach_lookup_result.phones.mapNotNull { it.number }.distinct()
            val rr_filtered_email_results = emails.filter { cur_email ->
                var judge = false
                ALLOWED_RR_EMAIL_REGIONS.forEach {
                    if (cur_email.toLowerCase().contains(it)) {
                        judge = true
                    }
                }
                judge
            }
            val listwise_start_time = System.currentTimeMillis()
            val listwise_passed = runBlocking {
                validateEmailsAsync(emails = rr_filtered_email_results)
            }
            val listwise_end = System.currentTimeMillis()
            logger.info("Listwise API 调用耗时: ${listwise_end - listwise_start_time} ms")

            return if (listwise_passed) {
                AdvisorInfo(
                    rr_api_call = true,
                    rr_domain_preview = rocket_reach_email_regions,
                    rr_email_credit_used = true,
                    rr_email_results = rr_filtered_email_results,
                    rr_filtered_email_results = rr_filtered_email_results,
                    rr_phone_numbers = phones,
                )
            } else {
                call_contact_out_api(
                    linkedin_profile_url,
                    rocket_reach_email_regions = rocket_reach_email_regions,
                    rr_credit_used = true,
                )
            }

        } else {
            // else we still use contact out
            return call_contact_out_api(
                linkedin_profile_url,
                rocket_reach_email_regions = rocket_reach_email_regions,
                rr_credit_used = false,
            )
        }
    }


    private fun call_contact_out_api(
        linkedin_profile_url: String,
        rocket_reach_email_regions: List<String>,
        rr_credit_used: Boolean = false,
    ): AdvisorInfo {
        val cc_start = System.currentTimeMillis()
        val contact_out_result = contactOutService.get_profile_via_linkedin_url(
            linkedin_profile_url = linkedin_profile_url
        )
        val cc_end = System.currentTimeMillis()
        logger.info("Contact Out API 调用耗时: ${cc_end - cc_start} ms")
        var co_email_credit_used: Boolean = true
        if (contact_out_result.status_code != 200) {
            if (contact_out_result.status_code == 404) {
                co_email_credit_used = false
            } else {
                biz_error("error when calling contact out api, status_code:${contact_out_result.status_code}, message:${contact_out_result.message}")
            }
        }
        val emails =
            (contact_out_result.profile?.email.orEmpty() + contact_out_result.profile?.personal_email.orEmpty() + contact_out_result.profile?.work_email.orEmpty()).distinct()
        val phones = contact_out_result.profile?.phone.orEmpty().distinct()
        return AdvisorInfo(
            rr_api_call = true,
            rr_domain_preview = rocket_reach_email_regions,
            rr_email_credit_used = rr_credit_used,
            co_api_call = true,
            co_credit_used = co_email_credit_used,
            co_email_results = emails,
            co_phone_results = phones,
        )

    }

    suspend fun validateEmailsAsync(emails: List<String>): Boolean = coroutineScope {
        try {
            val deferredResults = emails.map { email ->
                async {
                    listWiseEmailValidationService
                        .validate_with_parsed_rsp(
                            ListWiseEmailValidationRequest(email)
                        )
                        .email_status
                }
            }
            val listWiseResult = deferredResults.awaitAll()
            !listWiseResult.none { it !in ListWiseEmailValidationService.unpassed_listwise_status }
        } catch (e: Exception) {
            true
        }
    }


}
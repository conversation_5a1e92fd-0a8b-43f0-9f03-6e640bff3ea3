package com.cpvsn.rm.core.features.inquiry

import com.cpvsn.core.util.extension.remove_prefix
import com.cpvsn.crud.jdbc.batch.JdbcEntityBatchDao
import com.cpvsn.crud.jdbc.batch.JdbcEntityBatchRepo
import com.cpvsn.crud.model.CrudOptions
import com.cpvsn.crud.model.Includes
import com.cpvsn.crud.model.Patch
import com.cpvsn.crud.util.ids
import com.cpvsn.rm.core.base.service.RmBaseRepository
import com.cpvsn.rm.core.features.inquiry.model.Inquiry
import com.cpvsn.rm.core.features.inquiry.model.InquiryBranch
import com.cpvsn.rm.core.features.inquiry.model.InquiryInstance
import com.cpvsn.rm.core.features.inquiry.model.InquiryInstanceTextFormUtil.generalTxtForm
import com.cpvsn.rm.core.features.inquiry.model.InquiryQuestion
import com.cpvsn.rm.core.features.misc.entity_update_log.EntityUpdateLog
import com.cpvsn.rm.core.features.task.Task
import com.cpvsn.rm.core.features.task.TaskService
import com.cpvsn.rm.core.features.task.event.TaskEvent
import com.cpvsn.rm.core.features.task.event.TaskEventService
import com.cpvsn.rm.core.features.task.event.TaskEventType
import com.cpvsn.rm.core.features.task.sq.TaskSqService
import com.cpvsn.rm.core.util.Event
import com.cpvsn.rm.core.util.InvokeUtil
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class InquiryInstanceRepo : RmBaseRepository<InquiryInstance>(), JdbcEntityBatchRepo<Int, InquiryInstance> {
    //region @
    @Autowired
    private lateinit var inquiryBranchService: InquiryBranchService

    @Autowired
    private lateinit var taskService: TaskService

    @Autowired
    private lateinit var taskEventService: TaskEventService
    @Autowired
    private lateinit var taskSqService: TaskSqService
    //endregion

    override val batchDao: JdbcEntityBatchDao<Int, InquiryInstance> by lazy {
        JdbcEntityBatchDao(InquiryInstance::class, dataSource)
    }

    override fun handleJoin(
        list: List<InquiryInstance>,
        include: Set<String>,
    ): List<InquiryInstance> {
        val res = super.handleJoin(list, include)

        if (include.contains(InquiryInstance::plain_text.name)) {
            res.forEach {
                it.plain_text = it.generalTxtForm()
            }
        }

        if (include.contains(InquiryInstance::task.name)) {
            val items = Task.findAll(Task.Query(
                ids = res.filter { it.create_type == InquiryInstance.CreateType.BEFORE_TASK }
                    .ids(InquiryInstance::source_id)
            ), include = include.remove_prefix(InquiryInstance::task.name))
                .associateBy { it.id }
            res.forEach {
                it.task = items[it.source_id]
            }
        }

        if (include.contains(InquiryInstance::client_publish_log.name)) {
            val map = EntityUpdateLog.findAll(
                EntityUpdateLog.Query(
                    topic = EntityUpdateLog.Topic.SQ_PUBLISH.name,
                    entity_ids = res.ids()
                ), include.remove_prefix(InquiryInstance::client_publish_log)
            )
                .groupBy { it.entity_id }

            res.forEach {
                it.client_publish_log = map[it.id].orEmpty().sortedByDescending { log -> log.create_at }
            }
        }

        return res
    }

    @Transactional
    override fun save(entity: InquiryInstance, option: CrudOptions.SaveOption<InquiryInstance>): InquiryInstance {
        val branch = inquiryBranchService.get(entity.branch_id, Includes.setOf(InquiryBranch::questions))
        val questions = branch.questions.orEmpty()
        entity.inquiry_id = branch.inquiry_id
        entity.inquiry_type = branch.inquiry_type

        // take snapshot
        entity.branch_snapshot = branch
        entity.qa_list_snapshot = questions

        // customization
        // https://www.notion.so/capvision/Screening-Questions-Automatic-screener-78ff9e60ae444790a1f7de84a0fe4ba9
        if (entity.inquiry_type == Inquiry.Type.SCREENING) {
            if (questions.any { it.is_advisor_job_confirm }) {
                val client_publish = entity.client_publish_question_ids ?: questions.map { it.id }
                val should_hidden_to_client = questions.filter {
                    it.is_advisor_job_confirm
                }.map { it.id }

                entity.client_publish_question_ids = client_publish.filter {
                    it !in should_hidden_to_client
                }
            }

            update_question_list_based_on_npi_requirement(entity)
        }

        return super.save(entity, option)
    }

    @Transactional
    fun save_copy(entity: InquiryInstance): InquiryInstance {

        val instance =
            super.save(entity.apply { this.is_one_off_question = false }, option = CrudOptions.SaveOption.default())

        if (entity.inquiry_type == Inquiry.Type.SCREENING
            && entity.status in (InquiryInstance.Status.values().toSet() - InquiryInstance.Status.ASSIGNED)
        ) {
            taskSqService.process_recording_consent(instance.qa_list_snapshot, instance.source_id, instance.receiver_id)
            InvokeUtil.trigger(Event.ADVISOR_DOC_CHANGED(advisor_id = entity.receiver_id))
            taskEventService.trigger_event(TaskEvent {
                this.task_id = instance.source_id
                this.type = TaskEventType.SQ_RESPOND
                this.payload_id = instance.id
            })
        }

        return instance
    }

    /**
     * Saves [InquiryInstance]s & triggers the relevant events.
     * When a user copies tasks from one project to another, they have the option to copy the screening questions as well.
     * This function creates several screening question instances to be created. Make sure the `source_id` is set to the
     * new task and the `id` is set to 0.
     * Patches `qa_list_snapshot` because [save] overwrites it - I don't know why, so I won't edit [save].
     * @param entities list of [InquiryInstance] with the `id` set to 0 and the `source_id` set to the new task's ID.
     */
    @Transactional
    fun save_copy_batch(entities: List<InquiryInstance>): List<InquiryInstance> {
        val instance_id_map = entities.associate { Pair(it.source_id, it.qa_list_snapshot) }
        val instances = super.save(entities, option = CrudOptions.SaveOption.default())
        val sq_patches = instances.mapNotNull {
            val old_snapshot = instance_id_map[it.source_id]
            old_snapshot?.let { _ ->
                Patch.of(
                    it.apply {
                        qa_list_snapshot = old_snapshot
                    }, fields = setOf(InquiryInstance::qa_list_snapshot.name)
                )
            }
        }
        super.patch(patches = sq_patches)
        val responded_entities = entities.filter { entity ->
            entity.inquiry_type == Inquiry.Type.SCREENING
                    && entity.status in (InquiryInstance.Status.values().toSet() - InquiryInstance.Status.ASSIGNED)
        }

        responded_entities.forEach { instance ->
            InvokeUtil.trigger(Event.ADVISOR_DOC_CHANGED(advisor_id = instance.receiver_id))

            taskEventService.trigger_event(TaskEvent {
                this.task_id = instance.source_id
                this.type = TaskEventType.SQ_RESPOND
                this.payload_id = instance.id
            })
        }

        return instances
    }

    fun direct_save(entity: InquiryInstance): InquiryInstance {
        return super.save(entity)
    }

    private fun update_question_list_based_on_npi_requirement(
        entity: InquiryInstance
    ) {
        val task = Task.firstOrNull(
            Task.Query(id = entity.source_id),
            null,
            Includes.setOf(Task::angle)
        )
        if (task?.angle?.require_npi_number_question == false) {
            val npi_question = entity.qa_list_snapshot.find { it.tags.contains(InquiryQuestion.Tag.ADVISOR_NPI_CONFIRM) }
            if (npi_question != null) {
                val removed_sort_order = npi_question.sort_order
                entity.qa_list_snapshot = entity.qa_list_snapshot
                    .filter { it.id != npi_question.id } // Remove the ADVISOR_NPI_CONFIRM question from qa_list_snapshot
                    .map { question ->
                        // Adjust sort_order only for questions with a higher sort_order than the removed question
                        if (question.sort_order > removed_sort_order) {
                            question.apply {
                                this.sort_order -= 1
                                this.display_order = this.display_order?.minus(1)
                            }
                        } else {
                            question
                        }
                    }
            }
        } else if (task?.angle?.require_npi_number_question == true) {
            var npi_question = entity.qa_list_snapshot.find { it.tags.contains(InquiryQuestion.Tag.ADVISOR_NPI_CONFIRM) }
            if (npi_question == null) {
                // Create an ADVISOR_NPI_CONFIRM question and add it to qa_list_snapshot end
                npi_question = InquiryQuestion.create_npi_confirm_question().apply {
                    this.sort_order = entity.qa_list_snapshot.size
                    this.display_order = entity.qa_list_snapshot.size + 1
                    this.branch_id = entity.branch_id
                }
                entity.qa_list_snapshot = entity.qa_list_snapshot + npi_question
            }
        }
    }
}

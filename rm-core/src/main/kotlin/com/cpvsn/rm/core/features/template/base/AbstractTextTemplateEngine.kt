package com.cpvsn.rm.core.features.template.base

import org.slf4j.LoggerFactory


abstract class AbstractTextTemplateEngine<T : Any> : TextTemplateEngine<T> {
    protected val pattern = """\{\{([0-9a-zA-Z_]+?)}}""".toRegex()
    protected val logger = LoggerFactory.getLogger(javaClass)

    abstract fun find_placeholder(placeholder: String): TextPlaceHolder<T>?

    override fun process(template: String, model: T): String {
        return pattern.replace(template) { result ->
            val name = result.groupValues[1]
            val placeholder = find_placeholder(name)
                ?: return@replace result.value
            try {
                placeholder.resolve(model)
            } catch (e: IllegalArgumentException) {
                logger.error(e.message, e)
                result.value
            }
        }
    }
}

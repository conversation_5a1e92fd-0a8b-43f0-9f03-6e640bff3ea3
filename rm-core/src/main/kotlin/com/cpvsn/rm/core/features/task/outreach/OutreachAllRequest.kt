package com.cpvsn.rm.core.features.task.outreach

import com.cpvsn.rm.core.features.email.EmailRecordTracking

data class OutreachAllRequest(
    val project_id: Int,
    val email_template_id: Int,
    val only_leads: <PERSON><PERSON><PERSON>,
    /**
     * if this value is null, we send outreach email for all users in the project
     */
    val angle_id: Int? = null,
    val lead_group_id: Int? = null,
    // https://www.notion.so/capvision/Leads-Send-on-Behalf-3166e7eeaf3347c68816631cbf1e9c64
    val represent_user_id: Int? = null,
    val to_leads_vendor: EmailRecordTracking.Provider? = null,
    val bcg_template_id: Int? = null,
)

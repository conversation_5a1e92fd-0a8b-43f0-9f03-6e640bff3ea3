package com.cpvsn.rm.core.features.zoom.meeting.base

import org.springframework.http.HttpMethod


/**
 * @see <a href="https://developers.zoom.us/docs/api/rest/reference/zoom-api/methods/#tag/Meetings">Docs/API/Zoom Meeting API</a>
 */
object ZoomApiEndpoints {

    /**
     *@see kong.unirest.UnirestInstance
     */
    data class Endpoint(
        val url: String,
        val httpMethod: HttpMethod,
    )

    const val BASE_URL = "https://api.zoom.us/v2"
    const val get_access_token = "https://zoom.us/oauth/token"

    fun getMe() = Endpoint(
            url = "/users/me",
            httpMethod = HttpMethod.GET
    )

    //region meeting crud
    fun meetingCreate(
        userId: String,
    ) = Endpoint(
            url = "/users/$userId/meetings",
            httpMethod = HttpMethod.POST
    )

    fun meetingsList(
        userId: String,
    ) = Endpoint(
            "/users/$userId/meetings",
            httpMethod = HttpMethod.GET
    )

    fun getMeeting(
        meetingId: String,
    ) = Endpoint(
        url = "/meetings/$meetingId",
        httpMethod = HttpMethod.GET
    )

    fun updateMeeting(
        meetingId: String,
    ) = Endpoint(
        url = "/meetings/$meetingId",
        httpMethod = HttpMethod.PATCH
    )

    fun deleteMeeting(
        meetingId: String,
    ) = Endpoint(
        url = "/meetings/$meetingId",
        httpMethod = HttpMethod.DELETE
    )

    fun getPastMeetingDetails(
        meetingId: String,
    ) = Endpoint(
        url = "/past_meetings/$meetingId",
        httpMethod = HttpMethod.GET
    )
    //endregion

    //region unused
    fun meetingInviteLinksCreate(
        meetingId: String,
    ) = Endpoint(
        url = "/meetings/$meetingId/invite_links",
        httpMethod = HttpMethod.POST
    )

    fun getMeetingInvitation(
        meetingId: String,
    ) = Endpoint(
        "/meetings/$meetingId/invitation",
        httpMethod = HttpMethod.GET
    )

    fun listMeetingTemplates(
        userId: String,
    ) = Endpoint(
        "/users/$userId/meeting_templates",
        httpMethod = HttpMethod.GET
    )
    //endregion

    //region registration
    fun addMeetingRegistrant(
        meetingId: String
    ) = Endpoint(
        url = "/meetings/$meetingId/registrants",
        httpMethod = HttpMethod.POST
    )

    fun listMeetingRegistrants(
        meetingId: String
    ) = Endpoint(
        url = "/meetings/$meetingId/registrants",
        httpMethod = HttpMethod.GET
    )

    fun updateMeetingRegistrantQuestion(
        meetingId: String
    ) = Endpoint(
        url = "/meetings/$meetingId/registrants/questions",
        httpMethod = HttpMethod.PATCH
    )

    fun updateRegistrantStatus(
        meetingId: String
    ) = Endpoint(
        url = "/meetings/$meetingId/registrants/status",
        httpMethod = HttpMethod.PUT
    )
    //endregion


    //region participant
    fun getPastMeetingParticipants(
        meetingId: String,
    ) = Endpoint(
        url = "/past_meetings/$meetingId/participants",
        httpMethod = HttpMethod.GET
    )
    //endregion

    //region meeting recording
    fun updateMeetingStatus(
        meetingId: String,
    ) = Endpoint(
        url = "/meetings/$meetingId/status",
        httpMethod = HttpMethod.PUT
    )

    fun getMeetingRecordings(
        meetingId: String,
    ) = Endpoint(
        url = "/meetings/$meetingId/recordings",
        httpMethod = HttpMethod.GET
    )

    fun listAllRecordings(
        from: String,
    ) = Endpoint(
        url = "/accounts/me/recordings?from=$from&page_size=300",
        httpMethod = HttpMethod.GET
    )

    fun deleteMeetingRecordingFile(
        meetingId: String,
        recordingId: String,
    ) = Endpoint(
        url = "/meetings/$meetingId/recordings/$recordingId?action=trash",
        httpMethod = HttpMethod.DELETE
    )

    fun deleteMeetingRecordings(
        meetingId: String,
    ) = Endpoint(
        url = "/meetings/$meetingId/recordings?action=trash",
        httpMethod = HttpMethod.DELETE
    )

    fun recoverSingleRecording(
        meetingId: String,
        recordingId: String,
    ) = Endpoint(
        url = "/meetings/$meetingId/recordings/$recordingId/status",
        httpMethod = HttpMethod.PUT
    )

    fun recoverMeetingRecordings(
        meetingUUID: String,
    ) = Endpoint(
        url = "/meetings/$meetingUUID/recordings/status",
        httpMethod = HttpMethod.PUT
    )
    //endregion

    //region dashboard api (Bussiness Plan)

    // https://developers.zoom.us/docs/api/rest/reference/zoom-api/methods/#operation/dashboardMeetings
    fun dashboardMeetings(
        type: String = "live",
    ) = Endpoint(
            url = "/metrics/meetings",
            httpMethod = HttpMethod.GET
    )

    // https://developers.zoom.us/docs/api/rest/reference/zoom-api/methods/#operation/dashboardMeetingParticipants
    fun dashboardMeetingParticipants(
        meetingId: String,
        type: String = "live",
    ) = Endpoint(
            url = "/metrics/meetings/$meetingId/participants?type=$type",
            httpMethod = HttpMethod.GET
    )
    //endregion

    //region user
    fun listUsers(
        page_size: Int
    ) = Endpoint(
        url = "/users?page_size=$page_size",
        httpMethod = HttpMethod.GET
    )

    fun getUser(
        userId: String
    ) = Endpoint(
        url = "/users/$userId",
        httpMethod = HttpMethod.GET
    )
    //endregion

    //region report
    fun getMeetingParticipantReport(
        meetingId: String
    ) = Endpoint(
        url = "/report/meetings/$meetingId/participants",
        httpMethod = HttpMethod.GET
    )
    //endregion

    fun inMeetingControl(
        meetingId: String
    ) = Endpoint(
        url = "/live_meetings/$meetingId/events",
        httpMethod = HttpMethod.PATCH
    )
}
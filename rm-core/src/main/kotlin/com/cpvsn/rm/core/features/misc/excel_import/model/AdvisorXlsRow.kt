package com.cpvsn.rm.core.features.misc.excel_import.model

import com.cpvsn.core.base.entity.BaseQuery
import com.cpvsn.core.util.delegates.PropDelegates
import com.cpvsn.crud.jdbc.batch.JdbcEntityBatchDao
import com.cpvsn.crud.jdbc.batch.JdbcEntityBatchRepo
import com.cpvsn.crud.orm.annotation.Column
import com.cpvsn.crud.orm.annotation.PatchFields
import com.cpvsn.crud.orm.annotation.Relation
import com.cpvsn.crud.query.Criteria
import com.cpvsn.rm.core.base.entity.RmCompanion
import com.cpvsn.rm.core.base.entity.RmEntity
import com.cpvsn.rm.core.base.service.RmBaseRepository
import com.cpvsn.rm.core.features.advisor.Advisor
import com.cpvsn.rm.core.features.misc.contact_info.ContactInfo
import com.cpvsn.rm.core.util.ExcelColumn
import com.fasterxml.jackson.annotation.JsonIgnore
import org.springframework.stereotype.Repository

class AdvisorXlsRow : RmEntity() {
    companion object : RmCompanion<AdvisorXlsRow>()


    @ExcelColumn(naturalIndex = 17)
    @Column
    var batch: String = ""

    @ExcelColumn(naturalIndex = 1)
    @Column
    var row_num: Int = 0

    @ExcelColumn(naturalIndex = 5)
    @Column
    var name: String = ""

    @ExcelColumn(naturalIndex = 6)
    @Column
    var background: String = ""

    @ExcelColumn(naturalIndex = 7)
    @Column
    var location_str: String = ""

    @ExcelColumn(naturalIndex = 8)
    @Column
    var sourced_by_name: String = ""

    @Column
    var user_id: Int? = null

    //region job
    @ExcelColumn(naturalIndex = 10)
    @Column
    var company: String = ""

    @Column
    @ExcelColumn(naturalIndex = 11)
    var position: String = ""

    @Column
    @ExcelColumn(naturalIndex = 12)
    var start_date: String = ""

    @Column
    @ExcelColumn(naturalIndex = 13)
    var end_date: String = ""
    //endregion

    //region contact_info
    @Column
    @ExcelColumn(naturalIndex = 14)
    var linkedin_url: String = ""

    @Column
    @ExcelColumn(naturalIndex = 15)
    var emails_str: String = ""

    @Column
    @ExcelColumn(naturalIndex = 16)
    var phones_str: String = ""
    //endregion

    @Column
    @ExcelColumn(naturalIndex = 9)
    var target_project_id: Int? = null

    /**
     * 不为null,则此条纪录的联系方式已与库里专家重复。无法导入
     */
    @Column
    @ExcelColumn(naturalIndex = 2)
    var duplicate_advisor_id: Int? = null

    @Column
    var imported_company_id: Int? = null

    @Column
    @ExcelColumn(naturalIndex = 3)
    var imported_advisor_id: Int? = null

    @Column
    @ExcelColumn(naturalIndex = 4)
    var imported_task_id: Int? = null

    @Column
    @ExcelColumn(naturalIndex = 16)
    var is_linkedin_premium: Boolean? = null

    @Column
    @ExcelColumn(naturalIndex = 17)
    var lead_group_name: String? = null

    //region +

    @PatchFields(["emails_str"])
    var emails: List<String> by PropDelegates.comma_list(this::emails_str) {
        it
    }

    @PatchFields(["phones_str"])
    var phones: List<String> by PropDelegates.comma_list(this::phones_str) {
        it
    }

    @PatchFields(["location_str"])
    var locations: List<String> by PropDelegates.comma_list(this::location_str) {
        it
    }

    @Relation(reference = "imported_advisor_id")
    var imported_advisor: Advisor? = null

    @get:JsonIgnore
    val personal_contact_infos: List<String> by lazy {
        listOf(
            *emails.toTypedArray(),
            *phones.toTypedArray(),
            linkedin_url
        )
    }

    @get:JsonIgnore
    val result_advisor_id: Int?
        get() = duplicate_advisor_id ?: imported_advisor_id

    fun generateContactInfoEntities(): List<ContactInfo> {
        val entities = emails.mapIndexed { i, s ->
            ContactInfo {
                owner_type = ContactInfo.OwnerType.ADVISOR
                type = ContactInfo.Type.EMAIL
                value = s
                is_main = i == 0
            }
        } + phones.mapIndexed { i, s ->
            ContactInfo {
                owner_type = ContactInfo.OwnerType.ADVISOR
                type = ContactInfo.Type.PHONE
                value = s
                is_main = i == 0
            }
        } + ContactInfo {
            owner_type = ContactInfo.OwnerType.ADVISOR
            type = ContactInfo.Type.LINKEDIN_URL
            value = linkedin_url
            is_main = true
        }
        return entities
    }
    //endregion


    data class Query(
        @Criteria.Eq
        val id: Int? = null,
        @Criteria.IdsIn
        val ids: Set<Int>? = null,
        @Criteria.Gte
        val id_gte: Int? = null,
        @Criteria.Lte
        val id_lte: Int? = null,

        @Criteria.Eq
        val row_num: Int? = null,
        @Criteria.IdsIn
        val row_nums: Set<Int>? = null,
        @Criteria.Gte
        val row_num_gte: Int? = null,
        @Criteria.Lte
        val row_num_lte: Int? = null,

        @Criteria.Eq
        val batch: String? = null,
        @Criteria.IsNull
        val user_id_is_null: Boolean? = null,

        @Criteria.Contains
        val name_contains: String? = null,
        @Criteria.Contains
        val linkedin_url_contains: String? = null,

        @Criteria.Eq
        val target_project_id: Int? = null,
        @Criteria.IdsIn
        val target_project_ids: Set<Int>? = null,

        @Criteria.IsNull
        val target_project_id_is_null: Boolean? = null,
        @Criteria.IsNull
        val duplicate_advisor_id_is_null: Boolean? = null,
        @Criteria.IsNull
        val imported_company_id_is_null: Boolean? = null,
        @Criteria.IsNull
        val imported_advisor_id_is_null: Boolean? = null,
        @Criteria.IsNull
        val imported_task_id_is_null: Boolean? = null,
    ) : BaseQuery<AdvisorXlsRow>()

    @Repository
    class Repo : RmBaseRepository<AdvisorXlsRow>(), JdbcEntityBatchRepo<Int, AdvisorXlsRow> {
        override val batchDao: JdbcEntityBatchDao<Int, AdvisorXlsRow> by lazy {
            JdbcEntityBatchDao(AdvisorXlsRow::class, dataSource)
        }
    }
}
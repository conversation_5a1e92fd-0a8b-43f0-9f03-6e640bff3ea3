package com.cpvsn.rm.core.features.inquiry.model

import com.cpvsn.core.base.entity.BaseQuery
import com.cpvsn.crud.orm.annotation.Column
import com.cpvsn.crud.orm.annotation.PatchFields
import com.cpvsn.crud.orm.entity.SoftDeletable
import com.cpvsn.crud.query.Criteria
import com.cpvsn.rm.core.annotation.DocConstant
import com.cpvsn.rm.core.base.entity.RmCompanion
import com.cpvsn.rm.core.base.entity.RmEntity
import com.cpvsn.rm.core.features.inquiry.pojo.InquiryQuestionOption
import com.cpvsn.rm.core.util.JsonUtil
import com.fasterxml.jackson.annotation.JsonIgnore
import org.jsoup.Jsoup
import java.time.Instant

class InquiryQuestion : RmEntity(), SoftDeletable {
    companion object : RmCompanion<InquiryQuestion>() {
        fun create_record_consent_question(): InquiryQuestion {
            return InquiryQuestion().apply {
                this.title =
                    "<div style=\"font-family: Calibri;font-size: 11pt\">If a call is scheduled with our client, do you consent to recording for their internal note taking purposes?</div>"
                this.tags = listOf(Tag.CALL_RECORDING_CONSENT)
                this.type = Type.RADIO
                this.options = listOf(
                    InquiryQuestionOption().apply {
                        this.text = "I Consent"
                        this.value = "I Consent"
                    },
                    InquiryQuestionOption().apply {
                        this.text = "I do not Consent"
                        this.value = "I do not Consent"
                    }
                )
                this.is_required = true
            }
        }

        fun create_npi_confirm_question(): InquiryQuestion {
            return InquiryQuestion().apply {
                this.type = Type.TEXT
                this.title =
                    "<div style=\"font-size: 11pt; font-family: Calibri\">Please provide your National Provider Identifier below:</div>"
                this.tags = listOf(Tag.ADVISOR_NPI_CONFIRM)
                this.is_required = true
            }
        }
    }

    @Column
    var branch_id: Int = 0

    @Column
    var sort_order: Int = 0

    @Column
    var display_order: Int? = null

    @Column
    var type: Type = Type.YES_NO

    @Column
    var title: String = ""

    @Column
    var is_required: Boolean = true

    @Column
    var level: Int = 0     // 1 if it is sub question under another question

    @Column
    var parent_id: Int? = null

    @Column
    @JsonIgnore
    var content_json: String? = null

    @Column
    var tags_str: String = ""

    var tags: List<Tag> = emptyList()
        get() = tags_str.split(",")
            .filter { it.isNotBlank() }
            .map { Tag.valueOf(it) }
        set(value) {
            this.tags_str = value.joinToString(",") { it.name }
            field = value
        }

    @Column
    @JsonIgnore
    var options_json: String? = null

    override var delete_at: Instant? = null

    @PatchFields(["content_json"])
    var content: Map<String, Any?>?
        get() = content_json?.let {
            JsonUtil.parse(it)
        }
        set(map) {
            content_json = map?.let { JsonUtil.stringify(it) }
        }

    @PatchFields(["options_json"])
    var options: List<InquiryQuestionOption>?
        get() = options_json?.let {
            JsonUtil.parse(it)
        }
        set(list) {
            options_json = list?.let { JsonUtil.stringify(it) }
        }

    //region +
    @get:JsonIgnore
    val is_option_question: Boolean
        get() = type in setOf(Type.YES_NO, Type.RADIO, Type.CHECKBOX)
    val title_text: String?
        get() = Jsoup.parse(title).text()

    // automatically generated SQ should not be published to the client
    @get:JsonIgnore
    val is_advisor_job_confirm: Boolean
        get() {
            val included_tags = setOf(
                Tag.ADVISOR_JOB_CONFIRM,
                Tag.ADVISOR_NPI_CONFIRM,
                Tag.CALL_RECORDING_CONSENT,
            )
            return tags.any { included_tags.contains(it) }
        }
    var sub_questions: List<InquiryQuestion>? = null


    var answer: InquiryAnswer? = null

    var is_one_off_added_question: Boolean? = false

    // tmp field for judgement
    var need_client_show: Boolean? = null

    //endregion

    @DocConstant(displayNameFieldName = "value")

    enum class Type(val value: String) {
        YES_NO("yes/ no"),
        RADIO("单选"),
        CHECKBOX("多选"),
        NUMERIC("数字回答"),
        TEXT("文字回答"),
        TEXT_DESCRIPTION("文字描述"),
        RANKING_WITH_EXPLANATION("带解释的ranking"),
        RANKING_SET("一组ranking问题"),
        NO_RANKING_SET("ranking question without the ranking field"),
        MATRIX_CHECKBOX("MATRIX_CHECKBOX"),
        MATRIX_RADIO("MATRIX_RADIO"),
        MATRIX_NUMERIC("MATRIX_NUMERIC"),
        MATRIX_PERCENTAGE("MATRIX_PERCENTAGE"),
    }

    enum class OptionType {
        ALLOW,
        DENY,
        HOLD
    }

    enum class OptionSubType {
        COMMON,
        MATRIX_ROW,
        MATRIX_COLUMN
    }

    enum class Tag {
        ADVISOR_JOB_CONFIRM,
        REQUIRE_ADVISOR_INPUT_FULLNAME,
        ADVISOR_NPI_CONFIRM,
        CALL_RECORDING_CONSENT,
        COMPLIANCE_QUESTION
    }

    data class Query(
        @Criteria.Eq
        var id: Int? = null,
        @Criteria.IdsIn
        var ids: Set<Int>? = null,
        @Criteria.Eq
        var parent_id: Int? = null,
        @Criteria.IdsIn
        var parent_ids: Set<Int>? = null,
        @Criteria.Eq
        var branch_id: Int? = null,
        @Criteria.IdsIn
        var branch_ids: Set<Int>? = null,

        override var includeSoftDeletedRecords: Boolean = false
    ) : BaseQuery<InquiryQuestion>()
}

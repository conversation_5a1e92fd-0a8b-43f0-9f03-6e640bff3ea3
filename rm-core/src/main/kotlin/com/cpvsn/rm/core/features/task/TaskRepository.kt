package com.cpvsn.rm.core.features.task

import com.cpvsn.core.svc.spring.CoreAppContextHolder
import com.cpvsn.core.util.extension.BD
import com.cpvsn.core.util.extension.add_prefix
import com.cpvsn.core.util.extension.remove_prefix
import com.cpvsn.crud.jdbc.batch.JdbcEntityBatchDao
import com.cpvsn.crud.jdbc.batch.JdbcEntityBatchRepo
import com.cpvsn.crud.model.CrudOptions
import com.cpvsn.crud.model.Includes
import com.cpvsn.crud.model.Patch
import com.cpvsn.crud.model.dot
import com.cpvsn.crud.pagination.Page
import com.cpvsn.crud.pagination.PageRequest
import com.cpvsn.crud.pagination.Sort
import com.cpvsn.crud.util.*
import com.cpvsn.rm.core.base.agg.AggregationService
import com.cpvsn.rm.core.base.service.RmBaseRepository
import com.cpvsn.rm.core.config.oversea.OverSeaDbRouter
import com.cpvsn.rm.core.extensions.contains_any
import com.cpvsn.rm.core.extensions.legacy_ids
import com.cpvsn.rm.core.features.advisor.Advisor
import com.cpvsn.rm.core.features.advisor.AdvisorService
import com.cpvsn.rm.core.features.advisor.custom_source.AdvisorCustomSourceService
import com.cpvsn.rm.core.features.advisor.tag.psq.AdvisorTagMapProjectSqService
import com.cpvsn.rm.core.features.client.Client
import com.cpvsn.rm.core.features.client.blacklist.ClientBlacklist
import com.cpvsn.rm.core.features.client.blacklist.ClientBlacklistService
import com.cpvsn.rm.core.features.compliance.tc.advisor.AdvisorTc
import com.cpvsn.rm.core.features.contract.ContractCalcService
import com.cpvsn.rm.core.features.contract.ContractService
import com.cpvsn.rm.core.features.email_template.constant.EmailTemplatePlaceholder
import com.cpvsn.rm.core.features.email_template.models.PlaceholderBasedModel
import com.cpvsn.rm.core.features.finance.advisor_payment.local.LocalAdvisorPayment
import com.cpvsn.rm.core.features.finance.payment.PaymentItem
import com.cpvsn.rm.core.features.finance.payment.PaymentTopic
import com.cpvsn.rm.core.features.finance.revenue.Revenue
import com.cpvsn.rm.core.features.inquiry.model.Inquiry
import com.cpvsn.rm.core.features.inquiry.model.InquiryInstance
import com.cpvsn.rm.core.features.misc.communication.CommunicationRecord
import com.cpvsn.rm.core.features.misc.entity_update_log.EntityUpdateLog
import com.cpvsn.rm.core.features.misc.loopup.LoopUpService
import com.cpvsn.rm.core.features.misc.loopup.LoopupNumber
import com.cpvsn.rm.core.features.misc.loopup.LoopupRoom
import com.cpvsn.rm.core.features.misc.schedule.Schedule
import com.cpvsn.rm.core.features.portal.PortalType
import com.cpvsn.rm.core.features.portal.advisor.PortalAdvisor
import com.cpvsn.rm.core.features.portal.common.Portal
import com.cpvsn.rm.core.features.project.Project
import com.cpvsn.rm.core.features.project.transaction.ReceivableTransaction
import com.cpvsn.rm.core.features.task.arrange.TaskArrangement
import com.cpvsn.rm.core.features.task.arrange.enums.ArrangementType
import com.cpvsn.rm.core.features.task.ca.TaskCa
import com.cpvsn.rm.core.features.task.ca.TaskCaType
import com.cpvsn.rm.core.features.task.constant.TaskStatus
import com.cpvsn.rm.core.features.task.decline.TaskAdvisorDeclineService
import com.cpvsn.rm.core.features.task.event.TaskEvent
import com.cpvsn.rm.core.features.task.event.TaskEventType
import com.cpvsn.rm.core.features.task.outreach.TaskOutreachUnsubscribeService
import com.cpvsn.rm.core.features.task.outreach.TaskSurveyOutreachService
import com.cpvsn.rm.core.features.task.task_communication.TaskCommunication
import com.cpvsn.rm.core.util.PageUtil
import com.cpvsn.rm.core.util.biz_error
import org.jsoup.Jsoup
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Repository
import org.springframework.transaction.annotation.Transactional
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.Instant


@Repository
class TaskRepository : RmBaseRepository<Task>(), AggregationService,
    JdbcEntityBatchRepo<Int, Task> {

    override val batchDao: JdbcEntityBatchDao<Int, Task> by lazy {
        JdbcEntityBatchDao(Task::class, dataSource)
    }

    //region @
    @Autowired
    override lateinit var dao: Task.MybatisMapper

    @Autowired
    override lateinit var aggregation_mapper: Task.MybatisMapper

    @Autowired
    private lateinit var contractService: ContractService

    @Autowired
    private lateinit var contractCalcService: ContractCalcService

    @Autowired
    private lateinit var loopUpService: LoopUpService

    @Autowired
    private lateinit var advisorService: AdvisorService

    @Autowired
    private lateinit var advisorTagMapProjectSqService: AdvisorTagMapProjectSqService

    @Autowired
    private lateinit var clientBlacklistService: ClientBlacklistService

    @Autowired
    private lateinit var taskApprovalService: TaskApprovalService

    @Autowired
    private lateinit var taskOutreachUnsubscribeService: TaskOutreachUnsubscribeService

    @Autowired
    private lateinit var taskSurveyOutreachService: TaskSurveyOutreachService

    @Autowired
    private lateinit var advisorCustomSourceService: AdvisorCustomSourceService

    //endregion

    @Transactional
    fun listByCaculatedSort(
        tasks: List<Task>,
        pageRequest: PageRequest,
        extra: Includes?
    ): Page<Task> {
        val calculatedSortFields: Set<String> = setOf(
            Task::rate_usd_approximate.name
        )
        val orders = pageRequest.sort?.orders.orEmpty()
        val order1 = orders.firstOrNull()

        // 暂时只考虑作为唯一sort字段的情况
        // val containsCaculatedSort = orders.map { it.name }.toSet()
        //    .intersect(calculatedSortFields).isNotEmpty()
        val containsCaculatedSort = order1?.name in calculatedSortFields

        // if it can be sorted in SQL
        if (order1 == null || !containsCaculatedSort) {
            val page = PageUtil.page(tasks, pageRequest.page, pageRequest.size)
            return page
        }

        val selector: (Task) -> Comparable<*> = when (order1.name) {
            Task::rate_usd_approximate.name -> { task ->
                task.rate_usd_approximate ?: 0.BD
            }

            else -> error("sorter unsupported")
        }
        val items = if (order1.direction == Sort.Direction.ASC) {
            tasks.sortedWith(compareBy(selector))
        } else {
            tasks.sortedWith(compareByDescending(selector))
        }

        // pagination & join
        val page = PageUtil.page(items, pageRequest.page, pageRequest.size)
        return page
    }

    @Transactional
    override fun handleJoin(
        list: List<Task>,
        include: Set<String>
    ): List<Task> {
        val res = super.handleJoin(list, include)

        if (include.contains(Task::pre_ca_list.name)) {
            val items = TaskCa.findAll(
                TaskCa.Query(
                    task_ids = res.ids(),
                    type = TaskCaType.PRE_CALL,
                ), include = include.remove_prefix(Task::pre_ca_list.name)
            )
            res join items on Task::id eq TaskCa::task_id intoList Task::pre_ca_list
        }

        if (include.contains(Task::post_ca_list.name)) {
            val items = TaskCa.findAll(
                TaskCa.Query(
                    task_ids = res.ids(),
                    type = TaskCaType.POST_CALL,
                ), include = include.remove_prefix(Task::post_ca_list.name)
            )
            res join items on Task::id eq TaskCa::task_id intoList Task::post_ca_list
        }

        if (include.contains(Task::latest_pre_ca.name)) {
            val map = TaskCa.findAll(
                TaskCa.Query(
                    task_ids = res.ids(),
                    type = TaskCaType.PRE_CALL,
                ), include = include.remove_prefix(Task::latest_pre_ca.name)
            )
                .groupBy { it.task_id }
                .mapValues { (_, v) ->
                    v.maxByOrNull {
                        // latest_pre_ca should point to the latest created ca
                        // -- request by Viky
                        it.id
                    }!!
                }
            res.forEach {
                it.latest_pre_ca = map[it.id]
            }
        }

        if (include.contains(Task::latest_submitted_pre_ca.name)) {
            val map = TaskCa.findAll(
                TaskCa.Query(
                    task_ids = res.ids(),
                    submit_time_is_null = false,
                    type = TaskCaType.PRE_CALL,
                ),
                include = include.remove_prefix(Task::latest_submitted_pre_ca.name)
            )
                .groupBy { it.task_id }
                .mapValues { (_, v) ->
                    v.maxByOrNull {
                        it.submit_time ?: Instant.EPOCH
                    }!!
                }
            res.forEach {
                it.latest_submitted_pre_ca = map[it.id]
            }
        }

        if (include.contains(Task::latest_submitted_post_ca.name)) {
            val items = TaskCa.findAll(
                TaskCa.Query(
                    task_ids = res.legacy_ids(),
                    submit_time_is_null = false,
                    type = TaskCaType.POST_CALL,
                ),
                include = include.remove_prefix(Task::latest_submitted_post_ca.name)
            ).groupBy { it.task_id }
                .map { entry ->
                    entry.value.maxByOrNull {
                        it.submit_time ?: Instant.EPOCH
                    }!!
                }
                .toList()
            res join items on Task::id eq TaskCa::task_id into Task::latest_submitted_post_ca
        }

        if (include.contains(Task::latest_sq.name)) {
            val items = InquiryInstance.findAll(
                InquiryInstance.Query(
                    create_type = InquiryInstance.CreateType.BEFORE_TASK,
                    source_ids = res.ids(),
                    inquiry_type = Inquiry.Type.SCREENING,
                ), include = include.remove_prefix(Task::latest_sq.name)
            )
                .groupBy { it.source_id }
                .map { entry -> entry.value.maxByOrNull { it.id }!! }
                .toList()
            res join items on Task::id eq InquiryInstance::source_id into Task::latest_sq
        }

        if (include.contains(Task::sq_instances.name)) {
            val map = InquiryInstance.findAll(
                InquiryInstance.Query(
                    create_type = InquiryInstance.CreateType.BEFORE_TASK,
                    source_ids = res.ids(),
                    inquiry_type = Inquiry.Type.SCREENING,
                ), include.remove_prefix(Task::sq_instances.name)
            )
                .groupBy { it.source_id }
                .mapValues {
                    it.value.sortedByDescending { i -> i.create_at }
                }
            res.forEach {
                it.sq_instances = map[it.id].orEmpty()
            }
        }

        if (include.contains(Task::latest_submitted_sq.name)) {
            val items = InquiryInstance.findAll(
                InquiryInstance.Query(
                    create_type = InquiryInstance.CreateType.BEFORE_TASK,
                    source_ids = res.ids(),
                    inquiry_type = Inquiry.Type.SCREENING,
                    status_ne = InquiryInstance.Status.ASSIGNED
                ), include = include.remove_prefix(Task::latest_submitted_sq.name)
            )
                // get latest submitted instance
                .groupBy { it.source_id }
                .map { entry ->
                    entry.value.maxByOrNull {
                        it.submit_time ?: Instant.EPOCH
                    }!!
                }
                .toList()
            res join items on Task::id eq InquiryInstance::source_id into Task::latest_submitted_sq
        }

        if (include.contains(Task::bound_contracts.name)) {
            // it's OK cause we won't list tasks with contracts, extra=contracts would only be used in one_detail
            res.forEach {
                it.bound_contracts =
                    contractService.resolve_task_binding_by_time_match(it.id)
                        ?.let { contract -> listOf(contract) }
                        ?: emptyList()
            }
        }

        if (include.contains(Task::schedule.name)) {
            val items = Schedule.findAll(
                Schedule.Query(
                    task_ids = res.ids(),
                    creator_type = Schedule.Role.PM
                ), include.remove_prefix(Task::schedule)
            )
            res join items on Task::id eq Schedule::task_id into Task::schedule
        }

        /**
         * we use advisor id instead of task id to find schedules here
         * because of the 2021.12.22 email
         * "can we set the system so expert availability stores on the expert’s profile instead of on a project to project basis?"
         */
        if (include.contains(Task::advisor_schedules.name)) {
            val map = Schedule.findAll(
                Schedule.Query(
                    advisor_ids = res.ids(Task::advisor_id),
//                    task_ids = res.ids(),
                    creator_type = Schedule.Role.ADVISOR
                ), include.remove_prefix(Task::advisor_schedules)
            ).groupBy { it.advisor_id }
            res.forEach {
                it.advisor_schedules = map[it.advisor_id].orEmpty()
            }
        }

        if (include.contains(Task::advisor_schedules_with_arranged.name)) {
            val map = Schedule.findAll(
                Schedule.Query(
                    advisor_ids = res.ids(Task::advisor_id),
                    creator_type_in = setOf(Schedule.Role.ADVISOR, Schedule.Role.PM)
                ), include.remove_prefix(Task::advisor_schedules_with_arranged)
            ).groupBy { it.advisor_id }
            res.forEach {
                it.advisor_schedules_with_arranged = map[it.advisor_id].orEmpty()
            }
        }

        if (include.contains(Task::advisor_schedules_after_current.name)) {
            val map = Schedule.findAll(
                Schedule.Query(
                    advisor_ids = res.ids(Task::advisor_id),
//                    task_ids = res.ids(),
                    creator_type = Schedule.Role.ADVISOR,
                    end_time_gt = Instant.now(),
                ), include.remove_prefix(Task::advisor_schedules_after_current)
            ).groupBy { it.advisor_id }
            res.forEach {
                it.advisor_schedules_after_current = map[it.advisor_id].orEmpty()
            }
        }

        if (include.contains(Task::non_conflicting_advisor_schedules_after_current.name)) {
            val map = Schedule.findAll(
                Schedule.Query(
                    advisor_ids = res.ids(Task::advisor_id),
                    creator_type_in = setOf(Schedule.Role.ADVISOR, Schedule.Role.PM),
                    end_time_gt = Instant.now(),
                ), include.remove_prefix(Task::non_conflicting_advisor_schedules_after_current)
            ).groupBy { it.advisor_id }

            res.forEach { task ->
                task.non_conflicting_advisor_schedules_after_current =
                    generate_non_conflicting_schedules(
                        available_schedule = map[task.advisor_id]
                            .orEmpty()
                            .filter {
                                it.creator_type == Schedule.Role.ADVISOR
                                        && it.start_time != null
                                        && it.end_time != null
                            },
                        arranged_schedule = map[task.advisor_id]
                            .orEmpty()
                            .filter {
                                it.creator_type == Schedule.Role.PM
                                        && it.start_time != null
                                        && it.end_time != null
                            },
                    )
            }
        }

        if (include.contains(Task::contact_schedules.name)) {
            val map = Schedule.findAll(
                Schedule.Query(
                    task_ids = res.ids(),
                    creator_type = Schedule.Role.CLIENT_CONTACT
                ), include.remove_prefix(Task::contact_schedules)
            ).groupBy { it.task_id }
            res.forEach {
                it.contact_schedules = map[it.id].orEmpty()
            }
        }

        if (include.contains(Task::contains_matched_schedule.name)) {
            // val  vs  extra , which is better ?
        }

        //region loopup
        if (include.contains(Task::loopup_numbers.name)) {
            val items = LoopupNumber.findAll(
                LoopupNumber.Query(
                    ids = res.flatMap { it.loopup_number_ids }.toSet()
                )
            ).associateBy { it.id }
            res.forEach {
                it.loopup_numbers =
                    it.loopup_number_ids.mapNotNull { id -> items[id] }.orEmpty()
            }
        }

        if (include.contains(Task::loopup_room.name)) {
            val rooms = loopUpService.list_room(
                LoopupRoom.Query(
                    task_ids = res.legacy_ids(),
                    occupy_reason = LoopupRoom.OccupyReason.CALL
                )
            ).associateBy { it.task_id }
            res.forEach {
                it.loopup_room = rooms[it.id]
            }
        }

        if (include.contains(Task::loopup_dial_in_for_advisor.name)) {
            res.forEach {
                it.loopup_dial_in_for_advisor =
                    EmailTemplatePlaceholder.CONFERENCE_LINE_ONE_CLICK_DIAL_IN_LINK_ADVISOR
                        .resolve(PlaceholderBasedModel(task = it))
                        .let { Jsoup.parse(it).text() }
            }
        }

        if (include.contains(Task::loopup_dial_in_for_contact.name)) {
            res.forEach {
                it.loopup_dial_in_for_contact =
                    EmailTemplatePlaceholder.CONFERENCE_LINE_ONE_CLICK_DIAL_IN_LINK_CONTACT
                        .resolve(PlaceholderBasedModel(task = it))
                        .let { Jsoup.parse(it).text() }
            }
        }
        //endregion

        if (include.contains(Task::advisor_normal_payment.name)) {
            val map1 = LocalAdvisorPayment.findAll(
                LocalAdvisorPayment.Query(
                    task_ids = res.ids(),
                    is_adjustment = false,
                ), include = include.remove_prefix(Task::advisor_normal_payment.name)
            )
                .associateBy { it.task_id }
            res.forEach {
                it.advisor_normal_payment = map1[it.id]
            }
        }

        if (include.contains(Task::latest_request_cap_approval_user.name)) {
            val map = TaskEvent.findAll(
                TaskEvent.Query(
                    task_ids = res.ids(),
                    type = TaskEventType.CAPVISION_COMPLIANCE_SEND
                ),
                include = Includes.setOf(
                    TaskEvent::create_by.name,
                    *include.remove_prefix(Task::latest_request_cap_approval_user)
                        .add_prefix(TaskEvent::create_by)
                )
            ).groupBy { it.task_id }.mapValues { (_, v) ->
                v.maxByOrNull { it.create_at ?: Instant.EPOCH }!!
            }
            res.forEach {
                it.latest_request_cap_approval_user = map[it.id]?.create_by
            }
        }

        if (Task::has_billed_jit.name in include) {
            val tasks = if (res.any { it.revenues != null }) {
                res
            } else {
                Task.findAll(
                    Task.Query(ids = res.ids()),
                    Includes.setOf(Task::revenues)
                )
            }.associateBy({ it.id }) { it.revenues.orEmpty() }
            res.forEach { task ->
                task.has_billed_jit = tasks[task.id].orEmpty()
                    .any { it.status == Revenue.Status.BILLED }
            }
        }

        if (include.contains_any(
                Task::default_rate_jit.name,
                Task::default_rate_currency_jit.name
            )
        ) {
            val map = Task.findAll(
                Task.Query(
                    ids = res.ids()
                ), include = Includes.setOf(
                    Task::angle,
                    Task::project,
                    Task::lead_group,
                )
            ).associateBy { it.id }
            res.forEach {
                val task = map[it.id]
                it.default_rate_jit = task?.angle?.rate ?: task?.lead_group?.honorarium_amount ?: task?.project?.rate
                it.default_rate_currency_jit =
                    task?.angle?.rate_currency ?: task?.lead_group?.honorarium_currency ?: task?.project?.rate_currency
            }
        }

        if (Task::db_page_url_jit.name in include) {
            res.forEach {
                it.db_page_url_jit = OverSeaDbRouter.PROJECT_TASK_INFO.getUrl(
                    mapOf(
                        Project::id.name to it.project_id,
                        "task_id" to it.id,
                        "angle_id" to it.angle_id,
                    )
                )
            }
        }

        if (Task::client_publish_log.name in include) {
            val map = EntityUpdateLog.findAll(
                EntityUpdateLog.Query(
                    topic = EntityUpdateLog.Topic.TASK_PUBLISH.name,
                    entity_ids = res.ids()
                ), include.remove_prefix(Task::client_publish_log)
            )
                .groupBy { it.entity_id }

            res.forEach {
                it.client_publish_log = map[it.id].orEmpty()
                    .sortedByDescending { log -> log.create_at!! }
            }
        }

        // consider nested query conditions
        // e.g extra=...,entity_update_logs(topic=SQ&field=name).create_by
        if (Task::entity_update_logs.name in include) {
            val items = EntityUpdateLog.findAll(
                EntityUpdateLog.Query(
                    entity_table_name = Task::class.entityClassCompanion.tableName,
                    entity_ids = res.ids()
                ), include.remove_prefix(Task::entity_update_logs)
            )
                .groupBy { it.entity_id }
                .mapValues { it.value.sortedByDescending { log -> log.create_at!! } }

            res.forEach {
                it.entity_update_logs = items[it.id].orEmpty()
            }
        }

        if (Task::advisor_blocked_status.name in include) {
            val project_id_task_map = res.groupBy { it.project_id }
            project_id_task_map.forEach { (project_id, task_list) ->
                val client_id = Project.find(project_id)?.client_id
                if (client_id != null) {
                    val blocked_reason_map =
                        clientBlacklistService.get_client_advisor_blocked_info(
                            client_id = client_id,
                            advisor_ids = task_list.mapNotNull { it.advisor_id }
                                .toSet()
                        )
                    task_list.forEach { cur_task ->
                        val blocked_reasons =
                            mutableSetOf<ClientBlacklist.BlockedType>()
                        blocked_reason_map.forEach { (reason, blocked_advisor_ids) ->
                            if (cur_task.advisor_id in blocked_advisor_ids) {
                                blocked_reasons.add(reason)
                            }
                        }
                        cur_task.advisor_blocked_status = blocked_reasons
                    }
                } else {
                    // avoid null error if client_id doesn't exist
                    task_list.forEach { cur_task ->
                        cur_task.advisor_blocked_status = emptySet()
                    }
                }
            }
        }

        if (Task::survey_receivable.name in include) {
            val map = ReceivableTransaction.findAll(
                ReceivableTransaction.Query(
                    task_ids = res.ids(),
                    type = ReceivableTransaction.ReceivableType.SURVEY
                ), include.remove_prefix(Task::survey_receivable)
            ).associateBy { it.task_id }
            res.forEach {
                it.survey_receivable = map[it.id]
            }
        }

        if (Task::arrangement.name in include) {
            val map = TaskArrangement.findAll(
                TaskArrangement.Query(
                    type = ArrangementType.COMMON,
                    task_ids = res.ids()
                ), include.remove_prefix(Task::arrangement)
            )
                .associateBy { it.task_id }

            res.forEach {
                it.arrangement = map[it.id]
            }
        }

        if (Task::vetting_call_arrangement.name in include) {
            val map = TaskArrangement.findAll(
                TaskArrangement.Query(
                    type = ArrangementType.VETTING_CALL,
                    task_ids = res.ids()
                ), include.remove_prefix(Task::vetting_call_arrangement)
            )
                .associateBy { it.task_id }
            res.forEach {
                it.vetting_call_arrangement = map[it.id]
            }
        }

        if (Task::advisor_completed_task_count.name in include) {
            val advisor_ids = res.mapNotNull { it.advisor_id }.toSet()
            val client_ids = res.mapNotNull { it.client_id }.toSet()
            val founded_tasks = Task.findAll(
                query = Task.Query(
                    advisor_ids = advisor_ids,
                    client_ids = client_ids,
                    general_status = TaskStatus.General.COMPLETED,
                    project_type_not_in = setOf(Project.Type.SURVEY),
                )
            )
            val grouped_by_advisor_and_client = founded_tasks.groupBy {
                it.client_id
            }.mapValues { task_list -> task_list.value.groupBy { it.advisor_id } }
            res.forEach {
                val advisor_id = it.advisor_id
                val client_id = it.client_id
                it.advisor_completed_task_count =
                    grouped_by_advisor_and_client[client_id]?.get(advisor_id)?.size
                        ?: 0
            }
        }

        // !important keep this line at the bottom
        handleClientRateJoin(res, include)

        return res
    }

    /**
     * 计算某个专家在项目中的单价，需综合考虑专家的合同单价、专家的senior rate
     *      step1: 专家费率结合合同的senior加收条款，得到专家的senior rate multiplier(加收系数）
     *      step2: 加收系数 senior rate multiplier * 合同单价， 得到专家的单价
     * 其中，依据专家是CN/US专家（根据location判断），分别采用
     *
     *
     * 3种情况下3个值, 依取值优先顺序排列：
     * (1) 已手动设定值:
     *      1.KM在db中为task设定了client rate, 存储在Task::client_rate_usd
     *        基于此rate, 倒推得到 Task::given_senior_rate_multiplier
     *      2.于project和task_angle中新指定的特殊client_rate用以代替合同单价,会受到advisor rate与合同senior multiplier的影响
     *
     *
     *
     * (2) 默认值（专家已签署TC）
     *      Task::default_client_rate_usd,
     *      Task::senior_rate_multiplier
     *
     *
     * (3) 考虑已发送但尚未签署的专家TC中的费率的情况下的值
     *      Task::default_client_rate_usd_considering_unsigned_tc
     *      Task::senior_rate_multiplier_considering_unsigned_tc
     *
     * 最终展示给客户看的 Task::standard_rate_multiplier_display_to_client，依上面优先顺序取值
     */
    fun handleClientRateJoin(
        list: List<Task>,
        include: Set<String>
    ): List<Task> {
        if (!include.contains_any(
                Task::given_senior_rate_multiplier.name,
                Task::specified_client_rate_jit.name,
                Task::specified_client_rate_currency_jit.name,
                Task::specified_client_rate_currency_after_multiple.name,
                Task::default_client_rate_usd.name,
                Task::senior_rate_multiplier.name,
                Task::default_client_rate_usd_considering_unsigned_tc.name,
                Task::senior_rate_multiplier_considering_unsigned_tc.name,
                Task::standard_rate_multiplier_display_to_client.name
            )
        ) {
            return list
        }
        val copied_tasks_map = Task.findAll(
            query = Task.Query(
                ids = list.ids()
            ),
            include = Includes.setOf(
                Task::advisor dot Advisor::location_id_path,
                Task::angle,
                Task::project,
                Task::client dot Client::effective_contract,
                Task::client dot Client::preference
            ),
        ).associateBy { it.id }


        if (Task::given_senior_rate_multiplier.name in include) {
            list.forEach {
                val copied_entity = copied_tasks_map[it.id] ?: return@forEach
                copied_entity.client?.effective_contract?.let { contract ->
                    val advisor = it.advisor ?: return@forEach
                    if (it.client_rate_usd == null) return@forEach
                    it.given_senior_rate_multiplier =
                        contractCalcService.calc_given_senior_rate_multiplier(
                            it.client_rate_usd!!,
                            advisorService.is_cn_by_location(advisor.location_id_path.orEmpty()),
                            contract
                        )
                }
            }
        }

        if (Task::specified_client_rate_jit.name in include ||
            Task::specified_client_rate_currency_jit.name in include ||
            Task::specified_client_rate_currency_after_multiple.name in include
        ) {
            list.forEach {
                val copied_entity = copied_tasks_map[it.id] ?: return@forEach
                val multiplier =
                    copied_entity.client?.effective_contract?.let { cur_contract ->
                        copied_entity.advisor?.let { cur_advisor ->
                            contractCalcService.calc_senior_rate_multiplier(
                                advisor = cur_advisor,
                                contract = cur_contract,
                            )
                        }
                    } ?: 1.BD
                it.specified_client_rate_jit =
                    copied_entity.angle?.client_rate
                        ?: copied_entity.project?.client_rate
                it.specified_client_rate_currency_jit =
                    copied_entity.angle?.client_rate_currency_enum
                        ?: copied_entity.project?.client_rate_currency_enum
                it.specified_client_rate_currency_after_multiple =
                    it.specified_client_rate_jit?.multiply(multiplier)
            }
        }

        if (include.contains(Task::default_client_rate_usd.name)
            || include.contains(Task::senior_rate_multiplier.name)
        ) {
            list.forEach {
                val copied_entity = copied_tasks_map[it.id] ?: return@forEach
                copied_entity.client?.effective_contract?.let { contract ->
                    val advisor = it.advisor ?: return@forEach
                    if (advisor.rate != null && advisor.rate_currency != null) {
                        it.senior_rate_multiplier =
                            contractCalcService.calc_senior_rate_multiplier(
                                advisor.rate!!.toBigDecimal(),
                                advisor.rate_currency!!,
                                contract
                            )
                        it.default_client_rate_usd =
                            contractCalcService.evaluate_client_rate_usd(
                                advisor.rate!!.toBigDecimal(),
                                advisor.rate_currency!!,
                                advisorService.is_cn_by_location(advisor.location_id_path.orEmpty()),
                                contract
                            )
                    }
                }
            }
        }

        if (include.contains(Task::default_client_rate_usd_considering_unsigned_tc.name)
            || include.contains(Task::senior_rate_multiplier_considering_unsigned_tc.name)
        ) {

            // 取最新的带费率的TC (包含已发送尚未签署的) 中约定的费率
            val map_tc = AdvisorTc.findAll(
                AdvisorTc.Query(
                    advisor_ids = list.ids(Task::advisor_id)
                )
            ).filter { it.rate != null && it.create_time != null }
                .groupBy { it.advisor_id }
                .mapValues {
                    it.value.maxByOrNull { tc -> tc.create_time!! }
                }

            list.forEach {
                val advisor = it.advisor ?: return@forEach
                val tc = map_tc[advisor.id] ?: return@forEach
                val copied_entity = copied_tasks_map[it.id] ?: return@forEach
                copied_entity.client?.effective_contract?.let { contract ->
                    it.senior_rate_multiplier_considering_unsigned_tc =
                        contractCalcService.calc_senior_rate_multiplier(
                            tc.rate!!,
                            tc.rate_currency_enum!!,
                            contract
                        )
                    it.default_client_rate_usd_considering_unsigned_tc =
                        contractCalcService.evaluate_client_rate_usd(
                            tc.rate!!,
                            tc.rate_currency_enum!!,
                            advisorService.is_cn_by_location(advisor.location_id_path.orEmpty()),
                            contract
                        )
                }
            }
        }

        // senior_rate_multiplier,
        // given_senior_rate_multiplier,
        // senior_rate_multiplier_considering_unsigned_tc
        //      should be joined before this
        if (Task::standard_rate_multiplier_display_to_client.name in include) {
            list.forEach {
                val client = copied_tasks_map[it.id]?.client ?: return@forEach
                val display_oversea_when_to_client =
                    client.preference?.display_oversea_when_to_client == true
                client.effective_contract?.let { contract ->
                    val client_rate = it.client_rate_usd
                        ?: it.default_client_rate_usd
                        ?: it.default_client_rate_usd_considering_unsigned_tc

                    it.standard_rate_multiplier_display_to_client =
                        if (display_oversea_when_to_client && client_rate != null) {
                            contractCalcService.calc_given_senior_rate_multiplier(
                                client_rate_usd = client_rate,
                                is_cn_advisor = true,  // 这种情况下就用cn单价作为基数计算系数
                                contract
                            )
                        } else {
                            it.given_senior_rate_multiplier
                                ?: it.senior_rate_multiplier
                                ?: it.senior_rate_multiplier_considering_unsigned_tc
                        }
                }
            }

        }

        if (Task::advisor_answered_current_project_sq.name in include) {
            Task.join(list, Includes.setOf(Task::project))
            list.groupBy { it.project_id }.forEach { entry ->
                val cur_project_tasks = entry.value
                val project =
                    cur_project_tasks.firstOrNull()?.project ?: biz_error("related project not found when handle join")
                val cur_project_all_task_ids = Task.findAll(
                    query = Task.Query(
                        project_id = entry.key
                    )
                ).ids()
                val receiver_ids = cur_project_tasks.ids(Task::advisor_id)
                val inquiry_instances = InquiryInstance.findAll(
                    query = InquiryInstance.Query(
                        source_ids = cur_project_all_task_ids,
                        status_ne = InquiryInstance.Status.ASSIGNED,
                        receiver_ids = receiver_ids,
                        inquiry_type = Inquiry.Type.SCREENING,

                        )
                ).filter {
                    it.inquiry_id == project.sq_id || it.is_one_off_question == true
                }
                val existing_receiver_ids = inquiry_instances.ids(InquiryInstance::receiver_id)
                cur_project_tasks.groupBy { it.advisor_id }.forEach { entry ->
                    val advisor_id = entry.key
                    val tasks = entry.value
                    if (advisor_id in existing_receiver_ids) {
                        tasks.forEach { it.advisor_answered_current_project_sq = true }
                    } else {
                        tasks.forEach { it.advisor_answered_current_project_sq = false }
                    }
                }
            }
        }

        return list
    }

    override fun handleIncludeProperty(
        list: List<Task>,
        propertyName: String,
        nestedIncludes: Set<String>
    ) {
        super.handleIncludeProperty(list, propertyName, nestedIncludes)

        when (propertyName) {
            Task::compliance_approve_policy_jit.name -> {
                taskApprovalService.compute_legal_approve_policy(list)
            }

            Task::require_pre_call_ca_jit.name -> {
                taskApprovalService.compute_require_pre_call_ca(list)
            }

            Task::compliance_passed_jit.name -> {
                list.forEach {
                    it.compliance_passed_jit =
                        taskApprovalService.validate_task_legal_approve_status(it)
                }
            }

            Task::task_communication.name -> {
                val task_communications = get_task_communications(list)
                list.forEach {
                    it.task_communication = task_communications[it.id]
                }
            }

            Task::latest_or_create_survey_portal.name -> {
                val now = Instant.now()
                val filtered_tasks = list.filter {
                    !it.has_completed
                            && it.project_sub_type == Project.SubType.Survey
                }
                val map = Portal.findAll(
                    Portal.Query(
                        task_ids = filtered_tasks.ids(Task::id),
                        type = PortalType.ADVISOR_THIRD_PARTY_SURVEY,
                        token_expire_at_gt = now,
                        consume_at_is_null = true,
                        disable = false,
                    )
                ).groupBy { it.task_id }
                    .mapValues { (_, v) ->
                        v.maxByOrNull { it.create_at ?: Instant.EPOCH }
                    }
                // get user id here might not be a good pattern
                // however, this extra prop is for temp usage.
                val user_id = userIdService?.get_user_id()
                filtered_tasks.forEach { task ->
                    val portal = map[task.id]
                    task.latest_or_create_survey_portal =
                        portal ?: user_id?.let {
                            taskSurveyOutreachService
                                .create_survey_portal(task, it)
                        }
                }
                Portal.join(
                    list.mapNotNull { it.latest_or_create_survey_portal },
                    nestedIncludes,
                )
            }

            Task::latest_or_create_unsubscribe_outreach_portal.name -> {
                val now = Instant.now()
                val uncompleted_tasks = list.filter {
                    !it.has_completed
                }
                val map = Portal.findAll(
                    Portal.Query(
                        task_ids = uncompleted_tasks.ids(Task::id),
                        type = PortalType.ADVISOR_UNSUBSCRIBE_OUTREACH,
                        token_expire_at_gt = now,
                        consume_at_is_null = true,
                        disable = false,
                    )
                ).groupBy { it.task_id }
                    .mapValues { (_, v) ->
                        v.maxByOrNull { it.create_at ?: Instant.EPOCH }
                    }
                // get user id here might not be a good pattern
                // however, this extra prop is for temp usage.
                val user_id = userIdService?.get_user_id()
                uncompleted_tasks.forEach { task ->
                    val portal = map[task.id]
                    task.latest_or_create_unsubscribe_outreach_portal = portal
                        ?: user_id?.let {
                            taskOutreachUnsubscribeService
                                .create_portal(task, it)
                        }
                }
                Portal.join(
                    list.mapNotNull {
                        it.latest_or_create_unsubscribe_outreach_portal
                    },
                    nestedIncludes,
                )
            }

            Task::latest_or_create_decline_portal.name -> {
                // cannot autowire because circular dependence
                val taskAdvisorDeclineService: TaskAdvisorDeclineService = CoreAppContextHolder
                    .getBean()
                val now = Instant.now()
                val uncompleted_tasks = list.filter {
                    !it.has_completed
                }
                val map = Portal.findAll(
                    Portal.Query(
                        task_ids = uncompleted_tasks.ids(Task::id),
                        type = PortalType.ADVISOR_DECLINE,
                        token_expire_at_gt = now,
                        consume_at_is_null = true,
                        disable = false,
                    )
                ).groupBy { it.task_id }
                    .mapValues { (_, v) ->
                        v.maxByOrNull { it.create_at ?: Instant.EPOCH }
                    }
                uncompleted_tasks.forEach { task ->
                    val portal = map[task.id]
                    task.latest_or_create_decline_portal = portal
                        ?: taskAdvisorDeclineService
                            .create_portal(
                                project_id = task.project_id,
                                task_id = task.id,
                                advisor_id = task.advisor_id
                            )
                }
                Portal.join(
                    list.mapNotNull {
                        it.latest_or_create_decline_portal
                    },
                    nestedIncludes,
                )
            }

            Task::p_l_profit.name -> {
                compute_task_profit(list)
            }

            Task::is_custom_sourced.name -> {
                val customSourcedAdvisorIds = advisorCustomSourceService
                    .is_task_custom_sourced_bulk(list)

                list.forEach {
                    it.is_custom_sourced =
                        when (it.id) {
                            in customSourcedAdvisorIds.custom_sourced_task_ids -> true
                            in customSourcedAdvisorIds.not_custom_sourced_task_ids -> false
                            else -> null
                        }
                }
            }
        }
    }

    override fun patch(patch: Patch<Task>, option: CrudOptions.PatchOption<Task>) {
        super.patch(patch, option)
        advisorTagMapProjectSqService.handle_task_changed_async(
            patch.entity.id, patch.fields,
        )
    }

    /**
     * Populates the [Task.task_communication] field.
     * Takes the [Task.email_records] and [Task.portal_advisor_records] lists, filters out irrelevant and redundant entries,
     * compiles them into one list, and sorts that list.
     * It filters out emails that don't have an advisor_id or that does have a client_contact_id to get rid of client communications.
     * Filters out advisor_records that appear in the email lists' [CommunicationRecord.advisor_portal] field to get rid of duplicates.
     * @param Task a Task object that will have its [Task.task_communication] field populated.
     * @return A sorted, compiled [List] of [TaskCommunication] objects.
     */
    fun get_task_communications(
        tasks: List<Task>
    ): Map<Int, List<TaskCommunication>> {
        val emails = CommunicationRecord.findAll(
            CommunicationRecord.Query(
                task_ids = tasks.ids(),
                advisor_id_is_null = false,
                client_contact_id_is_null = true
            ),
            include = setOf(CommunicationRecord::email_template.name, CommunicationRecord::create_by.name)
        )

        val call_logs = TaskCallLog.findAll(
            TaskCallLog.Query(
                task_ids = tasks.ids()
            ),
            include = setOf(TaskCallLog::user.name)
        )

        val email_portal_ids = emails.mapNotNull {
            it.portal_id
        }

        //only filters out links that are in emails if those links are
        val links_query = if (email_portal_ids.isEmpty()) {
            PortalAdvisor.Query(
                task_ids = tasks.ids()
            )
        } else {
            PortalAdvisor.Query(
                task_ids = tasks.ids(),
                id_not_in = email_portal_ids.toSet()
            )
        }

        val links = PortalAdvisor.findAll(
            links_query,
            include = setOf(PortalAdvisor::create_user.name)
        ) //"links" refers to links retrieved with the "Links" button on each task

        val email_communications = emails.groupBy {
            it.task_id
        }.mapValues { entry ->
            entry.value.map {
                val type = if (it.via_linkedin == true) TaskCommunication.Type.LINKEDIN
                else TaskCommunication.Type.EMAIL
                TaskCommunication(
                    create_at = it.create_at,
                    type = type,
                    name = it.email_template?.name ?: "",
                    create_by_name = it.create_by?.name ?: "",
                    email_content_type = it.email_content_type
                )
            }
        }

        val link_communications = links.groupBy {
            it.task_id
        }.mapValues { entry ->
            entry.value.map {
                TaskCommunication(
                    create_at = it.create_at,
                    type = TaskCommunication.Type.LINK,
                    name = it.type.name,
                    create_by_name = it.create_user?.name ?: "",
                    email_content_type = null
                )
            }
        }

        val zoom_phone_communications = call_logs.groupBy { it.task_id }.mapValues { entry ->
            entry.value.map {
                TaskCommunication(
                    create_at = it.create_at,
                    type = TaskCommunication.Type.ZOOM_PHONE,
                    name = it.advisor_phone_number,
                    create_by_name = it.user?.name ?: "",
                    email_content_type = null
                )
            }
        }

        return tasks.ids().associateWith { task_id ->
            val combined_communications =
                (email_communications[task_id] ?: emptyList()) + (link_communications[task_id]
                    ?: emptyList()) + (zoom_phone_communications[task_id] ?: emptyList())

            //sort by create_at desc -> most recent on top
            combined_communications.sortedByDescending { it.create_at }
        }

    }

    fun compute_task_profit(
        list: List<Task>,
    ) {
        if (list.isEmpty()) return
        val task_ids = list.ids()
        // task income
        val revenues = Revenue.findAll(Revenue.Query(task_ids = task_ids))
        val transactions = ReceivableTransaction.findAll(ReceivableTransaction.Query(task_ids = task_ids))
        val taskId_revenue_map = revenues.groupBy { it.task_id }
            .mapValues { (_, revenueList) ->
                revenueList.fold(BigDecimal.ZERO) { acc, revenue -> acc + (revenue.cash ?: BigDecimal.ZERO) }
            }
        val taskId_transaction_map = transactions.groupBy { it.task_id }
            .mapValues { (_, transactionList) ->
                transactionList.fold(BigDecimal.ZERO) { acc, transaction -> acc + transaction.amount }
            }
        val taskId_income_map = (taskId_revenue_map.keys + taskId_transaction_map.keys).associateWith {
            (taskId_revenue_map[it] ?: BigDecimal.ZERO) + (taskId_transaction_map[it] ?: BigDecimal.ZERO)
        }

        // task expense
        val taskId_payment_map = PaymentItem.findAll(
            PaymentItem.Query(
                topic = PaymentTopic.Query(task_tsid_in = list.ids(Task::tsid)),
                status_not_in = setOf(PaymentItem.Status.CANCELLED, PaymentItem.Status.PAY_FAILED)
            ),
            Includes.setOf(PaymentItem::topic dot PaymentTopic::task)
        ).groupBy { it.topic?.task?.id ?: 0 }
            .mapValues { (_, paymentList) ->
                paymentList.fold(BigDecimal.ZERO) { acc, payment -> acc + payment.amount }
            }
        val taskId_lap_map = LocalAdvisorPayment.findAll(
            LocalAdvisorPayment.Query(task_ids = task_ids)
        ).groupBy { it.task_id }
            .mapValues { (_, lapList) ->
                lapList.fold(BigDecimal.ZERO) { acc, lap -> acc + lap.amount }
            }
        val taskId_expense_map = (taskId_payment_map.keys + taskId_lap_map.keys).associateWith {
            (taskId_payment_map[it] ?: BigDecimal.ZERO) + (taskId_lap_map[it] ?: BigDecimal.ZERO)
        }

        // task profit
        list.forEach { task ->
            val income = taskId_income_map[task.id] ?: BigDecimal.ZERO
            if (income.compareTo(BigDecimal.ZERO) == 0) {
                task.p_l_profit = null
                return@forEach
            }
            val expense = taskId_expense_map[task.id] ?: BigDecimal.ZERO
            task.p_l_profit = (income - expense).divide(income, 4, RoundingMode.HALF_UP)
        }
    }

    fun generate_non_conflicting_schedules(
        available_schedule: List<Schedule>,
        arranged_schedule: List<Schedule>
    ): List<Schedule> {
        // 排序两个列表，确保时间是有序的
        val sorted_available = available_schedule.sortedBy { it.start_time }
        val sorted_arranged = arranged_schedule.sortedBy { it.start_time }

        val result = mutableListOf<Schedule>()

        // 遍历每个 available_schedule
        for (available in sorted_available) {
            var start = available.start_time!!
            val end = available.end_time!!

            // 从头开始检查所有 arranged_schedule
            for (arranged in sorted_arranged) {
                // 如果 arranged 的时间段完全在 available 之前，跳过
                if (arranged.end_time!! <= start) {
                    continue
                }

                // 如果 arranged 的时间段完全在 available 之后，跳出循环
                if (arranged.start_time!! >= end) {
                    break
                }

                // 如果有冲突，添加无冲突的前段时间
                if (arranged.start_time!! > start) {
                    result.add(
                        Schedule {
                            this.start_time = start
                            this.end_time = arranged.start_time
                        }
                    )
                }

                // 更新起点为 arranged 冲突结束后的时间点
                start = maxOf(start, arranged.end_time!!)

                // 如果当前时间段已完全被覆盖，退出内层循环
                if (start >= end) {
                    break
                }
            }

            // 如果剩余时间段不冲突，将其添加到结果中
            if (start < end) {
                result.add(
                    Schedule {
                        this.start_time = start
                        this.end_time = end
                    }
                )
            }
        }

        return result
    }
}

package com.cpvsn.rm.core.features.task.lead_group

import com.cpvsn.core.svc.spring.ext.extExecute
import com.cpvsn.crud.model.Includes
import com.cpvsn.crud.model.Patch
import com.cpvsn.crud.model.dot
import com.cpvsn.rm.core.features.project.Project
import com.cpvsn.rm.core.features.task.Task
import com.cpvsn.rm.core.features.task.TaskService
import com.cpvsn.rm.core.features.user.UserResourceOrder
import com.cpvsn.rm.core.util.Event
import com.cpvsn.rm.core.util.InvokeUtil
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.transaction.support.TransactionTemplate

@Service
class LeadGroupService {

    @Autowired
    private lateinit var taskService: TaskService

    @Autowired
    private lateinit var transactionTemplate: TransactionTemplate

    @Autowired
    private lateinit var userResourceOrderRepo: UserResourceOrder.Repo

    companion object {
        val DEFAULT_LEAD_GROUP_NAME = "All Leads"
    }

    fun save_cascade(entity: LeadGroup): LeadGroup {
        return LeadGroup.save(
            entity
        )
    }

    fun patch_cascade(patch: Patch<LeadGroup>, include: Set<String>): LeadGroup {
        val res = LeadGroup.patchThenGet(
            patch, include = include
        )

        InvokeUtil.trigger(Event.PROJECT_DOC_CHANGED(project_id = res.project_id))
        return res
    }

    @Transactional
    fun delete_cascade(id: Int) {
        transactionTemplate.execute {
            val group = LeadGroup.get(id, include = Includes.setOf(LeadGroup::tasks))
            // Move leads to another group or ungroup them
            group.tasks.orEmpty().forEach { lead ->
                Patch.fromMutator(lead) {
                    this.lead_group_id = null
                }.patchThenGet()
            }
            LeadGroup.delete(id)
        }
    }

    fun move_leads_to_group(
        lead_ids: Set<Int>,
        target_group_id: Int,
    ): List<Task> {
        return transactionTemplate.extExecute {
            val leads = Task.findAll(Task.Query(ids = lead_ids))
            // Update lead group for each lead
            leads.forEach { lead ->
                Patch.fromMutator(lead) {
                    this.lead_group_id = target_group_id
                }.patchThenGet()
            }
            leads
        }
    }

    fun user_customize_sort(
        project_id: Int,
        id_position_map: Map<Int, Double>,
        user_id: Int,
    ): List<LeadGroup> {
        return transactionTemplate.extExecute { _ ->
            val project = Project.get(
                project_id, Includes.setOf(
                    Project::lead_groups dot LeadGroup::customize_order
                )
            )
            val groups = project.lead_groups.orEmpty()

            val orders = groups.sortedBy {
                id_position_map[it.id]
                    ?: it.customize_order?.display_order?.toDouble()
                    ?: it.rank.toDouble()
            }.mapIndexed { index, group ->
                UserResourceOrder {
                    this.user_id = user_id
                    this.resource_id = group.id
                    this.resource_type = UserResourceOrder.Type.LEAD_GROUP
                    this.display_order = index + 1
                }
            }

            userResourceOrderRepo.batchSave(orders)

            LeadGroup.join(groups, Includes.setOf(LeadGroup::customize_order))
        }
    }

    fun create_default_group_for_projects_before() {
        val projects = Project.findAll(
            query = Project.Query(
                sub_type = Project.SubType.Survey
            )
        )
        projects.forEach { cur_project ->
            val default_lead_group = LeadGroup.firstOrNull(
                query = LeadGroup.Query(
                    project_id = cur_project.id,
                    name = DEFAULT_LEAD_GROUP_NAME
                )
            ) ?: let {
                LeadGroup.save(LeadGroup {
                    this.project_id = cur_project.id
                    this.name = DEFAULT_LEAD_GROUP_NAME
                })
            }
            val cur_project_no_lead_group_leads = Task.findAll(
                query = Task.Query(
                    project_id = cur_project.id,
                    angle_id_is_null = true,
                    lead_group_id_is_null = true,
                )
            )
            cur_project_no_lead_group_leads.forEach {
                Patch.fromMutator(it) {
                    it.lead_group_id = default_lead_group.id
                }.patch()
            }
        }
    }


    /**
     * Creates lead groups with the same name, honorarium amount, and honorarium currency as the leads in [lead_groups], and attaches them to the project specified by [project_id]
     */
    fun clone_lead_groups(
        project_id: Int,
        lead_groups: List<LeadGroup>,
    ): List<LeadGroup> {
        val new_lead_groups = lead_groups.map { raw_lead_group ->
            val entity = LeadGroup {
                this.project_id = project_id
                this.name = raw_lead_group.name
                this.rank = 0
                this.honorarium_amount = raw_lead_group.honorarium_amount
                this.honorarium_currency = raw_lead_group.honorarium_currency
            }
            LeadGroup.save(entity)
        }
        return new_lead_groups
    }
} 
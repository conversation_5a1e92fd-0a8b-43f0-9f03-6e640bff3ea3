package com.cpvsn.rm.core.features.task.angle

import com.cpvsn.core.base.entity.BaseCompanion
import com.cpvsn.core.base.entity.BaseQuery
import com.cpvsn.core.base.entity.TimeAuditingEntity
import com.cpvsn.crud.orm.annotation.Column
import com.cpvsn.crud.orm.annotation.Relation
import com.cpvsn.crud.orm.annotation.TableDefinition
import com.cpvsn.crud.query.Criteria
import com.cpvsn.rm.core.features.user.User

@TableDefinition(
    indices = [
        TableDefinition.IndexDefinition(columns = ["angle_id", "user_id"], TableDefinition.IndexType.UNIQUE)]
)
class TaskAngleMember : TimeAuditingEntity() {

    companion object : BaseCompanion<TaskAngleMember>()

    @Column
    var angle_id: Int = 0

    @Column
    var user_id: Int = 0

    @Relation
    var angle: TaskAngle? = null

    @Relation
    var user: User? = null

    data class Query(
        @Criteria.Eq
        val id: Int? = null,
        @Criteria.IdsIn
        val ids: Set<Int>? = null,
    ) : BaseQuery<TaskAngleMember>()
}

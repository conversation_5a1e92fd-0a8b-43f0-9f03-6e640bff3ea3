package com.cpvsn.rm.core.features.thirdparty.deepgram

import com.cpvsn.core.svc.rpc.http.unirest.UnirestUtil.configureLogger
import com.cpvsn.core.svc.rpc.http.unirest.UnirestUtil.configureObjectMapper
import com.cpvsn.core.util.CoreJsonUtil
import com.cpvsn.rm.core.features.thirdparty.deepgram.request.DeepgramModel
import com.cpvsn.rm.core.features.thirdparty.deepgram.request.DeepgramRequestParam
import com.cpvsn.rm.core.features.thirdparty.deepgram.response.DeepgramRequestIdResponse
import com.cpvsn.rm.core.features.thirdparty.deepgram.webhook.DeepgramWebhookService.Companion.HOST
import com.cpvsn.rm.core.features.thirdparty.deepgram.webhook.DeepgramWebhookService.Companion.TWILIO_ENDPOINT
import com.cpvsn.rm.core.features.thirdparty.deepgram.webhook.DeepgramWebhookService.Companion.ZOOM_ENDPOINT
import com.cpvsn.rm.core.features.twiliosdk.entity.TwilioAsset
import com.cpvsn.rm.core.features.twiliosdk.enums.TwilioAddOnEnum
import com.cpvsn.rm.core.features.zoom.meeting.entity.ZoomAsset
import com.cpvsn.rm.core.util.JacksonUtil
import com.fasterxml.jackson.core.type.TypeReference
import kong.unirest.Unirest
import org.slf4j.LoggerFactory

class DeepgramRestApiService(val properties: DeepgramProperties) {
    companion object {
        const val SERVICE_NAME = "deepgram"
    }

    private val logger = LoggerFactory.getLogger(this::class.java)

    private val unirest = Unirest.spawnInstance().apply {
        config().defaultBaseUrl(properties.base_url)
        // deepgram will recognize the file type automatically
        configureObjectMapper()
        configureLogger(logger)
    }

    fun <T> postRequest(
        endpoint: String?,
        params: Map<String, String?>?,
        data: Map<String, String?>?,
        responseType: TypeReference<T>,
    ): T? {
        val response = unirest.post(endpoint)
            .header("Authorization", "Token ${properties.token}")
            .header("Content-Type", "application/json")
            .queryString(params)
            .body(data)
            .asObject {
                val buf = it.contentAsString
                logger.info("Deepgram Response $buf")
                if (!buf.isNullOrBlank()) {
                    JacksonUtil.objectMapper.readValue(buf, responseType)
                } else {
                    null
                }
            }
        return response.body
    }

    fun create_transcript(
        audio_link: String,
        callback_url: String,
    ): DeepgramRequestIdResponse? {
        val params_json = CoreJsonUtil.stringify(
            DeepgramRequestParam(
                callback = callback_url,
                model = DeepgramModel.WHISPER_LARGE.value,
            )
        )
        val params = CoreJsonUtil.parse<Map<String, String?>>(params_json)
        val data = mapOf(
            "url" to audio_link,
        )
        val res = postRequest(
            properties.listen_endpoint,
            params,
            data,
            object : TypeReference<DeepgramRequestIdResponse?>() {})
        return res
    }

    fun request_for_twilio(
        res: TwilioAsset
    ): TwilioAsset? {
        return create_transcript(
            audio_link = res.capvision_file_url,
            callback_url = HOST + TWILIO_ENDPOINT
        )?.let {
            TwilioAsset.save(TwilioAsset {
                conference_sid = res.conference_sid
                type = TwilioAsset.Type.VOICE_CONFERENCE_TRANSCRIPTION
                add_on = TwilioAddOnEnum.deepgram
                recording_sid = res.recording_sid
                add_on_result_sid = it.request_id
            })
        }
    }

    fun request_for_zoom(
        res: ZoomAsset
    ): ZoomAsset? {
        return create_transcript(
            audio_link = res.capvision_file_url,
            callback_url = HOST + ZOOM_ENDPOINT,
        )?.let {
            ZoomAsset.save(ZoomAsset {
                meeting_uuid = res.meeting_uuid
                meeting_id = res.meeting_id
                start_time = res.start_time
                asset_id = it.request_id
                file_type = ZoomAsset.FileType.TRANSCRIPT
                recording_type = SERVICE_NAME
            })
        }
    }

}
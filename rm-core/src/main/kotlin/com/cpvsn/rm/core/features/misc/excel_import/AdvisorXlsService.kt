package com.cpvsn.rm.core.features.misc.excel_import

import com.cpvsn.core.model.BusinessException
import com.cpvsn.core.util.CoreJsonUtil
import com.cpvsn.crud.model.Includes
import com.cpvsn.crud.pagination.PageRequest
import com.cpvsn.crud.pagination.Sort
import com.cpvsn.crud.util.ids
import com.cpvsn.rm.core.features.advisor.Advisor
import com.cpvsn.rm.core.features.advisor.AdvisorCreationService
import com.cpvsn.rm.core.features.advisor.AdvisorRepository
import com.cpvsn.rm.core.features.advisor.job.AdvisorJob
import com.cpvsn.rm.core.features.client.blacklist.ClientBlacklistService
import com.cpvsn.rm.core.features.misc.company.Company
import com.cpvsn.rm.core.features.misc.contact_info.ContactInfo
import com.cpvsn.rm.core.features.misc.excel_import.ExcelImportUtils.get_current_time_signature
import com.cpvsn.rm.core.features.misc.excel_import.model.AdvisorXlsRow
import com.cpvsn.rm.core.features.misc.excel_import.pojo.AdvisorXlsPojo
import com.cpvsn.rm.core.features.misc.location.Location
import com.cpvsn.rm.core.features.project.Project
import com.cpvsn.rm.core.features.search.EsSearchService
import com.cpvsn.rm.core.features.task.Task
import com.cpvsn.rm.core.features.task.TaskCreationService
import com.cpvsn.rm.core.features.task.TaskRepository
import com.cpvsn.rm.core.features.task.constant.TaskWillingnessType
import com.cpvsn.rm.core.features.task.lead_group.LeadGroup
import com.cpvsn.rm.core.features.task.lead_group.LeadGroupService
import com.cpvsn.rm.core.features.task.pojo.TaskCreateRequest
import com.cpvsn.rm.core.features.user.User
import com.cpvsn.rm.core.util.PoiExcelUtil
import com.cpvsn.rm.core.util.biz_check
import com.cpvsn.rm.core.util.biz_error
import com.cpvsn.rm.core.util.global_executor
import com.github.doyaaaaaken.kotlincsv.dsl.csvReader
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.net.URL


@Service
class AdvisorXlsService {
    //region @
    @Autowired
    private lateinit var advisorCreationService: AdvisorCreationService

    @Autowired
    private lateinit var taskCreationService: TaskCreationService

    @Autowired
    private lateinit var repo: AdvisorXlsRow.Repo

    @Autowired
    private lateinit var taskRepository: TaskRepository

    @Autowired
    private lateinit var advisorRepository: AdvisorRepository

    @Autowired
    private lateinit var companyRepository: Company.Repo

    @Autowired
    private lateinit var esSearchService: EsSearchService

    @Autowired
    private lateinit var clientBlacklistService: ClientBlacklistService
    private val logger = LoggerFactory.getLogger(this::class.java)
    //endregion


    fun dump_data(): List<AdvisorXlsRow> {
        val fileInputStream = this.javaClass.classLoader.getResourceAsStream("files/advisor_xls.xlsx")!!
        val sheet = XSSFWorkbook(fileInputStream).getSheetAt(0)
        var pojos = PoiExcelUtil.readModelsFromSheet(
            sheet = sheet,
            clazz = AdvisorXlsPojo::class,
            natural_start_row = 1
        )
        // the first line may be title
        if (!pojos[0].linkedin_url.contains("www.")) {
            pojos = pojos.subList(fromIndex = 1, toIndex = pojos.size)
        }
        val timestr = get_current_time_signature()
        val rows = pojos.mapNotNull {
            try {
                it.toAdvisorXlsRow(batch = "batch_$timestr")
            } catch (e: Exception) {
                null
            }
        }
        repo.batchSave(rows)
        return rows
    }

    fun read_data_from_url(
        file_url: String,
        file_name: String,
    ): List<AdvisorXlsRow> {
        val fileInputStream = URL(file_url).openStream()
        val is_csv = file_name.endsWith(".csv")
        var pojos = if (is_csv) {
            val lines = csvReader().readAll(fileInputStream)
            lines.mapIndexed { idx, fields ->
                AdvisorXlsPojo().apply {
                    this.row = idx + 1
                    this.linkedin_url = fields[0]
                    this.name = fields[1]
                    this.position = fields[2]
                    this.company = fields[3]
                    this.location = fields[4]
                    this.start_date = fields[5]
                    this.project_id = fields[6]

                    this.sourced_by_name = fields[7]
                    this.email_1 = fields[8]
                    this.email_2 = fields[9]
                    this.email_3 = fields[10]
                    this.email_4 = fields[11]
                    this.phone_1 = fields[12]
                    this.phone_2 = fields.getOrNull(13) ?: ""

                    this.phone_3 = fields.getOrNull(14) ?: ""
                    this.is_linkedin_premium = fields.getOrNull(15) ?: ""
                    this.lead_group_name = fields.getOrNull(index = 16) ?: ""
                }
            }
        } else {
            val sheet = XSSFWorkbook(fileInputStream).getSheetAt(0)
            PoiExcelUtil.readModelsFromSheet(
                sheet = sheet,
                clazz = AdvisorXlsPojo::class,
                natural_start_row = 1
            )
        }

        // the first line may be title
        if (!pojos[0].linkedin_url.contains("www.")) {
            pojos = pojos.subList(fromIndex = 1, toIndex = pojos.size)
        }
        val timestr = get_current_time_signature()
        val rows = pojos.mapIndexedNotNull { index, pojo ->
            //Notify the user the upload is incorrect if an email does not have '@'
            pojo.emails.forEach { email ->
                if (email.isNotBlank() and !email.contains("@")) {
                    val row = index + 1
                    biz_error("One or more of these email addresses does not contain an @ symbol. (near row $row)")
                }
            }
            try {
                pojo.toAdvisorXlsRow(batch = "${file_name}_$timestr")
            } catch (e: Exception) {
                null
            }
        }
        return rows
    }

    fun execute_steps(
        query: AdvisorXlsRow.Query,
    ): List<AdvisorXlsRow> {
        try {
            // 5 steps
            fill_user_id(query)
            mark_duplicate_in_network_by_contactinfo(query)
            import_companies(query)
            import_advisors(query)
            val results = import_to_project_as_tasks(query)

            // sync es
            val res = AdvisorXlsRow.findAll(query)
                .filter { row -> row.imported_advisor_id !in results.removed_requests.map { it.advisor_id }.toSet() }
            global_executor.execute {
                esSearchService.advisor_bulk_by_ids(
                    ids = res.ids(AdvisorXlsRow::imported_advisor_id)
                )
            }
            return res
        } catch (e: Exception) {
            e.printStackTrace()
            throw BusinessException("Error occured(${e.message}). If you can't resolve it, please contact IT team for support")
        }
    }

    //region 5 steps
    fun fill_user_id(
        query: AdvisorXlsRow.Query,
    ): List<AdvisorXlsRow> {
        val rows = AdvisorXlsRow.findAll(query)
            .filter { it.user_id == null }
        val sourced_by_names = rows.map { it.sourced_by_name }.distinct()
        biz_check(sourced_by_names.none { it.isBlank() }) {
            "There's blank value in sourced_by_name"
        }
        val users = User.findAll(
            User.Query(
                name_in = sourced_by_names.toSet()
            )
        ).associateBy { it.name }

        val unmatched_names = sourced_by_names.filter {
            it !in users.keys
        }
        biz_check(unmatched_names.isEmpty()) {
            "There's un-matched value in sourced_by_name:${unmatched_names.joinToString(",")}"
        }
        rows.forEach {
            it.user_id = users[it.sourced_by_name]!!.id
        }
        repo.batchPatch(rows, fields = setOf(AdvisorXlsRow::user_id.name))
        return rows
    }

    fun mark_duplicate_in_network_by_contactinfo(
        query: AdvisorXlsRow.Query,
    ): List<AdvisorXlsRow> {
        val rows = AdvisorXlsRow.findAll(query)

        // check with advisor database
        val exist_contact_infos = ContactInfo.findAll(
            ContactInfo.Query(
                owner_type = ContactInfo.OwnerType.ADVISOR,
                value_in = rows.flatMap { it.personal_contact_infos }.distinct()
                    .filter { !it.contains("?") }
                    .toSet()
            ))
        val map = exist_contact_infos.associateBy { it.value.toLowerCase() }

        val update_rows = mutableListOf<AdvisorXlsRow>()
        rows.forEach { row ->
            val duplicate = row.personal_contact_infos.mapNotNull { map[it.toLowerCase()] }
            duplicate.firstOrNull()?.let { contactInfo ->
                row.duplicate_advisor_id = contactInfo.owner_id
                update_rows.add(row)
            }
        }
        repo.batchPatch(update_rows, setOf(AdvisorXlsRow::duplicate_advisor_id.name))
        return rows
    }

    fun import_companies(
        query: AdvisorXlsRow.Query,
    ): List<AdvisorXlsRow> {
        val rows = AdvisorXlsRow.findAll(query)
            .filter { it.imported_company_id == null }

        val company_names = rows.map { it.company }.filter { it.isNotBlank() }.distinct()
        val exist_companies = Company.findAll(
            Company.Query(
                name_in = company_names.toSet()
            )
        )

        val create_company_names = rows.map { it.company }
            .filter { exist_companies.none { o -> o.name == it } }
            .filter { it.isNotBlank() }
            .distinct()

        val create_companies = create_company_names
            .map {
                Company {
                    name = it
                    english_name = it

                }
            }
        companyRepository.batchSave(create_companies)
        val created = Company.list(
            pageable = PageRequest(
                page = 1,
                size = create_companies.size + 100,
                sort = Sort.by(Sort.Order(Company::id.name, Sort.Direction.DESC))
            )
        ).items

        val map_created = created.associateBy { it.name }

        val update_rows = mutableListOf<AdvisorXlsRow>()
        rows.forEach {
            map_created[it.company]?.let { company ->
                it.imported_company_id = company.id
                update_rows.add(it)
            }
        }
        repo.batchPatch(update_rows, setOf(AdvisorXlsRow::imported_company_id.name))
        return rows
    }

    fun import_advisors(
        query: AdvisorXlsRow.Query,
    ): List<Advisor> {
        val rows = AdvisorXlsRow.findAll(query)
            .filter { it.user_id != null }
            .filter { it.result_advisor_id == null }

        val map_location = Location.findAll(
            Location.Query(
                name_in = rows.flatMap { it.locations }.filter { !it.contains("'") }.toSet()
            )
        ).associateBy { it.name }

        val map_company = Company.findAll(
            Company.Query(
                name_in = rows.map { it.company }
                    .filter { it.isNotBlank() }
                    .toSet()
            )).associateBy { it.name }

        // 坑，他提供的excel里的联系方式有重复的
        val batch_added_contact_infos = mutableSetOf<String>()
        val batch_added_rows = mutableListOf<AdvisorXlsRow>()
        val map_row_advisor = mutableMapOf<AdvisorXlsRow, Advisor>()

        val advisors = rows.mapNotNull { row ->
            val advisor = Advisor()

            // 忽略重复行
            val duplicate_row = batch_added_rows.firstOrNull {
                it.linkedin_url == row.linkedin_url && it.name == row.name
            }
            if (duplicate_row != null) {
                return@mapNotNull null
            }
            batch_added_rows.add(row)

            // 忽略非重复行的重复联系信息
            val personal_usable_contact_infos = row.generateContactInfoEntities().filter {
                it.value !in batch_added_contact_infos
            }
            batch_added_contact_infos.addAll(
                personal_usable_contact_infos.map { it.value }
            )

            val company_id = map_company[row.company]?.id
            val job = company_id?.let {
                AdvisorJob {
                    this.position = row.position
                    this.company_id = it
                    this.start_date = row.start_date
                    this.end_date = row.end_date
                    this.is_current = true
                }
            }
            advisor.apply {
                this.sourced_by_id = row.user_id!!
                this.create_by_id = row.user_id!!

                this.background = row.background
                this.email = row.emails.firstOrNull() ?: ""
                this.firstname = row.name.split(" ")[0]
                this.lastname = row.name.removePrefix("${this.firstname} ")

                this.location_id = row.locations.mapNotNull { s -> map_location[s] }.firstOrNull()?.id

                this.jobs = job?.let { listOf(it) }
                this.contact_infos = personal_usable_contact_infos
                this.is_linkedin_premium = row.is_linkedin_premium ?: false
            }

            map_row_advisor[row] = advisor
            advisor
        }

        // save advisor, 同时update row.imported_advisor_id
        val update_advisors = mutableListOf<Advisor>()
        val update_rows = mutableListOf<AdvisorXlsRow>()
        val res = advisors.mapNotNull {
            try {
                advisorCreationService.create_v1(it)  // 还是原来的object
                val row = map_row_advisor.entries
                    .first { entry -> entry.value == it }
                    .key

                row.imported_advisor_id = it.id
                update_rows.add(row)

                it.create_by_id = row.user_id!!
                update_advisors.add(it)

                it
            } catch (e: Exception) {
                logger.info("error occur during advisor import: [${CoreJsonUtil.stringify(it)}]")
                e.printStackTrace()
                null
            }
        }
        advisorRepository.batchPatch(update_advisors, setOf(Advisor::create_by_id.name))
        repo.batchPatch(update_rows, setOf(AdvisorXlsRow::imported_advisor_id.name))
        return res
    }

    fun import_to_project_as_tasks(
        query: AdvisorXlsRow.Query,
    ): ImportAsTaskResults {
        val all_rows = AdvisorXlsRow.findAll(query, Includes.setOf(AdvisorXlsRow::imported_advisor))
        val rows = all_rows
            .distinctBy { "${it.result_advisor_id}-${it.target_project_id ?: 0}" }  // <- very important !
            .filter { it.result_advisor_id != null }
            .filter {
                it.user_id != null
                        && it.target_project_id != null
                        && it.target_project_id!! > 0
                        && it.imported_task_id == null
            }

        val map_projectid_rows = rows.groupBy {
            it.target_project_id!!
        }

        val map_projectid_exist_advisorids = Task.findAll(
            Task.Query(
                project_ids = map_projectid_rows.keys
            )
        ).groupBy {
            it.project_id
        }.mapValues {
            it.value.ids(Task::advisor_id)
        }

        /**
         * Quick lookup for existing [LeadGroup] values, avoids many queries to `lead_group` table
         */
        val map_projectid_exist_lead_groups = LeadGroup.findAll(
            LeadGroup.Query(
                project_ids = map_projectid_rows.keys
            )
        ).groupBy {
            it.project_id
        }.mapValues {
            it.value.groupBy { value -> value.name }.toMutableMap()
        }.toMutableMap()

        val total_removed_requests = mutableListOf<ClientBlacklistService.TaskRemoval>()
        val total_requests = mutableListOf<TaskCreateRequest>()

        map_projectid_rows.forEach { (project_id, project_rows) ->
            val exist_advisor_ids = map_projectid_exist_advisorids[project_id].orEmpty()
            val map_userid_rows = project_rows.groupBy { it.user_id!! }
            val project = Project.get(
                project_id, include = Includes.setOf(
                    Project::members,
                )
            )

            val project_lead_groups = map_projectid_exist_lead_groups.getOrPut(project_id) {
                mutableMapOf()
            }

            map_userid_rows.forEach { (user_id, rows) ->
                var requests = rows
                    .filter {
                        it.result_advisor_id!! !in exist_advisor_ids
                    }.map { cur_row ->
                        val target_lead_group: LeadGroup? = if (project.enable_lead_group) {
                            val lead_group_name = cur_row.lead_group_name.takeUnless { it.isNullOrEmpty() }
                                ?: LeadGroupService.DEFAULT_LEAD_GROUP_NAME

                            project_lead_groups.getOrPut(lead_group_name) {
                                // Only save if not already present
                                listOf(LeadGroup.save(LeadGroup {
                                    this.project_id = cur_row.target_project_id!!
                                    this.name = lead_group_name
                                }))
                            }.first()
                        } else {
                            null
                        }
                        TaskCreateRequest(
                            task = Task {
                                this.advisor_id = cur_row.result_advisor_id
                                this.willingness = TaskWillingnessType.UNKNOWN
                                this.lead_group_id = target_lead_group?.id
                            }
                        )
                    }

                val client_id = project.client_id ?: com.cpvsn.core.util.extension.biz_error("client_id not found")
                val removed_requests = clientBlacklistService.validate_task_and_remove(
                    client_id = client_id,
                    advisor_ids = requests.mapNotNull { it.task?.advisor_id }.toSet()
                )

                total_removed_requests.addAll(removed_requests)
                requests = requests.filter { request ->
                    request.task?.advisor_id !in removed_requests.map { it.advisor_id }.toSet()
                }
                total_requests.addAll(requests)

                val tasks = taskCreationService.create_batch(
                        project_id = project_id,
                        requests = requests,
                        user_id = user_id,
                )
                val update_tasks = mutableListOf<Task>()
                val update_rows = mutableListOf<AdvisorXlsRow>()
                tasks.forEach {
                    it.create_by_id = user_id
                    update_tasks.add(it)

                    val row = rows.firstOrNull { row -> row.result_advisor_id == it.advisor_id }
                    if (row != null) {
                        row.imported_task_id = it.id
                        update_rows.add(row)
                    }
                }
                taskRepository.batchPatch(update_tasks, setOf(Task::create_by_id.name))
                repo.batchPatch(update_rows, setOf(AdvisorXlsRow::imported_task_id.name))
            }
        }
        return ImportAsTaskResults(
            removed_requests = total_removed_requests,
            added_requests = total_requests
        )
    }

    class ImportAsTaskResults(
        val removed_requests: MutableList<ClientBlacklistService.TaskRemoval>,
        val added_requests: MutableList<TaskCreateRequest>
    )
    //endregion
}

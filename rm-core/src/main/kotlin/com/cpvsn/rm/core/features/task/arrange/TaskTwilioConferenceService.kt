package com.cpvsn.rm.core.features.task.arrange

import com.cpvsn.core.util.extension.assert_not_null
import com.cpvsn.core.util.extension.assert_valid_id
import com.cpvsn.core.util.extension.biz_require
import com.cpvsn.core.util.extension.biz_require_not_null
import com.cpvsn.crud.model.Includes
import com.cpvsn.crud.model.Patch
import com.cpvsn.crud.model.dot
import com.cpvsn.crud.pagination.PageRequest
import com.cpvsn.crud.pagination.Sort
import com.cpvsn.crud.util.ids
import com.cpvsn.rm.core.config.oversea.OverSeaEnvService
import com.cpvsn.rm.core.extensions.to_ids
import com.cpvsn.rm.core.features.auth.portal.getIPortal
import com.cpvsn.rm.core.features.auth.portal.getPortal
import com.cpvsn.rm.core.features.client.Client
import com.cpvsn.rm.core.features.client.contact.ClientContact
import com.cpvsn.rm.core.features.core.app_config.AppConfigEntryService
import com.cpvsn.rm.core.features.core.app_config.AppConfigKey
import com.cpvsn.rm.core.features.email.EmailService
import com.cpvsn.rm.core.features.email_template.EmailTemplate
import com.cpvsn.rm.core.features.email_template.PlaceholderBasedEmailTemplateService
import com.cpvsn.rm.core.features.email_template.constant.EmailContentType
import com.cpvsn.rm.core.features.email_template.models.PlaceholderBasedModelIds
import com.cpvsn.rm.core.features.portal.PortalType
import com.cpvsn.rm.core.features.portal.clientcontact.ClientPortalAssetListEmailAuth
import com.cpvsn.rm.core.features.portal.common.Portal
import com.cpvsn.rm.core.features.portal.common.PortalService
import com.cpvsn.rm.core.features.portal.common.payload.ClientAssetAccessSigninPayload
import com.cpvsn.rm.core.features.portal.common.payload.TaskConferenceAssetPayload
import com.cpvsn.rm.core.features.project.Project
import com.cpvsn.rm.core.features.project.ProjectClientContact
import com.cpvsn.rm.core.features.project.ProjectMember
import com.cpvsn.rm.core.features.slack.SlackService
import com.cpvsn.rm.core.features.slack.SlackService2
import com.cpvsn.rm.core.features.slack.config.SlackConfig
import com.cpvsn.rm.core.features.task.Task
import com.cpvsn.rm.core.features.task.advisor_consent.TaskAdvisorRecordConsent
import com.cpvsn.rm.core.features.task.arrange.enums.ArrangementType
import com.cpvsn.rm.core.features.task.arrange.enums.ConferenceParticipantRole
import com.cpvsn.rm.core.features.task.arrange.pojo.AssetAcquireEmailTokenResponse
import com.cpvsn.rm.core.features.task.arrange.pojo.AssetAcquireVerificationEmailResponse
import com.cpvsn.rm.core.features.task.arrange.pojo.ClientAssetListResponse
import com.cpvsn.rm.core.features.task.arrange.pojo.ConferenceOutboundDialRequest
import com.cpvsn.rm.core.features.task.constant.BridgeType
import com.cpvsn.rm.core.features.task.constant.TranscriptType
import com.cpvsn.rm.core.features.thirdparty.deepgram.DeepgramRestApiService
import com.cpvsn.rm.core.features.twiliosdk.*
import com.cpvsn.rm.core.features.twiliosdk.config.TwilioProperties
import com.cpvsn.rm.core.features.twiliosdk.entity.*
import com.cpvsn.rm.core.features.twiliosdk.enums.TwilioAddOnEnum
import com.cpvsn.rm.core.features.twiliosdk.enums.TwilioConferenceParticipantStatus
import com.cpvsn.rm.core.features.twiliosdk.enums.TwilioConferenceStatus
import com.cpvsn.rm.core.features.twiliosdk.enums.TwilioVoiceCallStatus
import com.cpvsn.rm.core.features.twiliosdk.request.TwiCallback
import com.cpvsn.rm.core.features.twiliosdk.scheduler.TwilioInternalScheduler
import com.cpvsn.rm.core.features.user.User
import com.cpvsn.rm.core.features.zoom.meeting.entity.*
import com.cpvsn.rm.core.features.zoom.meeting.pojo.WordRenderTranscriptData
import com.cpvsn.rm.core.util.*
import com.cpvsn.web.auth.AuthContext
import com.deepoove.poi.XWPFTemplate
import com.ibm.icu.text.RuleBasedNumberFormat
import com.twilio.http.HttpMethod
import com.twilio.jwt.accesstoken.AccessToken
import com.twilio.jwt.accesstoken.VoiceGrant
import com.twilio.rest.api.v2010.account.Call
import com.twilio.twiml.VoiceResponse
import com.twilio.twiml.voice.*
import com.twilio.type.PhoneNumber
import com.twilio.type.Twiml
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.core.io.ClassPathResource
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.io.ByteArrayOutputStream
import java.security.SecureRandom
import java.time.Instant
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit
import java.util.*


@Service
class TaskTwilioConferenceService {

    //region @
    private val logger = LoggerFactory.getLogger(this::class.java)
    private val secureRandom = SecureRandom()

    @Autowired
    private lateinit var twilioProperties: TwilioProperties

    @Autowired
    private lateinit var twilioConferenceSDK: TwilioConferenceSDK

    @Autowired
    private lateinit var twilioVoiceRestapiService: TwilioVoiceRestapiService

    @Autowired
    private lateinit var twilioInternalScheduler: TwilioInternalScheduler

    @Autowired
    private lateinit var twilioVoiceConferenceService: TwilioVoiceConferenceService

    @Autowired
    private lateinit var twilioWebhookService: TwilioWebhookService

    @Autowired
    private lateinit var portalService: PortalService

    @Autowired
    private lateinit var portalRepo: Portal.Repo

    @Suppress("SpringJavaInjectionPointsAutowiringInspection")
    @Autowired
    private lateinit var overSeaEnvService: OverSeaEnvService

    @Autowired
    private lateinit var placeholderBasedEmailTemplateService: PlaceholderBasedEmailTemplateService

    @Autowired
    private lateinit var emailService: EmailService

    @Autowired
    private lateinit var slackService: SlackService

    @Autowired
    private lateinit var slackService2: SlackService2

    @Autowired
    private lateinit var slackConfig: SlackConfig

    @Autowired
    private lateinit var appConfigEntryService: AppConfigEntryService

    //endregion

    //region deprecated
    fun handle_join_conference_after_gather_human(
        conference_id: Int,
        request: TwiCallback.GatherActionCallbackRequest,
    ): String {
        val conference = TwilioVoiceConference.get(
            conference_id, Includes.setOf(
                TwilioVoiceConference::conference_participants
            )
        )
        val participant = conference.conference_participants.orEmpty()
            .firstOrNull {
                // 坑: "abc".endsWith("") == true
                it.phone_number.isNotBlank() &&
                        (it.phone_number == request.Called || request.Called.endsWith(it.phone_number))
            }
        val response = join_conference(
            conference,
            participant = participant
        )
        return response
    }

    //endregion

    //region twilio/join-conference
    @Deprecated("deprecated approach")
    fun handle_incoming_call_webaudio(
        request: TwiCallback.IncomingCallRequest,
    ): String {
        check(request.TaskId.isNotBlank())
        val arrangement = TaskArrangement.firstOrNull(
            query = TaskArrangement.Query(task_id = request.TaskId.toInt()),
            include = Includes.setOf(
                TaskArrangement::twilio_voice_conference dot TwilioVoiceConference::conference_participants
            )
        ).assert_not_null()

        val is_accessable = check_conference_is_accessible(arrangement.twilio_voice_conference!!)
        if (!is_accessable) {
            val isFinishedResponse = VoiceResponse.Builder()
                .pause(Pause.Builder().length(2).build())
                .say(Say.Builder("Sorry, the conference has finished. Goodbye.").build())
                .build()
            return isFinishedResponse.toXml()
        }

        val participant = arrangement.twilio_voice_conference!!.conference_participants.orEmpty()
            .firstOrNull { it.device_identity == request.Caller.removePrefix("client:") }

        global_executor.execute {
            slackService.slack_notify_participant_joining(
                conference = arrangement.twilio_voice_conference!!,
                participant = participant.assert_not_null()
            )
        }

        return join_conference(conference = arrangement.twilio_voice_conference!!, participant = participant)
    }

    // https://stackoverflow.com/questions/73281607/twilio-how-to-dial-two-multiple-numbers-and-join-them-to-conference-using-twim
    // 也可以用Participant.Creator
    // 是否需要让callee选择加入/拒绝？ (<Gather> Press 1 then join, Press 2 then hangup)
    // 另一种方法：https://www.twilio.com/docs/voice/api/conference-participant-resource#create-a-participant
    /**
     * 外呼的时候不直接把callee加入会议
     * 只有当确定对方接听了、而非voicemail设置的自动回复 —— 通过gather引导确认接听
     * 才在处理gather后将其加入会议
     * p.s. AMD 应答机检测不适合我们的场景，因为AMD需要听到声音才能判断. call screen更合适我们的场景
     */
    fun try_add_conference_participant_by_outbound_dial(
        from: String,
        to: String,
        twilioVoiceConference: TwilioVoiceConference,
        participantLable: String,
    ): Call {
        require(participantLable.isNotBlank())

        /*
         坑：中国的号码由于防诈骗因素不能搜集DTMT input, 被呼叫者一按键就会导致通话被运营商挂断
         Twilio support ticket: Call hang up during the gather process when dialing out to a Chinese phone number
         walkaround: 对于外呼中国的童话，使用Gather
         */
        val should_gather_speech_countries = listOf("+86")
        val should_gather_speech = should_gather_speech_countries.any {
            to.startsWith(it)
        }

        val response = if (should_gather_speech) {
            val say =
                Say.Builder("Welcome to Capvision’s conference. If you agree to join the conference, please answer with 'yes'.")
                    .build()
            val gather = Gather.Builder()
                .action(twilioWebhookService.getGatherHumanPickupActionUrl(twilioVoiceConference.id))
                .method(HttpMethod.POST)
                .inputs(Gather.Input.SPEECH)
                .speechTimeout("auto")
                .maxSpeechTime(10)
                .build()
            VoiceResponse.Builder()
                .say(say)
                .gather(gather)
                .say(say)
                .gather(gather)
                .build()
        } else {
            // 绝大多数场景
            // https://www.twilio.com/docs/voice/twiml/gather?code-sample=code-complex-gather-with-actionmethod-and-nested-say&code-language=Java&code-sdk-version=9.x
            val say =
                Say.Builder("Welcome to Capvision’s conference, please press 1 on your phone to enter the meeting.")
                    .build()
            val gather = Gather.Builder()
                .action(twilioWebhookService.getGatherHumanPickupActionUrl(twilioVoiceConference.id))
                .method(HttpMethod.POST)
                .say(say)
                .numDigits(1)
                .timeout(7)
                .build()
            VoiceResponse.Builder()
                .gather(gather)
                .gather(gather)
                .build()
        }

        val call = Call.creator(
            PhoneNumber(to),
            PhoneNumber(from),
            Twiml(response.toXml())
        ).create()
        return call
    }

    fun get_client(
        conference_id: Int
    ): Client? {
        val arrangement = TaskArrangement.firstOrNull(
            query = TaskArrangement.Query(twilio_voice_conference_id = conference_id),
            include = Includes.setOf(
                TaskArrangement::task dot Task::client
            )
        )
        val client = arrangement?.task?.client
        return client
    }

    private fun join_conference(
        conference: TwilioVoiceConference,
        participant: TwilioVoiceConferenceParticipant? = null,
    ): String {
        // TwiML- coference common part
        val twiConferenceBuilder = build_conference_twiml_common_part(
            conference = conference,
            participant = participant
        )
        // TwiML- participant specific part
        if (participant != null) {
            twiConferenceBuilder.participantLabel(TwilioIdentifierUtils.generate_unique_participant_label(participant))
        }

        val joinConferenceResponse = VoiceResponse.Builder()
            .apply { add_welcoming(this, conference.twilio_conference_sid) }
//            .apply {
//                // https://www.notion.so/capvision/Urgent-Twilio-update-for-client-ID-92-1cf23199f41a802ba21fc6cf9982b7ce
//                val client = get_client(conference_id = conference.id)
//                add_compliance_announcement(this, conference.twilio_conference_sid)
//            }
            .dial(Dial.Builder().conference(twiConferenceBuilder.build()).build())
            .build()
        logger.debug(joinConferenceResponse.toXml())
        return joinConferenceResponse.toXml()
    }

    /**
     * Basic TwiML <Conference> Noun .
     * common part: callback, record
     * @see https://www.twilio.com/docs/voice/twiml/conference
     */
    private fun build_conference_twiml_common_part(
        conference: TwilioVoiceConference,
        participant: TwilioVoiceConferenceParticipant? = null,
    ): Conference.Builder {
        val builder = Conference.Builder(conference.friendly_name)
            .waitMethod(HttpMethod.GET)
            .waitUrl("${twilioProperties.public_static_files_path}/twilio-conference-waiting-music-116216-loop4.mp3")
        // .waitUrl("${twilioProperties.public_static_files_path}/twilio-conference-waiting-music-116216.mp3")
        // .waitUrl("http://twimlets.com/holdmusic?Bucket=com.twilio.music.ambient")

        // https://www.notion.so/capvision/Twilio-Remove-the-beep-for-Client-Compliance-participant-type-28d91c5d886e49c58d1b6c9953478bba
        if (participant?.role == ConferenceParticipantRole.CLIENT_COMPLIANCE) {
            builder.muted(true)
                .beep(Conference.Beep.FALSE)
                .startConferenceOnEnter(false)
                .endConferenceOnExit(false)
        } else {

            // 如果已经有一个chaperone角色连接，此时新加入的参会者不会自动开始会议，而是要等待第二个非chapone角色连接后才开始会议
            // A conference does not start until at least two participants join and at least one participant is a moderator (startConferenceOnEnter=true)
            val (none_chaperone_count, chaperone_count) = get_current_active_participation_count(conference.twilio_conference_sid)
            // fix: 是第一个none chaperone, 但如果不是会议室已经有chaperone的话，也可以startOnEnter
            val should_start_conference_on_enter = (chaperone_count == 0) || (none_chaperone_count != 0)

            builder.muted(false)
                .startConferenceOnEnter(should_start_conference_on_enter)
                //.startConferenceOnEnter(false)
                .endConferenceOnExit(false)
        }

        // statusCallback: https://www.twilio.com/docs/voice/twiml/conference#attributes-statusCallback
        val statusCallbackEvents = listOf(
            Conference.Event.START,
            Conference.Event.END,
            Conference.Event.JOIN,
            Conference.Event.LEAVE,
            Conference.Event.HOLD,
            Conference.Event.ANNOUNCEMENT
        )
        builder
            .statusCallbackMethod(HttpMethod.POST)
            .statusCallbackEvents(statusCallbackEvents)
            .statusCallback(twilioWebhookService.getConferenceStatusCallbackUrl(conference.id))

        // participantLabel:
        // https://www.twilio.com/docs/voice/twiml/conference?code-sample=code-join-an-evented-conference&code-language=Java&code-sdk-version=8.x#attributes-participantLabel

        // "When participants join and leave, notification sounds are played to inform the other participants"
        // https://www.twilio.com/docs/voice/twiml/conference#attributes-beep

        // 录音recording
        // https://support.twilio.com/hc/en-us/articles/223132867-Recording-a-Phone-Call-with-Twilio#conf_record
        if (conference.enable_record) {
            // https://www.twilio.com/docs/voice/twiml/conference#attributes-recording-status-callback
            builder
                .recordingStatusCallbackMethod(HttpMethod.POST)
                .recordingStatusCallbackEvents(listOf(*Conference.RecordingEvent.values()))
                .recordingStatusCallback(twilioWebhookService.getConferenceRecordingStatusCallbackUrl(conference.id))
                .record(Conference.Record.RECORD_FROM_START)
        } else {
            builder.record(Conference.Record.DO_NOT_RECORD)
        }

        // 转录transcription
        // https://support.twilio.com/hc/en-us/articles/223133027-Transcribe-entire-phone-calls-with-Twilio
        //TODO 怎么不对conference开启transcription？(可能需要recording,但不需要transcription? 而这个add-on只能统一设置？)

        return builder
    }

    private fun check_conference_is_accessible(
        conference: TwilioVoiceConference,
    ): Boolean {
        // currently we remove check here, so invitee perhaps dial in at any time even if the conference has actually completed.
//        if (conference.expected_end_time != null) {
//            return Instant.now().isBefore(conference.expected_end_time!!.plus(30L, ChronoUnit.MINUTES))
//        }
        return true
    }


    fun should_announce_compliance(
        conference: TwilioVoiceConference,
        conferenceInstance: TwilioVoiceConferenceInstance
    ): Boolean {
        // if announced before, no need to announce again
        if (conferenceInstance.is_compliance_announced) return false

        // only for target client
        val target_client_ids = appConfigEntryService.find(AppConfigKey.TWILIO_COMPLIANCE_ANNOUNCEMENT_FOR_CLIENT_IDS)
            ?.value.orEmpty().to_ids()
        val client = get_client(conference_id = conference.id) ?: return false
        if (client.id !in target_client_ids) return false

        val active_participants = twilioVoiceRestapiService.get_conference_active_participants(
            conference_sid = conference.twilio_conference_sid
        )
        val expert_count = active_participants.count {
            it.label.startsWith(ConferenceParticipantRole.EXPERT.name.toLowerCase())
        }
        val client_count = active_participants.count {
            it.label.startsWith(ConferenceParticipantRole.CLIENT.name.toLowerCase())
                    && !it.label.startsWith(ConferenceParticipantRole.CLIENT_COMPLIANCE.name.toLowerCase())
                    && !it.label.startsWith(ConferenceParticipantRole.CLIENT_SPONSOR.name.toLowerCase())
        }
        val is_both_connected = client_count > 0 && expert_count > 0
        return is_both_connected
    }

    // https://www.notion.so/capvision/Urgent-Twilio-update-for-client-ID-92-1cf23199f41a802ba21fc6cf9982b7ce
    fun maybe_announce_compliance(
        conference: TwilioVoiceConference,
        conferenceInstance: TwilioVoiceConferenceInstance
    ): com.twilio.rest.api.v2010.account.Conference? {
        val should_announce = should_announce_compliance(conference, conferenceInstance)
        if (!should_announce) return null
        val res = twilioVoiceRestapiService.announce_in_conference(
            conference_sid = conference.twilio_conference_sid,
            announce_method = HttpMethod.GET,
            announce_url = "${twilioProperties.public_static_files_path}/compliance-annoucement-1.mp3"
        )
        // log announced
        Patch.fromMutator(conferenceInstance) {
            this.is_compliance_announced = true
        }.patch()
        return res
    }

    //region welcoming
    private fun add_welcoming(
        builder: VoiceResponse.Builder,
        sid: String,
    ): VoiceResponse.Builder {
        val (none_chaperone_count, _) = get_current_active_participation_count(sid)
        val ordinal = none_chaperone_count + 1

        builder.apply {
            if (ordinal > 10) {
                // https://stackoverflow.com/questions/6810336/is-there-a-way-in-java-to-convert-an-integer-to-its-ordinal-name
                val ordinal_str = RuleBasedNumberFormat(Locale.UK, RuleBasedNumberFormat.SPELLOUT)
                    .format(ordinal.toLong(), "%spellout-ordinal")
                val msg = "Welcome to Capvision’s conference system. You are the $ordinal_str participant."
                this.say(Say.Builder(msg).build())

            } else {
                val welcoming_audio_uri =
                    "${twilioProperties.public_static_files_path}/Capvision-Welcome-$ordinal.mp3"
                this.play(Play.Builder().loop(1).url(welcoming_audio_uri).build())
            }
        }
        return builder
    }

    @Deprecated("")
    private fun add_compliance_announcement(
        builder: VoiceResponse.Builder,
        sid: String
    ): VoiceResponse.Builder {
//        val announcement_text = """
//Our client requested that we provide you the following message prior to participating in this consultation with them:
//We buy and sell the securities of public companies on a regular basis.
//We do not want any material non-public information or confidential information.
//By participating in this call, you agree not to discuss any material non-public information or information that is subject to a confidentiality obligation.
//If you have information that is labeled “Confidential” or that for any reason should not be disclosed, like information covered by a confidentiality agreement, we do not want to receive that information.
//If you provide us with confidential information, we both could be charged with insider trading.
//If you are employed, you have informed your employer and you are permitted by your employer to discuss the information that is the subject of this call.
//A member of the client’s compliance department may listen to this call.
//        """.trimIndent()
//        builder.apply {
//            this.say(Say.Builder(announcement_text).voice(Say.Voice.WOMAN).build())
//        }
        val audio_uri = "${twilioProperties.public_static_files_path}/compliance-annoucement-1.mp3"
        builder.apply {
            this.play(
                Play.Builder().loop(1).url(audio_uri).build()
            )
        }
        return builder
    }

    /**
     * @return first: none-chaperone count， second: chaperone count
     */
    private fun get_current_active_participation_count(
        conference_sid: String,
    ): Pair<Int, Int> {
        // if this is the first participant, then the conference hasn't been created here
        val res = if (conference_sid.isNotBlank()) {
            val active_participants = twilioVoiceRestapiService.get_conference_active_participants(
                conference_sid = conference_sid
            )
            val partition = active_participants.partition {
                /**
                 * @see com.cpvsn.rm.core.features.twiliosdk.TwilioIdentifierUtils.generate_unique_participant_label
                 *
                 */
                !it.label.startsWith(ConferenceParticipantRole.CLIENT_COMPLIANCE.name.toLowerCase())
            }

            Pair(partition.first.size, partition.second.size)
        } else {
            Pair(0, 0)
        }
        return res
    }


    //endregion

    //endregion


    //region twilio/portal-dial-page
    /**
     * 向指定的手机号外呼，将其加入会议
     */
    @Transactional
    fun spawn_outbound_dial_into_conference(
        task_id: Int,
        request: ConferenceOutboundDialRequest,
        role: ConferenceParticipantRole,
        trigger_slack_notification: Boolean = false,
        arrangement_type: ArrangementType = ArrangementType.COMMON
    ): Call {
        val arrangement = TaskArrangement.firstOrNull(
            query = TaskArrangement.Query(
                task_id = task_id,
                type = arrangement_type
            ),
            include = Includes.setOf(
                TaskArrangement::twilio_voice_conference dot TwilioVoiceConference::conference_participants
            )
        )
        biz_checked_not_null(arrangement?.twilio_voice_conference) {
            "Conference invalid"
        }
        val twilio_phone = choose_dial_in_phone_number("")
        val is_accessable = check_conference_is_accessible(arrangement!!.twilio_voice_conference!!)
        biz_require(is_accessable) {
            "Conference is finished."
        }

        val call = try_add_conference_participant_by_outbound_dial(
            from = twilio_phone,
            to = request.phone_number,
            twilioVoiceConference = arrangement.twilio_voice_conference!!,
            participantLable = TwilioIdentifierUtils.generate_unique_participant_label(role, request.phone_number)
        )

        // update participant: 若不存在phone number对应的participant需新建记录
        var participant = arrangement.twilio_voice_conference!!.conference_participants.orEmpty()
            .firstOrNull { it.phone_number == request.phone_number }
        if (participant == null) {
            val new_participant = TwilioVoiceConferenceParticipant {
                this.twilio_voice_conference_id = arrangement.twilio_voice_conference_id.assert_valid_id()
                this.phone_number = request.phone_number
                this.status = TwilioConferenceParticipantStatus.NOT_JOINED_YET
                this.role = role
            }
            participant = TwilioVoiceConferenceParticipant.save(new_participant)
        }

        if (trigger_slack_notification) {
            global_executor.execute {
                slackService.slack_notify_participant_joining(
                    conference = arrangement.twilio_voice_conference!!,
                    participant = participant
                )
            }
        }

        return call
    }

    /**
     * js-sdk相当于直接打给TwiML App，不经过Twilio phone number
     * (voice url配置在App里， 而phone number也不过是App的入口）
     *
     * https://www.twilio.com/ja/docs/voice/sdks/javascript#twiml-applications
     *
     * Access Token: 生成一个token供客户打开portal页后通话用
     * Api key: api-key is created from: NewKey.creator().setFriendlyName("").create()
     *
     */
    @Deprecated("deprecated approach")
    fun create_twilio_jwt_access_token_for_participation(
        task_id: Int,
        role: ConferenceParticipantRole,
        email_address: String = "",
        name: String = "",
    ): String {
        val arrangement = TaskArrangement.firstOrNull(
            query = TaskArrangement.Query(
                task_id = task_id
            ),
            include = Includes.setOf(
                TaskArrangement::twilio_voice_conference dot TwilioVoiceConference::conference_participants
            )
        ).assert_not_null()

        val jwt_identity = TwilioIdentifierUtils.get_js_device_identity(
            arrangement,
            email_address,
            name,
            timestamp = Instant.now()
        )
        val jwt_token = generate_access_token(
            identity = jwt_identity,
            custom_params = mapOf(
                TwiCallback.IncomingCallRequest::IsJsDevice.name to true.toString(),
                TwiCallback.IncomingCallRequest::TaskId.name to arrangement.task_id.toString(),
                TwiCallback.IncomingCallRequest::EmailAddress.name to email_address,
                TwiCallback.IncomingCallRequest::InputName.name to name
            )
        )

        // update participant
        val participant = arrangement.twilio_voice_conference!!.conference_participants.orEmpty()
            .firstOrNull {
                (it.email_address == email_address && it.email_address.isNotBlank())
                        || (it.name == name && it.name.isNotBlank())
            }
        if (participant != null) {
            Patch.fromMutator(participant) {
                this.device_identity = jwt_identity
            }.patch()
        } else {
            val p = TwilioVoiceConferenceParticipant {
                this.twilio_voice_conference_id = arrangement.twilio_voice_conference_id.assert_valid_id()
                this.status = TwilioConferenceParticipantStatus.NOT_JOINED_YET
                this.device_identity = jwt_identity
                this.email_address = email_address
                this.name = name
                this.role = role
            }
            TwilioVoiceConferenceParticipant.save(p)
        }

        return jwt_token
    }

    fun kick_out_participant(
        participant_id: Int,
    ): List<Call> {
        val participant = TwilioVoiceConferenceParticipant.find(participant_id)
            ?: return emptyList()
        val calls = TwilioVoiceConferenceCall.findAll(
            TwilioVoiceConferenceCall.Query(
                participant_id = participant_id,
                status = TwilioVoiceCallStatus.CONNECTED
            )
        )
        // https://www.twilio.com/docs/voice/api/call-resource?code-sample=code-update-the-statuscallback-of-an-active-call&code-language=Java&code-sdk-version=9.x
        val res = calls.map {
            Call.updater(it.call_sid).setStatus(Call.UpdateStatus.COMPLETED).update()
        }
        return res
    }

    // token
    // https://www.twilio.com/docs/iam/access-tokens#step-3-generate-token
    // 当参会者通过js-device拨入时, 这里的identity将作为Call里的from
    private fun generate_access_token(
        identity: String,
        custom_params: Map<String, String>,
    ): String {
        val voiceGrant = VoiceGrant()
            .setOutgoingApplication(
                twilioProperties.client_dial_app_sid,
                custom_params
            )
            .setIncomingAllow(false)
        val acessToken = AccessToken.Builder(
            twilioProperties.account_sid,
            twilioProperties.client_dial_api_key,
            twilioProperties.client_dial_api_secret
        ).identity(identity)
            .ttl(3 * 60 * 60)
            .grant(voiceGrant)
            .build()

        val res = acessToken.toJwt()
        return res
    }
    //endregion

    //region assets acquire
    fun create_portal_ticket_for_twilio_asset_acquire(
        task_id: Int,
    ): Portal {
        val task = Task.get(task_id)
        val exist = Portal.findAll(
            Portal.Query(
                type = PortalType.TASK_CONFERENCE_ASSET,
                task_id = task_id,
                disable = false
            )
        )
        val ticket = Portal {
            this.project_id = task.project_id
            this.task_id = task_id
            this.type = PortalType.TASK_CONFERENCE_ASSET
            this.payload_obj = TaskConferenceAssetPayload()
        }
        portalService.create(ticket)
        // disable former portals
        val patches = exist.map {
            it.disable = true
            it
        }
        portalRepo.batchPatch(patches, fields = setOf(Portal::disable.name))
        return ticket
    }

    fun verify_input_email(
        input_email_address: String,
        task_id: Int,
        asset_ticket: Portal,
        require_compliance_review: Boolean,
        compliance_emails: List<String>,
    ): AssetAcquireVerificationEmailResponse {
        require(input_email_address.isNotBlank())
        val task = Task.get(
            task_id, Includes.setOf(
                Task::project dot Project::client_contacts
            )
        )
        val approved = is_asset_portal_link_approved(
            input_email_address,
            require_compliance_review,
            compliance_emails,
            task
        )
        if (!approved) {
            return AssetAcquireVerificationEmailResponse(approved = false)
        }

        val verification_result = verify_individual_page_input_email(input_email_address, task, compliance_emails)
        if (!verification_result) {
            val support_uid = listOfNotNull(
                task.support_uid,
                task.lead_uid,
                task.update_by_id,
                task.create_by_id
            ).first()
            val supporter = User.get(support_uid, Includes.setOf(User::phone_number))
            return AssetAcquireVerificationEmailResponse(
                allow = false,
                support_email = supporter.email,
                support_phone_number = supporter.phone_number,
                approved = true
            )
        }

        val pin_code = send_email_pin_code(input_email_address)
        val visitor = TaskConferenceAssetPayload.Visitor(
            email = input_email_address,
            pin_code = pin_code
        )
        Patch.fromMutator(asset_ticket) {
            this.payload_obj = (asset_ticket.payload_obj as TaskConferenceAssetPayload).update_visitor(visitor)
        }.patch()
        return AssetAcquireVerificationEmailResponse(allow = true, approved = true)
    }

    fun verify_individual_page_input_email(
        input_email_address: String,
        task: Task,
        compliance_emails: List<String>,
    ): Boolean {
        val allow_emails = task.project?.client_contacts.orEmpty().mapNotNull { it.email }
            .plus(compliance_emails.filter { it.isNotBlank() })
            .plus(
                task.project?.external_contacts_for_recording_list.orEmpty().filter { it.isNotBlank() }
            )
        return allow_emails.any { it.equals(input_email_address, ignoreCase = true) }
    }

    //https://www.notion.so/capvision/3-PRIORITY-Twilio-Compliance-setting-for-transcripts-and-recordings-1c7d544e85d340ddab7ee9afda499218
    fun is_asset_portal_link_approved(
        input_email_address: String,
        require_compliance_review: Boolean,
        compliance_emails: List<String>,
        task: Task
    ): Boolean {
        return when {
            !require_compliance_review -> true
            compliance_emails.any { it.equals(input_email_address, ignoreCase = true) } -> true
            task.asset_portal_link_approved ?: false -> true
            else -> false
        }
    }

    fun send_email_pin_code(
        input_email_address: String,
    ): String {
        val pin_code = RandomUtil.random_numeric(targetLength = 6)
        val email = placeholderBasedEmailTemplateService.process_first_by_data(
            query = EmailTemplate.Query(
                is_system_template = true,
                is_draft = false,
                content_type = EmailContentType.CONFERENCE_ASSET_TEMPORARY_PIN
            ),
            data = PlaceholderBasedModelIds().fetch_data()
        ).to_email_request()
        email.to_list = listOf(input_email_address)
        email.content = email.content.replace(
            "{{PIN_CODE}}",
            pin_code
        )
        emailService.send(email)
        return pin_code
    }

    fun verify_email_pin_code(
        email: String,
        pin_code: String,
        ticket: Portal,
    ): AssetAcquireEmailTokenResponse {
        val visitors = (ticket.payload_obj as TaskConferenceAssetPayload).visitors
        val visitor = visitors.firstOrNull { it.email == email }

        biz_check(visitor?.pin_code == pin_code) {
            "Invalid code, please retry."
        }
        // login success
        val token = generate_email_auth_token(email)
        return AssetAcquireEmailTokenResponse(email_token = token)
    }

    fun check_portal_permission(
        portal: Portal?,
        task: Task
    ) {
        when (portal?.type) {
            PortalType.CLIENT_ASSET_ACCESS_SIGNIN -> {
                val payload = portal.payload_obj as? ClientAssetAccessSigninPayload
                payload?.let {
                    biz_check(task.client_id == it.client_id) { "Permission denied." }
                } ?: throw IllegalArgumentException("Invalid sign-in data.")
            }

            PortalType.TASK_CONFERENCE_ASSET,
            PortalType.CONTACT_ADVISOR_RECOMMENDATION -> {
                val project = Project.get(portal.project_id!!)
                biz_check(task.client_id == project.client_id ) { "Permission denied." }
            }

            else -> {}
        }
    }

    fun get_task_twilio_assets(
        task_id: Int,
    ): List<TwilioAsset> {
        val arrangement = TaskArrangement.firstOrNull(
            query = TaskArrangement.Query(
                task_id = task_id
            ),
            sort = Sort.empty(),
            include = Includes.setOf(
                TaskArrangement::twilio_voice_conference
            )
        )
        val assets = arrangement?.twilio_voice_conference?.let {
            twilioVoiceConferenceService.get_assets(it)
        }.orEmpty()
        return assets
    }

    fun get_task_zoom_assets(
        task_id: Int,
    ): List<ZoomAsset> {
        val arrangement = TaskArrangement.firstOrNull(
            query = TaskArrangement.Query(task_id = task_id)
        ) ?: return emptyList()
        var instances = ZoomMeetingInstance.findAll(
            ZoomMeetingInstance.Query(
                meeting_entity_id = arrangement.zoom_meeting_entity_id
            )
        )
        // Multiple instances may occur if participants accidentally joined before start or after meeting end
        if (instances.size > 1) {
            instances = instances.filter { it.participants_count > 1 }
        }
        var assets = ZoomAsset.findAll(
            ZoomAsset.Query(
                meeting_uuid_in = instances.ids(ZoomMeetingInstance::meeting_uuid)
            )
        )
        // If multiple valid instances remain, select the one with the largest MP4 recording
        if (instances.size > 1) {
            assets.filter { it.file_type == ZoomAsset.FileType.MP4 }
                .maxByOrNull { it.file_size ?: 0 }
                ?.meeting_uuid
                ?.let { uuid ->
                    assets = assets.filter { it.meeting_uuid == uuid }
                }
        }

        // For each file type within the same instance, retain the asset with the maximum file size or maximum id
        return assets.groupBy { it.recording_type }
            .mapValues { (_, grouped_assets) ->
                grouped_assets.maxWithOrNull(
                    compareBy<ZoomAsset> { it.file_size ?: 0 }
                        .thenBy { it.id }
                )
            }
            .values
            .filterNotNull()
    }

    fun generate_zoom_video_url(
        task: Task,
    ): String {
        val zoom_assets = get_task_zoom_assets(task.id)
        val mp4 = zoom_assets.firstOrNull {it.file_type == ZoomAsset.FileType.MP4 }
        biz_require_not_null(mp4) { "The video file is unavailable for download." }
        return "${mp4.share_url}?pwd=${mp4.recording_play_passcode}"
    }

    fun get_recording_file_url(
        task: Task,
    ): String {
        val asset: Asset? = when (task.arrange_bridge_type) {
            BridgeType.TWILIO -> {
                val twilio_assets = get_task_twilio_assets(task.id)
                twilio_assets.filter {
                    it.type == TwilioAsset.Type.VOICE_CONFERENCE_RECORDING && it.capvision_file_url.isNotBlank()
                }.maxByOrNull { it.id }
            }

            BridgeType.ZOOM -> {
                val zoom_assets = get_task_zoom_assets(task.id)
                var res = zoom_assets.filter {
                    it.file_type == ZoomAsset.FileType.M4A && it.capvision_file_url.isNotBlank()
                }.maxByOrNull { it.id }
                if (res == null) {
                    res = zoom_assets.filter { it.file_type == ZoomAsset.FileType.MP4 }.maxByOrNull { it.id }
                    res?.let {
                        it.capvision_file_url = "${it.share_url}?pwd=${it.recording_play_passcode}"
                    }
                }
                res
            }

            else -> null
        }
        biz_require_not_null(asset) { "The recording file is unavailable for download." }
        return asset!!.capvision_file_url
    }

    fun get_transcript_asset_file_url(
        task: Task,
    ): String {
        val arrangement = TaskArrangement.firstOrNull(
            query = TaskArrangement.Query(task_id = task.id)
        )

        val asset: Asset? = when (task.arrange_bridge_type) {
            BridgeType.TWILIO -> {
                val twilio_assets = get_task_twilio_assets(task.id)
                val twilio_transcripts = twilio_assets.filter {
                    it.type == TwilioAsset.Type.VOICE_CONFERENCE_TRANSCRIPTION && it.capvision_file_url.isNotBlank()
                }
                val res = if (arrangement?.transcription_type == TranscriptType.DEEPGRAM) {
                    twilio_transcripts.filter { it.add_on == TwilioAddOnEnum.deepgram }
                        .maxByOrNull { it.id }
                        ?: twilio_transcripts.maxByOrNull { it.id }
                } else {
                    twilio_transcripts.maxByOrNull { it.id }
                }
                res
            }

            BridgeType.ZOOM -> {
                val zoom_assets = get_task_zoom_assets(task.id)
                val zoom_transcripts = zoom_assets.filter {
                    it.file_type == ZoomAsset.FileType.TRANSCRIPT && it.capvision_file_url.isNotBlank()
                }
                val res = if (arrangement?.transcription_type == TranscriptType.DEEPGRAM) {
                    zoom_transcripts.filter { it.recording_type == DeepgramRestApiService.SERVICE_NAME }
                        .maxByOrNull { it.id }
                        ?: zoom_transcripts.maxByOrNull { it.id }
                } else {
                    zoom_transcripts.maxByOrNull { it.id }
                }
                res
            }

            else -> null
        }
        biz_require_not_null(asset) { "The transcription file is unavailable for download." }
        return asset!!.capvision_file_url
    }

    // region generate transcript file
    fun build_transcript_conversations(
        content: String,
        zoom_expert_names: List<String>? = null,
        task: Task? = null,
    ): List<WordRenderTranscriptData.Conversation> {
        val result = mutableListOf<WordRenderTranscriptData.Conversation>()
        var current_speaker: String? = null
        val current_content = StringBuilder()

        content.lines().forEach { line ->
            if (line.startsWith("WEBVTT") ||
                line.isBlank() ||
                line.matches(Regex("^\\d+$")) ||
                line.matches(Regex("\\d{2}:\\d{2}:\\d{2}\\.\\d{3} --> \\d{2}:\\d{2}:\\d{2}\\.\\d{3}"))
            ) {
                return@forEach
            }

            if (line.contains(":")) { // 如果行中包含冒号，说明是新的 speaker
                if (current_speaker != null) {
                    // 保存当前 speaker 和内容
                    result.add(
                        WordRenderTranscriptData.Conversation(
                            speaker_name = current_speaker!!,
                            content = current_content.toString().trim()
                        )
                    )
                }
                val parts = line.split(":", limit = 2) // 按冒号分割，最多分为两部分
                current_speaker =
                    if (zoom_expert_names != null && zoom_expert_names.any { it.equals(parts[0], true) }) {
                        "Expert ${task?.display_id ?: ""}:"  // blind expert name
                    } else {
                        parts[0].toUpperCase().trim() // 第一个部分是 speaker 名字
                    }

                current_content.clear()
                current_content.append(parts[1].trim()) // 第二部分是内容
            } else {
                // 如果当前行没有冒号，则是前一行内容的分段，追加到当前内容
                current_content.append("\n\n").append(line.trim())
            }
        }
        return result
    }

    fun blind_expert_name_transcript_conversations(
        content: String,
        task: Task,
    ): List<WordRenderTranscriptData.Conversation> {
        val arrangement = TaskArrangement.firstOrNull(
            query = TaskArrangement.Query(task_id = task.id)
        )

        if (arrangement?.bridge_type != BridgeType.ZOOM) return build_transcript_conversations(content)

        val zoom_expert_names = ZoomMeetingEntry.findAll(
            ZoomMeetingEntry.Query(
                meeting_entity_id = arrangement.zoom_meeting_entity_id
            )
        ).filter { it.role == ConferenceParticipantRole.EXPERT }
            .map { it.user_name }

        return build_transcript_conversations(content, zoom_expert_names, task)
    }

    fun build_transcript_data(
        content: String,
        task: Task,
        is_blind_expert: Boolean = false,
    ): WordRenderTranscriptData {
        Task.join(
            task, Includes.setOf(
                Task::advisor,
                Task::project,
                Task::angle,
            )
        )
        val formatter = DateTimeFormatter.ofPattern("EEEE, MMMM d, yyyy")
        return WordRenderTranscriptData(
            project_name = task.project?.name ?: "",
            angle_topic = task.angle?.topic?.let { ": $it" },
            consultation_date = task.start_time?.atZone(ZoneId.systemDefault())?.format(formatter),
            expert_name = if (is_blind_expert) null else {
                task.advisor?.let { advisor ->
                    val name_parts = listOf(advisor.name_prefix, advisor.firstname, advisor.lastname)
                    val fullName = name_parts.filter { !it.isNullOrBlank() }.joinToString(" ")
                    "Expert: $fullName"
                }
            },
            conversations = if (is_blind_expert) blind_expert_name_transcript_conversations(
                content,
                task
            ) else build_transcript_conversations(content)
        )
    }

    fun generate_word_transcript_as_byte_array(
        content: String,
        task: Task,
        is_blind_expert: Boolean = false,
    ): ByteArray {
        val resource = ClassPathResource("templates/transcript/transcript_template.docx")
        val input_stream = resource.inputStream
        val data = build_transcript_data(content, task, is_blind_expert)
        val output_stream = ByteArrayOutputStream()
        XWPFTemplate.compile(input_stream).render(data).writeAndClose(output_stream)
        return output_stream.toByteArray()
    }

    fun generate_pdf_transcript_as_byte_array(
        content: String,
        task: Task,
        is_blind_expert: Boolean = false,
    ): ByteArray {
        val word_byte_array = generate_word_transcript_as_byte_array(content, task, is_blind_expert)
        return AsposeUtil.word_to_pdf(word_byte_array)
    }

    enum class TranscriptFileType {
        WORD,
        PDF
    }

    fun generate_transcript_file_name(
        task: Task,
        type: TranscriptFileType,
        is_blind_expert: Boolean,
    ): String {
        val expert_name = if (is_blind_expert) task.display_id ?: "" else listOf(
            task.advisor?.name_prefix,
            task.advisor?.firstname,
            task.advisor?.lastname
        ).filter { !it.isNullOrBlank() }.joinToString(" ")
        val formatter = DateTimeFormatter.ofPattern("MMM d, yyyy")
        val date = task.start_time?.atZone(ZoneId.systemDefault())?.format(formatter)
        val file_name = buildString {
            append("Capvision Transcript")
            task.tsid?.let { append("_$it") }
            append("Capvision - ")
            task.angle?.name?.let { append("$it ") }
            append("Consultation with ")
            append(expert_name)
            date?.let { append(" - $it") }
            when (type) {
                TranscriptFileType.WORD -> append(".docx")
                TranscriptFileType.PDF -> append(".pdf")
            }
        }
        return file_name
    }
    // endregion
    // region client asset list
    /**
     * Verifies if the provided input_email_address is associated with the given client, project, or compliance emails.
     */
    fun verify_client_input_email(
        input_email_address: String,
        project: Project?,
        compliance_emails: List<String>,
        client: Client,
    ): AssetAcquireVerificationEmailResponse {
        require(input_email_address.isNotBlank())

        val verification_result = verify_list_page_input_email(input_email_address, project, client, compliance_emails)
        if (!verification_result) {
            return AssetAcquireVerificationEmailResponse(
                allow = false,
                approved = true
            )
        }

        val pin_code = send_email_pin_code(input_email_address)
        val visitor = TaskConferenceAssetPayload.Visitor(
            email = input_email_address,
            pin_code = pin_code
        )
        if (client.client_portal_visitor_obj == null) {
            client.client_portal_visitor_obj = TaskConferenceAssetPayload()
        }
        Patch.fromMutator(client) {
            this.client_portal_visitor_obj = client.client_portal_visitor_obj!!.update_visitor(visitor)
        }.patch()
        return AssetAcquireVerificationEmailResponse(allow = true, approved = true)
    }

    fun verify_list_page_input_email(
        input_email_address: String,
        project: Project?,
        client: Client,
        compliance_emails: List<String>,
    ): Boolean {
        val allow_emails = ClientContact.findAll(
            ClientContact.Query(
                client_id = client.id,
                status_in = setOf(ClientContact.Status.ACTIVE, ClientContact.Status.PROSPECT),
                is_blocked = false
            )
        )
            .map { it.email }
            .plus(compliance_emails.filter { it.isNotBlank() })
            .plus(
                project?.external_contacts_for_recording_list?.filter { it.isNotBlank() } ?: emptyList()
            )
        return allow_emails.any { it.equals(input_email_address, ignoreCase = true) }
    }

    fun generate_asset_access_signin_token(
        input_email_address: String,
        client: Client,
        is_compliance: Boolean,
    ): AssetAcquireVerificationEmailResponse {
        val pin_code = send_email_pin_code(input_email_address)
        val visitor = TaskConferenceAssetPayload.Visitor(
            email = input_email_address,
            pin_code = pin_code
        )
        if (client.client_portal_visitor_obj == null) {
            client.client_portal_visitor_obj = TaskConferenceAssetPayload()
        }
        Patch.fromMutator(client) {
            this.client_portal_visitor_obj = client.client_portal_visitor_obj!!.update_visitor(visitor)
        }.patch()

        val portal = portalService.create(
            Portal {
                type = PortalType.CLIENT_ASSET_ACCESS_SIGNIN
                is_internal = false
                project_id = null
                payload_obj = ClientAssetAccessSigninPayload(client_id = client.id, is_compliance = is_compliance)
            },
            duration = null
        )
        return AssetAcquireVerificationEmailResponse(allow = true, approved = true, token = portal.token)
    }

    fun verify_client_email_pin_code(
        email: String,
        pin_code: String,
        client: Client,
    ): AssetAcquireEmailTokenResponse {
        val visitors = client.client_portal_visitor_obj?.visitors
        val visitor = visitors?.firstOrNull { it.email == email }
        biz_check(visitor?.pin_code == pin_code) {
            "Invalid code, please retry."
        }
        // login success
        val token = generate_email_auth_token(email)
        return AssetAcquireEmailTokenResponse(email_token = token)
    }

    // https://www.notion.so/capvision/Client-Portal-Post-release-changes-for-transcript-list-c4bbe379c13249a1bf360f9660e13960
    fun generate_email_auth_token(
        email: String,
    ): String {
        var email_auth = ClientPortalAssetListEmailAuth.firstOrNull(
            ClientPortalAssetListEmailAuth.Query(email = email)
        )

        val email_auth_token = RandomUtil.random_alpha_numeric(targetLength = 12, random = secureRandom)
        val portal = AuthContext.getIPortal() as? Portal
        val is_email_login_access = portal?.type == PortalType.CLIENT_ASSET_ACCESS_SIGNIN

        if (email_auth == null) {
            // create email auth
            email_auth = ClientPortalAssetListEmailAuth {
                this.email = email
                this.token = email_auth_token
                this.expire_at = Instant.now().plus(30, ChronoUnit.DAYS)
                if (is_email_login_access) {
                    this.sign_in_token = portal?.token ?: ""
                }
            }
            ClientPortalAssetListEmailAuth.save(email_auth)
        } else {
            // update expire time and token
            Patch.fromMutator(email_auth) {
                this.expire_at = Instant.now().plus(30, ChronoUnit.DAYS)
                this.token = email_auth_token
                if (is_email_login_access) {
                    this.sign_in_token = portal?.token ?: ""
                }
            }.patch()
        }

        return email_auth.token
    }

    enum class AccessType {
        TASK_ASSET,
        CLIENT_ASSET,
        SIGN_IN_PAGE,
    }
    fun verify_email_auth_token(
        token: String,
        access_type: AccessType,
        require_compliance_review: Boolean?,
        compliance_emails: List<String>,
    ): ClientPortalAssetListEmailAuth? {
        val email_auth = ClientPortalAssetListEmailAuth.firstOrNull(
            ClientPortalAssetListEmailAuth.Query(token = token)
        ) ?: return null

        if (email_auth.expire_at < Instant.now()) return null

        when (access_type) {
            AccessType.TASK_ASSET -> {
                val ticket = AuthContext.getPortal(PortalType.TASK_CONFERENCE_ASSET)
                val task_id = ticket.task_id.assert_valid_id()
                val task = Task.get(
                    task_id,
                    Includes.setOf(Task::project dot Project::client_contacts)
                )
                val approved = is_asset_portal_link_approved(
                    email_auth.email,
                    require_compliance_review!!,
                    compliance_emails,
                    task
                )
                if (!approved) return null

                val verification_result = verify_individual_page_input_email(email_auth.email, task, compliance_emails)
                if (!verification_result) return null
            }

            AccessType.CLIENT_ASSET -> {
                val portal = AuthContext.getIPortal() as? Portal
                val project_id = portal?.project_id.assert_valid_id()
                val project = Project.find(project_id, Includes.setOf(Project::client))
                val client = project?.client.assert_not_null { "Client cannot be null." }
                val verification_result = verify_list_page_input_email(email_auth.email, project, client, compliance_emails)
                if (!verification_result) return null
            }

            AccessType.SIGN_IN_PAGE -> {
                if (email_auth.sign_in_token.isBlank()) return null
            }
        }

        return email_auth
    }

    fun get_client_asset_list(
        email: String,
        pageRequest: PageRequest,
        query: Task.Query,
        extra: Includes?,
        client_id: Int,
        require_compliance_review: Boolean?,
        compliance_emails: List<String>?,
        has_compliance_or_legal_role: Boolean?,
    ): ClientAssetListResponse {
        require(email.isNotBlank())

        val is_compliance_email = (compliance_emails?.any { it.equals(email, ignoreCase = true) } == true) ||
                has_compliance_or_legal_role == true
        // When the front-end provides project IDs for filtering, project_ids != null
        val q = if (query.project_ids == null) {
            // Determine accessible projects based on user permissions when no pre-filtered project_ids provided
            when {
                // Compliance reviewers can access all projects
                is_compliance_email -> {
                    query.copy(client_id = client_id)
                }

                // Regular users can only access their related projects
                else -> {
                    val email_related_projects = Project.findAll(
                        Project.Query(
                            client_id = client_id,
                            or = listOf(
                                Project.Query(external_contacts_for_recording_contains = email),
                                Project.Query(project_client_contacts = ProjectClientContact.Query(client_contact = ClientContact.Query(email = email))),
                                Project.Query(member = ProjectMember.Query(user = User.Query(email = email))),
                            )
                        )
                    )
                    query.copy(
                        project_ids = email_related_projects.ids()
                    )
                }
            }
        } else query
        var enriched_query = q.copy(
            arrangement = TaskArrangement.Query(
                enable_record = true,
                or = listOf(
                    TaskArrangement.Query(twilio_voice_conference = TwilioVoiceConference.Query(status = TwilioConferenceStatus.ENDED, enable_record = true)),
                    TaskArrangement.Query(zoom_meeting = ZoomMeeting.Query(status = ZoomMeeting.Status.ENDED, enable_record = true))
                )
            ),
            advisor_record_consent = TaskAdvisorRecordConsent.Query(is_consent = true),
        )
        if (require_compliance_review == true && !is_compliance_email) {
            enriched_query = enriched_query.copy(
                asset_portal_link_approved = true
            )
        }
        val sorted_page_request = pageRequest.copy(sort = Sort.by { desc("conference_start_at") })
        val tasks = Task.list(enriched_query, sorted_page_request, extra.orEmpty())

        val angle = tasks.items.mapNotNull { it.angle?.name }.distinct()
        val project = tasks.items.mapNotNull { it.project }.distinct()
        val assets_status = build_assets_status(tasks.items)

        return ClientAssetListResponse(tasks, angle, assets_status, project)
    }

    fun build_assets_status(
        tasks: List<Task>
    ): List<Map<String, Any>> {
        val arrangement_list = tasks.mapNotNull { it.arrangement }
        TaskArrangement.join(
            arrangement_list, Includes.setOf(
                TaskArrangement::twilio_voice_conference
                        dot TwilioVoiceConference::instances
                        dot TwilioVoiceConferenceInstance::twilio_asset,
                TaskArrangement::zoom_meeting
                        dot ZoomMeeting::instances
                        dot ZoomMeetingInstance::zoom_assets
            )
        )

        // twilio
        val map_taskId_twilioAssets = arrangement_list
            .filter { it.bridge_type == BridgeType.TWILIO }
            .associateBy(
                { it.task_id },
                { arrangement ->
                    // Filter instances with more than one participant
                    arrangement.twilio_voice_conference?.instances?.filter { it.participants_count > 1 }
                        ?.flatMap { it.twilio_asset.orEmpty() }
                }
            )
        val twilio_assets_status = map_taskId_twilioAssets.map { entry ->
            val exist_recording = entry.value?.any { it.type == TwilioAsset.Type.VOICE_CONFERENCE_RECORDING } ?: false
            val exist_transcription =
                entry.value?.any { it.type == TwilioAsset.Type.VOICE_CONFERENCE_TRANSCRIPTION } ?: false
            mapOf("task_id" to entry.key, "recording" to exist_recording, "transcription" to exist_transcription)
        }

        // zoom
        val map_taskId_zoomAssets = arrangement_list
            .filter { it.bridge_type == BridgeType.ZOOM }
            .associateBy(
                { it.task_id },
                { arrangement ->
                    // Filter instances with more than one participant
                    arrangement.zoom_meeting?.instances?.filter { it.participants_count > 1 }
                        ?.flatMap { it.zoom_assets.orEmpty() }
                }
            )
        val zoom_assets_status = map_taskId_zoomAssets.map { entry ->
            val exist_recording = entry.value?.any {
                it.file_type == ZoomAsset.FileType.M4A || it.file_type == ZoomAsset.FileType.MP4
            } ?: false
            val exist_transcription = entry.value?.any { it.file_type == ZoomAsset.FileType.TRANSCRIPT } ?: false
            val exist_video = entry.value?.any { it.file_type == ZoomAsset.FileType.MP4 } ?: false
            mapOf("task_id" to entry.key, "recording" to exist_recording, "transcription" to exist_transcription, "video" to exist_video)
        }

        return twilio_assets_status + zoom_assets_status
    }
    // endregion

    //endregion


    /**
     * 目前只有一个号码，New York的
     */
    fun choose_dial_in_phone_number(
        country: String,
    ): String {
        val phone_numbers = TwilioPhoneNumber.findAll()
            .sortedBy { it.phone_number }
        return phone_numbers.first().phone_number
    }

}
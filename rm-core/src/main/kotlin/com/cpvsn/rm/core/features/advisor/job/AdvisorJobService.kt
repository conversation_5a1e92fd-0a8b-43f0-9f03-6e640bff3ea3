package com.cpvsn.rm.core.features.advisor.job

import com.cpvsn.core.util.extension.biz_require_not_null
import com.cpvsn.crud.model.Patch
import com.cpvsn.rm.core.config.Webpage
import com.cpvsn.rm.core.features.advisor.Advisor
import com.cpvsn.rm.core.features.email.EmailService
import com.cpvsn.rm.core.features.email_template.EmailTemplate
import com.cpvsn.rm.core.features.email_template.PlaceholderBasedEmailTemplateService
import com.cpvsn.rm.core.features.email_template.constant.EmailContentType
import com.cpvsn.rm.core.features.email_template.models.PlaceholderBasedModelIds
import com.cpvsn.rm.core.features.inquiry.model.InquiryQuestion
import com.cpvsn.rm.core.features.misc.company.CompanyAlias
import com.cpvsn.rm.core.features.misc.company.CompanyBlacklistService
import com.cpvsn.rm.core.features.portal.advisor.PortalAdvisor
import com.cpvsn.rm.core.features.project.Project
import com.cpvsn.rm.core.features.user.tag.UserAdvisorTag
import com.cpvsn.rm.core.features.user.tag.UserAdvisorTagMap
import com.cpvsn.rm.core.util.*
import kong.unirest.Unirest
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDate

@Service
class AdvisorJobService {

    private val logger = LoggerFactory.getLogger(this::class.java)

    @Autowired
    private lateinit var companyBlacklistService: CompanyBlacklistService

    @Autowired
    private lateinit var placeholderBasedEmailTemplateService: PlaceholderBasedEmailTemplateService

    @Autowired
    private lateinit var emailService: EmailService

    fun validate_experience(
        experience: AdvisorExperience,
    ): ExperienceLegalValidateResult {
        return validate_experience_batch(listOf(experience)).first()
    }

    fun validate_experience_batch(
        experiences: Collection<AdvisorExperience>,
    ): List<ExperienceLegalValidateResult> {
        val blacklist_company_names = companyBlacklistService
            .get_blacklist_company_names()
        val blacklist_company_aliases = companyBlacklistService
            .get_blacklist_company_aliases()
        return experiences.map { experience ->
            internal_validate(
                experience,
                blacklist_company_names = blacklist_company_names,
                blacklist_company_aliases = blacklist_company_aliases
            )
        }
    }

    /**
     * 当前在黑名单公司工作或者离职黑名单公司不足半年专家 不满足合规要求
     * 另外，公司只匹配上了黑名单公司的昵称，我们给予提示
     */
    internal fun internal_validate(
        experience: AdvisorExperience,
        blacklist_company_names: Set<String>,
        blacklist_company_aliases: List<CompanyAlias>,
    ): ExperienceLegalValidateResult {
        val six_months_before = LocalDate.now()
            .minusMonths(6)
        val company_name = experience.company_name
        val success = ExperienceLegalValidateResult.from_experience(
            experience,
            valid = true,
        )
        if (experience.company_name.isBlank()) {
            // fixme: there must be something wrong
            // Temporarily, we just hide the problem.
            logger.warn("blank company name")
            return success
        }
        val left_date = experience.end_date?.let {
            AdvisorJobDateUtil.parse_experience_end_date_str_for_legal_validate(it)
        }
        if (!experience.is_current
            && (
                    // currently, when we don't know left_date, we treat it as valid experience.
                    left_date == null || left_date.isBefore(six_months_before))
        ) {
            // there's no need to validate this experience
            // as the advisor has left the company for at least 6 months
            return success
        }
        if (company_name in blacklist_company_names) {
            return ExperienceLegalValidateResult.from_experience(
                experience,
                valid = false,
                error_message = "Company '$company_name' is banned in DB",
            )
        }

        val warn_companies = blacklist_company_aliases.filter {
            it.match(company_name)
        }

        if (warn_companies.isNotEmpty()) {
            return ExperienceLegalValidateResult.from_experience(
                experience,
                valid = true,
                warn_companies = warn_companies
            )
        }

        return success
    }

    // region npi
    // https://www.notion.so/capvision/New-Project-Type-Physician-Consultation-23cd178a65374a2ca11d5640d2415dac
    @Transactional
    fun validate_npi_number(
        npi_number: String,
        advisor_id: Int,
        is_from_advisor_profile: Boolean,
    ): ValidateNpiResponse {
        // firstly, we check our DB to avoid unnecessary third party api call
        val advisor = Advisor.get(advisor_id)
        when {
            npi_number == advisor.npi && advisor.npi_status == Advisor.NpiStatus.VALID ->
                return ValidateNpiResponse.SUCCESS

            npi_number != advisor.npi && advisor.npi_status == Advisor.NpiStatus.VALID
                    && !is_from_advisor_profile ->
                return ValidateNpiResponse.MISMATCH
        }
        val npi_exist = AdvisorNpiRegistry.firstOrNull(
            AdvisorNpiRegistry.Query(npi_number = npi_number)
        )
        if (npi_exist == null) {
            val res = Unirest.get(NpiRegistryConfig.api_url)
                .queryString("number", npi_number)
                .queryString("version", NpiRegistryConfig.version)
                .asObject {
                    JacksonUtil.objectMapper.readValue(it.contentAsString, NpiRegistryResponse::class.java)
                }.body
            if (res.result_count == 0) return ValidateNpiResponse.FAILURE

            val advisor_npi_registry = res.results.first().toAdvisorNpiRegistry()
            AdvisorNpiRegistry.save(advisor_npi_registry)
        }
        return ValidateNpiResponse.SUCCESS
    }

    fun save_advisor_npi_number_from_sq(
        advisor_id: Int,
        qa_list: List<InquiryQuestion>,
        portal: PortalAdvisor? = null
    ) {
        // Retrieve the NPI number from the first question tagged with ADVISOR_NPI_CONFIRM
        val npi_number = qa_list.firstOrNull { it.tags.contains(InquiryQuestion.Tag.ADVISOR_NPI_CONFIRM) }
            ?.answer?.content?.get("result")?.toString()
            ?.takeIf { it.isNotBlank() && it.matches(Regex("\\d{10}")) }
            ?: return

        val advisor = Advisor.get(advisor_id)
        val duplicate_advisor = Advisor.findAll(Advisor.Query(npi = npi_number))
            .firstOrNull {
                it.npi_status == Advisor.NpiStatus.VALID && it.id != advisor_id
            }
        when (duplicate_advisor) {
            null -> {
                Patch.fromMutator(advisor) {
                    this.npi = npi_number
                    this.npi_status = Advisor.NpiStatus.VALID
                }.patch()
                add_advisor_taxonomy_tag(npi_number, advisor_id)
            }

            else -> {
                when (portal) {
                    null -> {
                        //if a DB user tries to add a duplicate NPI, just return an error
                        biz_error("This NPI number exists for another expert: ${duplicate_advisor.id}")
                    }

                    else -> {
                        //if an advisor adds a duplicate NPI, ban them and email them
                        Patch.fromMutator(duplicate_advisor) {
                            this.npi_status = Advisor.NpiStatus.DUPLICATE
                            this.status_enum = Advisor.Status.BLACKLIST
                        }.patch()
                        Patch.fromMutator(advisor) {
                            this.npi = npi_number
                            this.npi_status = Advisor.NpiStatus.DUPLICATE
                            this.status_enum = Advisor.Status.BLACKLIST
                        }.patch()
                        send_duplicate_npi_notification(
                            advisor,
                            duplicate_advisor,
                            npi_number,
                            portal
                        )
                    }
                }
            }
        }
    }

    fun add_advisor_taxonomy_tag(
        npi_number: String,
        advisor_id: Int
    ) {
        val taxonomies_desc = AdvisorNpiRegistry.firstOrNull(
            AdvisorNpiRegistry.Query(
                npi_number = npi_number
            )
        )?.taxonomies_desc.takeIf { !it.isNullOrBlank() }

        taxonomies_desc?.let { taxonomy ->
            val tags = taxonomy.split(",").map { it.trim() }.filter { it.isNotEmpty() }
            tags.forEach { tag_str ->
                var tag = UserAdvisorTag.firstOrNull(
                    UserAdvisorTag.Query(name = tag_str)
                )
                if (tag == null) {
                    val new_tag = UserAdvisorTag {
                        this.name = tag_str
                        this.color = "light-gray"
                    }
                    tag = UserAdvisorTag.save(new_tag)
                }

                val exists = UserAdvisorTagMap.firstOrNull(
                    UserAdvisorTagMap.Query(
                        tag_id = tag.id,
                        advisor_id = advisor_id
                    )
                )
                if (exists == null) {
                    UserAdvisorTagMap {
                        this.tag_id = tag.id
                        this.advisor_id = advisor_id
                    }.save()
                    // sync to es
                    InvokeUtil.trigger(Event.ADVISOR_DOC_CHANGED(advisor_id = advisor_id))
                }
            }
        }
    }

    fun send_duplicate_npi_notification(
        advisor: Advisor,
        duplicate_advisor: Advisor,
        npi_number: String,
        portal: PortalAdvisor
    ) {
        val project = Project.get(portal.project_id!!)
        val data =  PlaceholderBasedModelIds(
            advisor_id = portal.advisor_id,
            task_id = portal.task_id,
            client_id = project.client_id,
            project_id = portal.project_id,
            user_id = portal.create_by_id,
        ).fetch_data()
        val query = EmailTemplate.Query(
            is_draft = false,
            content_type = EmailContentType.NOTIFY_KM_NPI_DUPLICATE,
            is_system_template = true
        )
        val template = placeholderBasedEmailTemplateService.find_one_template(query, data)
        biz_require_not_null(template) { "No suitable template" }
        template!!.content_template = template!!.content_template.replace("{{NPI_NUMBER}}", npi_number)
        template!!.content_template = template!!.content_template
            .replace("{{EXPERT_NAME_2}}", duplicate_advisor.full_name)
        template!!.content_template = template!!.content_template
            .replace("{{EXPERT_PROFILE_URL_2}}", Webpage.AdvisorProfile(duplicate_advisor.id).url)
        val email = placeholderBasedEmailTemplateService.process_by_data(template!!, data).to_email_request()
        logger.debug { "about to send email $email" }
        emailService.send(email)
    }
    //endregion
}

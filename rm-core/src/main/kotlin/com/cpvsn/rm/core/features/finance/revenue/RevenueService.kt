package com.cpvsn.rm.core.features.finance.revenue

import com.cpvsn.core.svc.email.EmailRequest
import com.cpvsn.core.util.DateRangeUtil
import com.cpvsn.core.util.extension.BD
import com.cpvsn.core.util.extension.assert_exist
import com.cpvsn.core.util.extension.assert_valid_id
import com.cpvsn.crud.model.CrudOptions
import com.cpvsn.crud.model.Includes
import com.cpvsn.crud.model.Patch
import com.cpvsn.crud.model.dot
import com.cpvsn.rm.core.base.taskscoped.TaskScopedEntityService
import com.cpvsn.rm.core.config.oversea.OverSeaEnvService
import com.cpvsn.rm.core.extensions.*
import com.cpvsn.rm.core.features.bridge.Region
import com.cpvsn.rm.core.features.client.Client
import com.cpvsn.rm.core.features.contract.ContractCalcService
import com.cpvsn.rm.core.features.contract.ContractService
import com.cpvsn.rm.core.features.contract.PayWay
import com.cpvsn.rm.core.features.contract.entity.Contract
import com.cpvsn.rm.core.features.contract.entity.ContractPaymentItem
import com.cpvsn.rm.core.features.core.app_config.AppConfigEntryService
import com.cpvsn.rm.core.features.core.app_config.AppConfigKey
import com.cpvsn.rm.core.features.email.EmailService
import com.cpvsn.rm.core.features.email.pojo.EmailRequestPojo
import com.cpvsn.rm.core.features.email_template.EmailTemplate
import com.cpvsn.rm.core.features.email_template.PlaceholderBasedEmailTemplateService
import com.cpvsn.rm.core.features.email_template.constant.EmailAddressPlaceHolder
import com.cpvsn.rm.core.features.email_template.constant.EmailContentType
import com.cpvsn.rm.core.features.email_template.models.PlaceholderBasedModel
import com.cpvsn.rm.core.features.email_template.models.PlaceholderBasedModelIds
import com.cpvsn.rm.core.features.finance.cronjob.survey_reports.SurveyClientReportExcelModelV1
import com.cpvsn.rm.core.features.finance.cronjob.survey_reports.pojo.SurveyReportMapper
import com.cpvsn.rm.core.features.finance.invoice.Invoice
import com.cpvsn.rm.core.features.finance.invoice.InvoiceServiceCommon
import com.cpvsn.rm.core.features.finance.invoice.specified.InvoiceServicePayGoEachProject
import com.cpvsn.rm.core.features.finance.revenue.pojo.ClientUsageTableResult
import com.cpvsn.rm.core.features.finance.revenue.pojo.FinancialTransaction
import com.cpvsn.rm.core.features.project.Project
import com.cpvsn.rm.core.features.project.ProjectClientContact
import com.cpvsn.rm.core.features.project.pojo.UpateProjectChargeRequest
import com.cpvsn.rm.core.features.project.transaction.ReceivableTransaction
import com.cpvsn.rm.core.features.task.Task
import com.cpvsn.rm.core.features.task.constant.TaskType
import com.cpvsn.rm.core.features.task.pojo.TaskCompleteRequest
import com.cpvsn.rm.core.features.task.transfer.TaskTransferUtil
import com.cpvsn.rm.core.features.template.FileTemplate
import com.cpvsn.rm.core.features.template.FileTemplateService
import com.cpvsn.rm.core.features.user.UserService
import com.cpvsn.rm.core.util.*
import com.cpvsn.web.auth.AuthContext
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.ApplicationContext
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.math.BigDecimal
import java.time.Instant
import java.time.LocalDate
import java.time.ZoneId
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.time.temporal.TemporalAdjusters
import kotlin.reflect.KMutableProperty1
import kotlin.reflect.full.memberProperties


@Suppress("PARAMETER_NAME_CHANGED_ON_OVERRIDE")
@Service
class RevenueService : EventListener, TaskScopedEntityService {

    //region @
    private val log: Logger = LoggerFactory.getLogger(this.javaClass)

    @Autowired
    private lateinit var contractService: ContractService

    @Autowired
    private lateinit var contractCalcService: ContractCalcService

    @Autowired
    private lateinit var invoiceServiceCommon: InvoiceServiceCommon

    @Autowired
    private lateinit var invoiceServicePayGoEachProject: InvoiceServicePayGoEachProject

    @Autowired
    private lateinit var placeholderBasedEmailTemplateService: PlaceholderBasedEmailTemplateService

    @Autowired
    private lateinit var emailService: EmailService

    @Autowired
    private lateinit var appConfigEntryService: AppConfigEntryService

    @Autowired
    private lateinit var repo: RevenueRepo

    @Autowired
    private lateinit var applicationContext: ApplicationContext

    @Autowired
    private lateinit var userService: UserService

    @Autowired
    private lateinit var overSeaEnvService: OverSeaEnvService

    @Autowired
    private lateinit var receivableTransactionRepo: ReceivableTransaction.Repo

    @Autowired
    private lateinit var surveyReportMapper: SurveyReportMapper

    @Autowired
    private lateinit var fileTemplateService: FileTemplateService
    //endregion

    init {
        @Suppress("LeakingThis")
        InvokeUtil.register(
            this, interests = mapOf(
                Event.TASK_REVENUE_CHANGE::class to CallbackOption.Sync  //don't change this. otherwise may cause transaction problems
            )
        )
    }

    override fun onEvent(event: Event) {
        when (event) {
            is Event.TASK_REVENUE_CHANGE -> calculate_task(event.task_id)
        }
    }

    //region cronjob
    fun invoice_billing_job() {
        val enable = appConfigEntryService
            .find(AppConfigKey.ENABLE_PAYGO_BILLING_JOB)
            ?.get_value(Boolean::class) ?: false
        if (!enable) {
            log.info("according to app config(key='${AppConfigKey.ENABLE_PAYGO_BILLING_JOB}') value, paygo billing job won't run!")
            return
        }

        log.debug("start paygo billing job")
        val revenues = Revenue.findAll(
            Revenue.Query(
                contract_id_is_null = false,
                invoice_id_is_null = true,
                contract = Contract.Query(
                    payway_in = setOf(PayWay.PAYGO, PayWay.TRIAL, PayWay.PROJECT_BASE)
                )
            ), include = setOf(Revenue::contract.name)
        )

        log.debug("find ${revenues.size} unbound revenues")
        val contract_revenue_map = revenues.groupBy { it.contract_id!! }
        val contract_mapped_by_id = Contract.findAll(
            Contract.Query(
                ids = contract_revenue_map.keys
            )
        ).associateBy { it.id }

        contract_revenue_map.forEach { (contract_id, revenues) ->
            contract_mapped_by_id[contract_id]?.let { contract ->
                val res = when (contract.payway) {
                    PayWay.PAYGO -> {
                        val paygo_invoice_service = if (contract.invoice_for_each_project_monthly == true) {
                            invoiceServicePayGoEachProject
                        } else {
                            invoiceServiceCommon
                        }
                        paygo_invoice_service.create(contract, revenues)
                    }

                    else -> {
                        invoiceServiceCommon.create(contract, revenues)
                    }
                }
                log.info("generate ${res.size} ${contract.payway} invoice")
            }
        }

    }

    // not used for the time being
    fun prepay_billing_job(invoice_date: LocalDate) {
        //todo should we consider timezone here?
        // if so, should we use timezone of 'BILLING' client contact of the client in payment item?
        val payment_items = ContractPaymentItem.findAll(
            ContractPaymentItem.Query(
                invoice_date = invoice_date.toString(),
                payway = PayWay.PREPAY,
                invoice_id_is_null = true
            )
        )
        payment_items.forEach {
            val invoice = Invoice {
                this.contract_payment_item_id = it.id
                this.contract_id = it.contract_id
            }
            log.debug("generate invoice for prepay payment item(with id=${it.id}): $it")
            invoiceServiceCommon.create_legacy(invoice)
        }
    }

    fun remind_billing_job() {
        val date = LocalDate.now()
        val payment_items = ContractPaymentItem.findAll(
            ContractPaymentItem.Query(
                invoice_date = date.toString(),
                invoice_id_is_null = true,
                payway_in = setOf(
                    PayWay.PREPAY,
                    PayWay.PROJECT_BASE
                ),
                contract = Contract.Query(
                    approval_status = Contract.ApprovalStatus.APPROVED
                ),
            ), include = setOf(
                ContractPaymentItem::contract.name,
                "${ContractPaymentItem::contract.name}.${Contract::client.name}"
            )
        )
        payment_items.forEach { send_invoice_issuing_remainder(it) }
    }

    fun send_weekly_survey_client_report() {
        val items = surveyReportMapper.get_survey_client_report()
        val model = SurveyClientReportExcelModelV1(items)
        val template = FileTemplate {
            name = "survey-client-report"
            type = FileTemplate.Type.EXCEL
            media_type = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            content_type = FileTemplate.ContentType.CUSTOM
            file_name_template = "Survey Client Report.xlsx"
            template_url = "classpath:templates/report/survey_client_report_v1.xlsx"
        }
        val file = fileTemplateService.process(template, model).content
        val attachment = EmailRequest.Attachment(
            bytes = file,
            name = "survey-client-report.xlsx",
            media_type = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        )
        val email = EmailRequestPojo(
            subject = "Survey Client Report",
            content = "This document and all information within is internal and confidential.",
            attachments = listOf(attachment),
            to_list = EmailAddressPlaceHolder.CAPVISION_SURVEY_REPORT_GROUP.resolve_address(PlaceholderBasedModel())
                .map { it.email }
        )
        println(model)
        println(model.items.size)
        emailService.send(email)
    }

    private fun send_invoice_issuing_remainder(payment_term: ContractPaymentItem) {
        val email = placeholderBasedEmailTemplateService.process_first_by_data(
            EmailTemplate.Query(
                is_draft = false,
                content_type = EmailContentType.NOTIFY_PAYMENT_TERM_INVOICE_DATE,
                is_system_template = true
            ),
            PlaceholderBasedModel(
                contract_payment_term = payment_term,
                contract = payment_term.contract,
                client = payment_term.contract?.client
            )
        ).to_email_request()
        emailService.send(email)
    }
//endregion

    //region revenue builders
    private fun build_revenue_UNRESOLVED(
        task_id: Int, task: Task? = null,
    ): Revenue {
        val task = task ?: Task.get(task_id).assert_exist(task_id)
        return Revenue {
            this.task_id = task.id
            this.project_id = task.project_id
            this.client_id = task.client_id.assert_valid_id()
            this.billing_notes = task.billing_notes
            this.create_type = Revenue.CreateType.UNRESOLVED
            this.status = Revenue.Status.CREATED
            this.revenue_time = task.start_time.assert_exist()

            if (task.specified_charge_cash != null) {
                this.cash = task.specified_charge_cash
                this.cash_currency = task.specified_charge_cash_currency
            }
        }
    }

    private fun build_revenue_FULL(
        task_id: Int,
        contract_id: Int,
        billing_hours: BigDecimal,
        charge_cash: BigDecimal? = null,
        task: Task? = null,
        contract: Contract? = null,
    ): Revenue {
        val task = task ?: Task.get(
            id = task_id,
            include = Includes.setOf(
                Task::specified_client_rate_jit
            )
        ).assert_exist(task_id)
        val contract = contract ?: Contract.get(contract_id)
        return Revenue {
            this.client_id = contract.client_id
            this.project_id = task.project_id
            this.create_type = Revenue.CreateType.FULL
            this.task_id = task.id
            this.client_contact_id = task.client_contact_id
            this.billing_notes = task.billing_notes
            this.client_hours = task.client_hours
            this.status = Revenue.Status.CREATED
            this.revenue_time = caculate_revenue_time(task.start_time.assert_exist())
            this.billing_hours = billing_hours
            this.contract_id = contract.id

            this.cash_currency = task.specified_client_rate_currency_jit ?: contract.currency_enum
            this.cash = billing_hours * (task.specified_client_rate_jit ?: contract.unit_price_china ?: 0.BD)
            if (charge_cash != null) {
                this.cash = charge_cash
            }
            if (task.specified_charge_cash != null) {
                this.cash = task.specified_charge_cash
                this.cash_currency = task.specified_charge_cash_currency
            }
        }
    }

    private fun build_revenue_PART(
        task_id: Int,
        contract_id: Int,
        billing_hours: BigDecimal
    ): Revenue = build_revenue_FULL(task_id, contract_id, billing_hours).apply {
        create_type = Revenue.CreateType.PART
    }

    private fun build_revenue_OVERFLOW(
        task_id: Int,
        billing_hours: BigDecimal
    ): Revenue {
        val task = Task.get(task_id).assert_exist(task_id)
        return Revenue {
            this.task_id = task.id
            this.client_id = task.client_id.assert_valid_id()
            this.project_id = task.project_id
            this.client_contact_id = task.client_contact_id
            this.billing_notes = task.billing_notes
            this.billing_hours = billing_hours
            this.client_hours = task.client_hours
            this.status = Revenue.Status.CREATED
            this.contract_id = null
            this.create_type = Revenue.CreateType.OVERFLOW
            this.revenue_time = caculate_revenue_time(task.start_time.assert_exist())
        }
    }

    // Sea's financial request that the case of completing tasks across months, set the corresponding revenue time to the current time.
    private fun caculate_revenue_time(task_start_time: Instant): Instant {
        return if (Region.current == Region.SEA) {
            val time_zone = overSeaEnvService.timezone_via_profile()
            val start_time = task_start_time.atZone(time_zone)
            val current_time = ZonedDateTime.now(time_zone)
            if (start_time.year == current_time.year &&
                start_time.month == current_time.month
            ) {
                task_start_time
            } else {
                Instant.now()
            }
        } else {
            task_start_time
        }
    }

//endregion

    fun task_revenue_preview(task_id: Int, request: TaskCompleteRequest): Revenue {
        val task = Task.get(task_id, Includes.setOf(Task::has_billed_jit, Task::task_outsource_info))
        val task_patch = request.task
        if (task_patch != null) {
            if (Task::complete_time.name !in task_patch.fields) {
                task.complete_time = Instant.now()
            } else {
                task.complete_time = task_patch.entity.complete_time
            }
            if (Task::client_rate_usd.name !in task_patch.fields
                && task.project_sub_type == Project.SubType.Consultation
                && task.client_rate_usd == null
            ) {
                Task.join(
                    task, Includes.setOf(
                        Task::advisor,
                        Task::default_client_rate_usd
                    )
                )
                task.client_rate_usd = task.default_client_rate_usd
            }
            val taskClass = Task::class
            for (fieldName in task_patch.fields) {
                val prop = taskClass.memberProperties.find { it.name == fieldName }
                if (prop is KMutableProperty1<*, *>) {
                    @Suppress("UNCHECKED_CAST")
                    val mutableProp = prop as KMutableProperty1<Task, Any?>
                    val newValue = mutableProp.get(task_patch.entity)
                    mutableProp.set(task, newValue)
                }
            }
        }
        val client_id = task.client_id ?: biz_error("task client id not found")
        biz_require(
            task.type in setOf(
                TaskType.CONSULTATION,
                TaskType.CLIENT_TASK
            )
        ) { "Task must be consultation or client task." }
        if (task.charge_cash != null) {
            val assigned_contract = biz_required_not_null(
                contractService.resolve_task_if_assigned_contract(task.id)
            ) {
                "Charge cash can only be used when project is assigned to a PROJECT_BASE contract."
            }
            biz_check(assigned_contract.payway == PayWay.PROJECT_BASE) {
                "Assigned contract should be of payway: PROJECT_BASE."
            }
            return build_revenue_FULL(
                task.id,
                assigned_contract.id,
                billing_hours = 0.BD,
                charge_cash = task.charge_cash,
                task = task,
            )
        }
        val revenues = Revenue.findAll(Revenue.Query(task_id = task_id))
        val min_impact_time =
            (listOf(task.start_time!!) + revenues.map { it.revenue_time }).min()
        val affected_contracts = Contract.findAll(
            Contract.Query(
                client_id = client_id,
                approval_status = Contract.ApprovalStatus.APPROVED,
                payway_ne = PayWay.PROJECT_BASE,
                effective_status = Contract.EffectiveStatus.EFFECTIVE
            )
        ).filter { it.end_time >= min_impact_time }.sortedBy { it.start_time }

        biz_require(affected_contracts.size <= 2) { "Too complicated to handle more than two contracts." }

        val matched_contract =
            contractService.resolve_task_binding_by_time_match(task_id, task = task)
        if (matched_contract == null) {
            return build_revenue_UNRESOLVED(task_id, task = task)
        } else {
            matched_contract.apply {
                this.unit_price_china = unit_price_us
            }
            task.has_overseas = false
            val billing_hours =
                contractCalcService.evaluate_billing_hours_using_contract(
                    task_id,
                    matched_contract,
                    task = task,
                )
            return build_revenue_FULL(
                task_id,
                matched_contract.id,
                billing_hours,
                task = task,
                contract = matched_contract,
            )
        }
    }

    //region billing caculation
    @Transactional
    fun calculate_task(task_id: Int) {
        val task = Task.get(task_id).assert_exist(task_id)
        val client_id = task.client_id.assert_valid_id()
        biz_require(
            task.type in setOf(
                TaskType.CONSULTATION,
                TaskType.CLIENT_TASK
            )
        ) { "Task must be consultation or client task." }
        biz_require(task.has_completed) { "Not allow to calculate incomplete task." }

        // if charge_cash is not null, it should be a project base contract
        if (task.charge_cash != null) {
            handle_project_base_contract(task)
            return
        }

        val revenues = Revenue.findAll(Revenue.Query(task_id = task_id))

        // considering the worst case, the impact on the following tasks. (task.start_time may not eq revenue_time)
        val min_impact_time =
            (listOf(task.start_time!!) + revenues.map { it.revenue_time }).min()
        // check invoiced revenues
//        val may_affected_revenues = Revenue.findAll(
//            Revenue.Query(
//                client_id = client_id,
//                revenue_time_gte = min_impact_time,
//                contract = Contract.Query(
//                    payway_ne = PayWay.PROJECT_BASE,
//                ),
//            )
//        )
//        biz_require(may_affected_revenues.none { it.is_invoiced }) {
//            "There are invoiced task occurred after this task, not allow to change it."
//        }

        // if more than 2 contracts are affected, refuse to process.
        val affected_contracts = Contract.findAll(
            Contract.Query(
                client_id = client_id,
                approval_status = Contract.ApprovalStatus.APPROVED,
                payway_ne = PayWay.PROJECT_BASE,
                effective_status = Contract.EffectiveStatus.EFFECTIVE
            )
        ).filter { it.end_time >= min_impact_time }.sortedBy { it.start_time }


        biz_require(affected_contracts.size <= 2) { "Too complicated to handle more than two contracts." }

        // 1. generate new revenues and update
        val matched_contract =
            contractService.resolve_task_binding_by_time_match(task_id)
        if (matched_contract == null) {
            update_revenues(revenues, listOf(build_revenue_UNRESOLVED(task_id)))
        } else {
            val billing_hours =
                contractCalcService.evaluate_billing_hours_using_contract(
                    task_id,
                    matched_contract
                )
            update_revenues(
                revenues,
                listOf(
                    build_revenue_FULL(
                        task_id,
                        matched_contract.id,
                        billing_hours
                    )
                )
            )
        }

        // 2. handle affected contracts
        val first = affected_contracts.elementAtOrNull(0)
        val second = affected_contracts.elementAtOrNull(1)
        first?.let {
            handle_contract(first, second)
        }
        second?.let {
            handle_contract(second, null)
        }
    }

    private fun update_task_revenues(
        task_id: Int,
        new: List<Revenue>
    ) {
        update_revenues(Revenue.findAll(Revenue.Query(task_id = task_id)), new)
    }

    /**
     * 2024/4/7 update:
     * 如果用户有AllowEditCutOffRevenue这个角色，那么在Complete Task的时候，
     * 如果Revenue是Locked，但是还没有出账的话，那么允许修改Client Charge信息，提交后生成一条新的Locked的Revenue。
     * 如果Task已经出账了，那么无论是否有这个角色，都不允许修改Client Charge
     */
    private fun update_revenues(
        old: List<Revenue>,
        new: List<Revenue>
    ) {
        biz_require(old.size <= 2 && new.size <= 2) {
            "Found more than two revenues when update task:${old.firstOrNull()?.task_id}"
        }
        biz_require(old.none { it.is_invoiced }) { "Not allowed to update invoiced revenue:${old.firstOrNull()?.id}." }

        val is_operator_allowed_edit_cutoff = userService.is_user_allowed_to_edit_cutoff_revenue(AuthContext.user_id)
        val should_lock_at = old.takeIf { old_revenues ->
            old_revenues.isNotEmpty() && old_revenues.all { it.is_locked }
        }?.first()?.lock_at

        // cutoff check
        if (old.size == 1 && new.size == 1) {
            val oldRevenue = old.first()
            val newRevenue = new.first()
            if (oldRevenue.is_locked) {
                val is_billing_no_changed = Revenue.compare_if_billing_un_changed(
                    oldRevenue,
                    newRevenue
                )
                biz_check(is_billing_no_changed || is_operator_allowed_edit_cutoff) {
                    "Not allowed to update cutoff revenue:${oldRevenue.id}."
                }
            }
        }

        (old - new).forEach { Revenue.delete(it.id) }
        val created = (new - old).map { Revenue.save(it) }

        if (should_lock_at != null) {
            created.forEach {
                Patch.fromMutator(it) {
                    this.lock_at = should_lock_at
                }.patch()
            }
        }
    }

    @Transactional
    fun unbind_revenue(revenue: Revenue) {
        biz_check(!revenue.is_invoiced) { "Not allowed to unbind invoiced revenue(id=${revenue.id})." }
        biz_check(!revenue.is_locked) { "Not allowed to unbind cutoff revenue(id=${revenue.id})." }

        when (revenue.create_type) {
            Revenue.CreateType.FULL, Revenue.CreateType.UNRESOLVED ->
                update_task_revenues(
                    revenue.task_id,
                    listOf(build_revenue_UNRESOLVED(revenue.task_id))
                )

            Revenue.CreateType.PART, Revenue.CreateType.OVERFLOW ->
                update_revenues(
                    listOf(revenue),
                    listOf(
                        build_revenue_OVERFLOW(
                            revenue.task_id,
                            revenue.billing_hours
                        )
                    )
                )
        }
    }

    private fun handle_contract(
        contract: Contract,
        follow_contract: Contract?
    ) {
        when (contract.payway) {
            PayWay.PAYGO, PayWay.TRIAL -> {
                return
            }

            PayWay.PROJECT_BASE -> {
                // todo
                return
            }

            else -> {
            }
        }

        // prepay
        val current_used = contractService.get_used_hours(contract)
        val current_hours_limit = contract.charge_hours.assert_exist()
        val overflowed_hours = current_used - current_hours_limit

        // prepay contract: usage reminder
        if (overflowed_hours < 0.BD) {
            val latest_billing =
                Contract.join(contract, setOf(Contract::revenues.name)).revenues!!
                    .maxByOrNull { it.revenue_time }?.billing_hours
            if (latest_billing != null) {
                val used_percentage_before =
                    (current_used - latest_billing) / current_hours_limit
                val used_percentage_after = current_used / current_hours_limit
                val range = used_percentage_before..used_percentage_after
                if (0.5.BD in range || 0.75.BD in range || 0.9.BD in range) {
                    emailService.send(
                        EmailRequestPojo.from_template_result(
                            placeholderBasedEmailTemplateService.process_built_in_by_data_id(
                                EmailContentType.NOTIFY_CONTRACT_USAGE,
                                PlaceholderBasedModelIds(
                                    client_id = contract.client_id,
                                    contract_id = contract.id
                                )
                            )
                        )
                    )
                }
            }
        }

        when {
            overflowed_hours > 0.BD -> {
                handle_over_size_prepay(contract, follow_contract, overflowed_hours)
            }

            overflowed_hours eq 0.BD -> {
                handle_reach_size_prepay(contract, follow_contract)
            }

            overflowed_hours < 0.BD -> {
                handle_lack_size_prepay(
                    contract,
                    follow_contract,
                    overflowed_hours.abs()
                )
            }
        }
    }
//endregion

    //region contract process: prepay
    private fun handle_over_size_prepay(
        contract: Contract,
        follow_contract: Contract?,
        overflowed_hours: BigDecimal
    ) {
        val revenues = Revenue.findAll(
            Revenue.Query(
                contract_id = contract.id
            )
        ).sortedWith(compareBy({ it.revenue_time }, { it.task_id })).reversed()

        val (list, boundary, value) = align_billing_hours(revenues, overflowed_hours)

        list.forEach { rev ->
            if (follow_contract == null) {
                update_task_revenues(
                    rev.task_id,
                    listOf(build_revenue_UNRESOLVED(rev.task_id))
                )
            } else {
                val billing_hours =
                    contractCalcService.evaluate_billing_hours_using_contract(
                        rev.task_id,
                        follow_contract
                    )
                update_task_revenues(
                    rev.task_id,
                    listOf(
                        build_revenue_FULL(
                            rev.task_id,
                            follow_contract.id,
                            billing_hours
                        )
                    )
                )
            }
        }

        boundary?.let {
            handle_boundary_task(
                it,
                contract,
                follow_contract,
                it.billing_hours - value!!
            )
        }

        handle_reach_size_prepay(contract, follow_contract)
    }

    private fun handle_reach_size_prepay(
        contract: Contract,
        follow_contract: Contract?
    ) {
        Revenue.findAll(
            Revenue.Query(
                contract_id = contract.id
            )
        ).maxByOrNull { it.revenue_time }?.revenue_time?.let {
            split_contract(contract, follow_contract, it)
        }
    }

    private fun handle_lack_size_prepay(
        contract: Contract,
        follow_contract: Contract?,
        lacked_hours: BigDecimal
    ) {
        val revenues = Revenue.findAll(
            Revenue.Query(
                client_id = contract.client_id,
                revenue_time_gte = contract.end_time,
                contract_id_ne = contract.id,
                contract = Contract.Query(
                    payway_ne = PayWay.PROJECT_BASE,
                ),
            )
        ).map {
            it.apply {
                billing_hours =
                    contractCalcService.evaluate_billing_hours_using_contract(
                        it.task_id,
                        contract
                    )
            }
        }.sortedWith(compareBy({ it.revenue_time }, { it.task_id }))

        // as we calculate the full hours, we need to release the remaining hours related to the origin contract.
        val released_hours = revenues.firstOrNull()?.let { rev ->
            Revenue.findAll(
                Revenue.Query(
                    contract_id = contract.id,
                    task_id = rev.task_id
                )
            ).sumBdBy { it.billing_hours }
        } ?: 0.BD

        val (list, boundary, value) = align_billing_hours(
            revenues,
            lacked_hours + released_hours
        )

        list.forEach { rev ->
            update_task_revenues(
                rev.task_id,
                listOf(
                    build_revenue_FULL(
                        rev.task_id,
                        contract.id,
                        billing_hours = rev.billing_hours
                    )
                )
            )
        }

        boundary?.let {
            handle_boundary_task(it, contract, follow_contract, value!!)
        }

        // check if reach size.
        val current_used = contractService.get_used_hours(contract)
        val current_hours_limit = contract.charge_hours.assert_exist()
        if (current_used eq current_hours_limit) {
            handle_reach_size_prepay(contract, follow_contract)
        }
    }

    private fun handle_boundary_task(
        boundary: Revenue,
        contract: Contract,
        follow_contract: Contract?,
        overflowed_hours: BigDecimal
    ) {
        val next_contract_id = follow_contract?.id
        val revenue_first =
            build_revenue_PART(
                boundary.task_id,
                contract.id,
                billing_hours = boundary.billing_hours - overflowed_hours
            )
        val revenue_second = if (next_contract_id != null) {
            build_revenue_PART(
                boundary.task_id,
                next_contract_id,
                billing_hours = overflowed_hours
            )
        } else {
            build_revenue_OVERFLOW(
                boundary.task_id,
                billing_hours = overflowed_hours
            )
        }
        update_task_revenues(boundary.task_id, listOf(revenue_first, revenue_second))
    }

    private fun align_billing_hours(
        revenues: List<Revenue>,
        totalHours: BigDecimal
    ): Triple<List<Revenue>, Revenue?, BigDecimal?> {
        val resultList = mutableListOf<Revenue>()
        for (rev in revenues) {
            val sum = rev.billing_hours + resultList.sumBdBy { it.billing_hours }
            when {
                sum eq totalHours -> {
                    resultList.add(rev)
                    return Triple(resultList, null, null)
                }

                sum > totalHours -> return Triple(resultList, rev, sum - totalHours)
                else -> resultList.add(rev)
            }
        }
        return Triple(resultList, null, null)
    }

    private fun split_contract(
        contract: Contract,
        follow_contract: Contract?,
        split_time: Instant
    ) {
        Patch.fromMap(
            contract.id, mapOf(
                Contract::end_time to split_time,
                Contract::effective_status to
                        if (split_time > Instant.now()) Contract.EffectiveStatus.EFFECTIVE
                        else Contract.EffectiveStatus.EXPIRED
            )
        ).patch()
        follow_contract?.let {
            Patch.fromMap(
                it.id, mapOf(
                    Contract::start_time to split_time,
                    Contract::effective_status to
                            if (split_time > Instant.now()) Contract.EffectiveStatus.INEFFECTIVE
                            else Contract.EffectiveStatus.EFFECTIVE
                )
            ).patch()
        }
    }
//endregion

    //region contract process: project_base
    private fun handle_project_base_contract(task: Task) {
        val assigned_contract = biz_required_not_null(
            contractService.resolve_task_if_assigned_contract(task.id)
        ) {
            "Charge cash can only be used when project is assigned to a PROJECT_BASE contract."
        }
        biz_check(assigned_contract.payway == PayWay.PROJECT_BASE) {
            "Assigned contract should be of payway: PROJECT_BASE."
        }

        val revenue =
            build_revenue_FULL(
                task.id,
                assigned_contract.id,
                billing_hours = 0.BD,
                charge_cash = task.charge_cash
            )
        update_task_revenues(task.id, listOf(revenue))

        val used_size = contractService.get_used_size(assigned_contract)
        biz_check(used_size <= assigned_contract.contract_size.assert_exist()) {
            "Charge cash will over contract size of contract: ${assigned_contract.id}."
        }
        // expired if runs out.
        if (used_size eq assigned_contract.contract_size) {
            Patch.fromMap(
                assigned_contract.id, mapOf(
                    Contract::effective_status to Contract.EffectiveStatus.EXPIRED
                )
            ).patch()
        }
    }
//endregion

//region project charge

    /**
     * 如果未lock就覆盖，已lock就生成一条ADJ （就是多一条收费） - 时间用当前时间
     * 每次都是输入总值，给他 算差值 创建一条adj  (在当前未lock的里面改， lock了就新建一条)
     */
    private fun update_project_charge_revenue(
        request: UpateProjectChargeRequest,
    ): List<Revenue> {
        // 当前已有的revenues
        val revenueQuery = Revenue.Query(project_id = request.project_id)
        val exist_revenues = Revenue.findAll(revenueQuery)

        val exist_currencies = exist_revenues.mapNotNull { it.cash_currency }.toSet()
        val (exist_fixed_revenues, exist_unfixed_revenues) = exist_revenues.partition {
            it.is_locked || it.is_invoiced
        }
        biz_check(exist_unfixed_revenues.size <= 1) { "Found more than one not locked/invoiced revenues" }
        val unfixed_revenue = exist_unfixed_revenues.firstOrNull()
        val already_fixed_cash = exist_fixed_revenues.sumBdBy { it.cash!! }

        // check
        if (exist_fixed_revenues.isNotEmpty()) {
            biz_check(exist_currencies.size == 1) {
                "Multi types of currency is not supported"
            }
            biz_check(
                request.total_charge != null
                        && exist_currencies.contains(request.total_charge_currency)
            ) {
                "Already charged(invoiced/locked) $already_fixed_cash ${exist_currencies.first()}"
            }
        }

        // (1) 若前端修改为Null
        if (request.total_charge == null) {
            // 由上面的check,此时exist_fixed_revenues肯定为empty
            unfixed_revenue?.let { Revenue.delete(it.id) }
        } else {
            val delta = request.total_charge - already_fixed_cash

            // (2) 数额等于已出账/lock的，无需在fixed以外的纪录
            if (delta == BigDecimal.ZERO) {
                unfixed_revenue?.let { Revenue.delete(it.id) }
            } else {
                val billing_notes =
                    "${delta.safe_change_precision_to_0digits()} ${request.total_charge_currency} charge total; Survey"
                val project = Project.get(
                    request.project_id, Includes.setOf(
                        Project::project_client_contacts
                    )
                )
                val client_contact_id = project.project_client_contacts.orEmpty().firstOrNull {
                    it.client_contact_type == ProjectClientContact.Type.PRIMARY
                }?.client_contact_id
                // (3) 数额> / <已出账/lock的，更新unfixed的那个revenue: update/create
                val create_type = if (exist_fixed_revenues.isNotEmpty()) {
                    Revenue.CreateType.ADJ
                } else {
                    Revenue.CreateType.FULL
                }
                val latest_revenue = if (unfixed_revenue != null) {
                    unfixed_revenue.apply {
                        this.revenue_time = Instant.now()
                        this.cash = delta
                        this.cash_currency = request.total_charge_currency
                        this.create_type = create_type
                        this.client_contact_id = client_contact_id
                        this.billing_notes = billing_notes
                    }
                    Revenue.update(unfixed_revenue)
                } else {
                    Project.join(
                        project, Includes.setOf(
                            Project::client dot Client::effective_contract
                        )
                    )

                    val revenue = Revenue {
                        this.client_id = project.client_id.assert_valid_id()
                        this.project_id = project.id
                        this.task_id = 0
                        this.revenue_time = Instant.now()
                        this.contract_id = project.client!!.effective_contract?.id
                        this.create_type = create_type
                        this.status = Revenue.Status.CREATED
                        this.cash = delta
                        this.cash_currency = request.total_charge_currency
                        this.client_contact_id = client_contact_id
                        this.billing_notes = billing_notes
                    }
                    // warning: prevent from going through RevenueRepo.save() with bussiness logic
                    Revenue.save(revenue, CrudOptions.SaveOption.default())
                }
            }
        }
        return Revenue.findAll(revenueQuery)
    }

    /**
     * charge client by project, not by individual task
     * common method from Survey/Patients projects
     */
    @Transactional
    fun update_project_charge(
        request: UpateProjectChargeRequest,
    ): Project {
        val project = Project.get(request.project_id)
        val revenues = update_project_charge_revenue(request)
        val res = if (project.sub_type == Project.SubType.Survey) {
            Patch.fromMap(
                id = request.project_id,
                map = mapOf(
                    Project::survey_total_charge to request.total_charge,
                    Project::survey_total_charge_currency to request.total_charge_currency
                )
            ).patchThenGet(
                Includes.setOf(Project::survey_revenue)
            )
        } else {
            Patch.fromMap(
                id = request.project_id,
                map = mapOf(
                    Project::total_charge to request.total_charge,
                    Project::total_charge_currency to request.total_charge_currency
                )
            ).patchThenGet()
        }
        res.revenues = revenues
        return res
    }
//endregion

    override fun handle_move_task(
        tasks: List<Task>,
        to_project_id: Int,
        operator: Int?
    ) {
        TaskTransferUtil.transfer1toN(
            tasks,
            to_project_id,
            operator,
            Task::revenues,
            repo,
        )
    }

    fun build_client_usage_table(
        today_date: LocalDate,
        client_id: Int,
    ): ClientUsageTableResult {
        val current_year_first_day = today_date.with(TemporalAdjusters.firstDayOfYear())
        val past_year_today = today_date.withYear(today_date.year - 1)
        val past_year_first_day = past_year_today.with(TemporalAdjusters.firstDayOfYear())
        val past_year_last_day = past_year_first_day.with(TemporalAdjusters.lastDayOfYear())

        val recent_two_years_revenues = Revenue.findAll(
            Revenue.Query(
                client_id = client_id,
                revenue_time_gte = past_year_first_day.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()
            ),
            include = Includes.setOf(Revenue::project)
        ).map { FinancialTransaction.from_revenue(it) }
        val recent_two_years_receivable_transactions = ReceivableTransaction.findAll(
            ReceivableTransaction.Query(
                project = Project.Query(client_id = client_id),
                transaction_date_gte = past_year_first_day.toString()
            ),
            include = Includes.setOf(ReceivableTransaction::project)
        ).map { FinancialTransaction.from_receivable_transaction(it) }

        val recent_two_years_transaction = recent_two_years_revenues + recent_two_years_receivable_transactions

        val past_year_transaction = recent_two_years_transaction.filter {
            it.transaction_instant.isBefore(
                current_year_first_day.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()
            )
        }
        val current_year_transaction = recent_two_years_transaction - past_year_transaction
        val past_year_ytd_transaction = past_year_transaction.filter {
            it.transaction_instant.isBefore(past_year_today.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant())
        }

        // past year statistical result
        val past_year_group = past_year_transaction.group_by_period(
            past_year_first_day,
            past_year_last_day,
            DateRangeUtil.Unit.MONTH
        )
        var past_year_summary_result = past_year_group
            .mapKeys { (k, _) -> DateTimeFormatter.ISO_DATE.format(k) }
            .mapValues { (_, v) -> v.group_and_summarize_by_project_type() }
        val past_year_ytd_summary_result = past_year_ytd_transaction.group_and_summarize_by_project_type()
        past_year_summary_result = past_year_summary_result.plus("YTD" to past_year_ytd_summary_result)

        // current year statistical result
        val current_year_group = current_year_transaction.group_by_period(
            current_year_first_day,
            today_date,
            DateRangeUtil.Unit.MONTH
        )
        var current_year_summary_result = current_year_group
            .mapKeys { (k, _) -> DateTimeFormatter.ISO_DATE.format(k) }
            .mapValues { (_, v) -> v.group_and_summarize_by_project_type() }
        val current_year_ytd_summary_result = current_year_transaction.group_and_summarize_by_project_type()
        current_year_summary_result = current_year_summary_result.plus("YTD" to current_year_ytd_summary_result)

        return ClientUsageTableResult(
            current_year = current_year_summary_result,
            past_year = past_year_summary_result
        )
    }

}

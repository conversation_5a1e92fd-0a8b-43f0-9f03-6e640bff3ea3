package com.cpvsn.rm.core.config.aws_s3

import org.springframework.boot.context.properties.ConfigurationProperties

@ConfigurationProperties("s3.sdk")
class S3SdkProperties {
    companion object {
        const val WARBURG_PINCUS = "WarburgPincus"
    }

    var clients: List<S3ClientConfig> = emptyList()
}
data class S3ClientConfig(
    var clientName: String = "",
    var accessKeyId: String = "",
    var secretAccessKey: String = "",
    var bucket: String = "",
    var region: String = ""
)
package com.cpvsn.rm.core.features.metrics.pojo

import com.cpvsn.rm.core.extensions.percent
import com.cpvsn.rm.core.extensions.safe_divide
import com.cpvsn.rm.core.extensions.sumBdBy
import com.cpvsn.rm.core.features.currency_conversion.CurrencyRateData
import com.cpvsn.rm.core.features.currency_conversion.to_iso_currency
import com.cpvsn.rm.core.features.metrics.ClientHourAdjuster
import com.cpvsn.rm.core.features.project.Project
import com.cpvsn.rm.core.features.task.CallTrackNote
import com.cpvsn.rm.core.features.task.Task
import com.cpvsn.rm.core.features.task.constant.TaskSubType
import java.math.BigDecimal
import java.math.RoundingMode
import java.text.NumberFormat
import java.time.Instant


/**
 * One row in the Call Tracker table. Represents one [Task] that is either a scheduled or completed call or survey complete.
 * @param effective_billable_hours Sometimes (for PayGo clients that are charged cash instead of credits), users enter a specified client charge for a task, instead of relying on the ContractCalcService to calculate client charge. In this case, the Revenue `billing_hours` is not reflective of the amount of service the client received. In this case, [effective_billable_hours] extrapolates billable hours using the client charge for this task and the client unit charge.
 */
class CallTrack(
    var task_id: Int = 0,
    var task_tsid: String? = null,
    var client_id: Int? = null,
    var client_name: String? = "",
    var project_id: Int? = null,
    var project_name: String? = "",
    var advisor_id: Int? = null,
    var advisor_name: String? = "",
    var rate: String? = "",
    var compliance: String? = "",
    var status: String? = "",   // general status
    var client_compliance_status: String? = null,
    var consultation_date: String? = "",
    var lead: String? = "",
    var support: String? = "",
    var client_hours: String? = "",
    var billable_hours: String? = "",
    var billing_notes: String? = "",
    var start_time: Instant? = null,
    var is_custom_sourced: Boolean? = null,
    var project_sub_type: Project.SubType? = null,
    var task_sub_type: TaskSubType? = null,
    var notes: List<CallTrackNote>? = null,
    var duration: BigDecimal? = null,
    var angle_id: Int? = null,
    var margin: String = "",
    var effective_billable_hours: String? = null,
    var total_charge: String? = null,
    var outsource_status: String? = null,
    var outsource_from: String? = null,
    var outsource_to: String? = null,
    var survey_receivable_amount: String? = null
) {
    companion object {
        fun fromTask(task: Task, adjusters: List<ClientHourAdjuster>, currencies: List<CurrencyRateData>): CallTrack {
            val currency_map = currencies.associateBy { it.from_currency }
            return CallTrack().apply {
                task_id = task.id
                task_tsid = task.tsid
                client_id = task.client_id
                client_name = task.client?.name
                project_id = task.project_id
                project_name = task.project?.name
                advisor_id = task.advisor_id
                advisor_name = "${task.advisor?.firstname ?: ""} ${task.advisor?.lastname ?: ""}"
                rate = "${task.advisor?.rate ?: ""} ${task.advisor?.rate_currency ?: ""}"
                compliance = task.client_compliance_status.name
                status = task.call_tracker_status?.name ?: task.general_status.name
                client_compliance_status = task.client_compliance_status.name
                consultation_date = task.start_time?.toString()
                lead = task.lead?.name
                support = task.support?.name
                client_hours =
                    calculate_client_hours(task, adjusters)?.toPlainString()
                billable_hours =
                    (task.marked_billable_hour ?: task.revenues?.sumBdBy { it.billing_hours })?.toPlainString()
                val total_revenues = task.revenues?.sumBdBy { it.cash ?: BigDecimal.ZERO }
                val formatter: NumberFormat = NumberFormat.getCurrencyInstance(java.util.Locale.US)
                total_charge = total_revenues?.let{formatter.format(it)}
                effective_billable_hours = total_revenues.safe_divide(task.client?.effective_contract?.unit_price_us, scale = 3)?.toPlainString()
                billing_notes = task.billing_notes
                start_time = task.start_time
                is_custom_sourced = task.is_custom_sourced
                project_sub_type = task.project_sub_type
                task_sub_type = task.sub_type_enum
                duration = task.client_duration
                notes =  billing_notes_to_call_track_note(task)
                angle_id = task.angle_id
                margin = calculate_margin(task, currency_map)
                outsource_status = task.task_outsource_status.name
                outsource_from = task.task_outsource_info?.outsource_advisor_from?.name
                outsource_to = task.task_outsource_info?.outsource_advisor_to?.name
                survey_receivable_amount = task.survey_receivable?.let {
                    it.amount.times(
                        currency_map[it.currency.to_iso_currency().name]?.rate ?: BigDecimal.ONE
                    )
                }?.let { formatter.format(it) }
            }
        }

        /**
         * `client_hours` is a performance metric used by the research team. It is typically related to `duration` and equal to [Task] `client_hours`,
         * but is adjusted in the call tracker for more difficult or more important calls, like custom sourced calls or calls for important clients.
         * @param task [Task] whose client hours will be evaluated
         * @param adjusters list of [ClientHourAdjuster] that will be multiplied against [task]'s client hours if `call_tracker_client_hours` is null. All [ClientHourAdjuster] in the DB should be included. We don't retrieve [ClientHourAdjuster] here because it would costly to do for every task that is requested.
         * @return `task.call_tracker_client_hours` if not null, otherwise `task.marked_client_hour` or `task.client_hours` multiplied against each adjuster. Always rounded to 3 decimal places.
         */
        fun calculate_client_hours(task: Task, adjusters: List<ClientHourAdjuster>): BigDecimal? {
            task.call_tracker_client_hours?.let{ return it.setScale(3, RoundingMode.HALF_UP) }
            var client_hours = task.marked_client_hour ?: task.client_hours ?: return null
            adjusters.forEach { adjuster ->
                client_hours = adjuster.type_enum.adjust(client_hours, adjuster, task)
            }
            return client_hours.setScale(3, RoundingMode.HALF_UP)
        }

        /**
         * @param task Takes this task's `billing_notes` value and
         * @return turns it into a [CallTrackNote] object with type `BILLING_NOTES`.
         */
        fun billing_notes_to_call_track_note(task: Task): List<CallTrackNote> {
            val billing_note = if (task.billing_notes.isNotBlank()) {
                CallTrackNote().apply {
                    task_id = task.id
                    type = CallTrackNote.Type.BILLING_NOTES
                    note = task.billing_notes
                }
            } else null
            return if (billing_note != null)
                task.call_track_notes?.plus(billing_note) ?: listOf(billing_note)
            else task.call_track_notes ?: emptyList()
        }

        /**
         * Calculates the margin of a task using its `advisor_payments`, `payment_topic.payments`, and `revenues`.
         * Formula is revenue sum / ( local payment sum + payment sum )
         * @param task [Task] that should include its `advisor_payments`, `payment_topic.payments`, and `revenues`.
         * @return [String] representation of margin percent using [percent]
         */
        fun calculate_margin(task: Task, currency_map: Map<String?, CurrencyRateData>): String {
            val local_payments = task.advisor_payments?.sumBdBy {
                it.amount.times(
                    currency_map[it.currency.to_iso_currency().name]?.rate ?: BigDecimal.ONE
                )
            } ?: BigDecimal.ZERO
            val payments = task.payment_topic?.payments?.sumBdBy {
                it.amount.times(
                    currency_map[it.currency_enum?.to_iso_currency()?.name]?.rate ?: BigDecimal.ONE
                )
            } ?: BigDecimal.ZERO
            val revenue = task.revenues?.sumBdBy { it.cash ?: BigDecimal.ZERO } ?: BigDecimal.ZERO
            val margin_amount = revenue.minus(local_payments).minus(payments)
            return margin_amount.percent(revenue)
        }
    }

}

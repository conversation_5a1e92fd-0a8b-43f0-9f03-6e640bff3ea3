package com.cpvsn.rm.core.features.email_template.constant

import com.cpvsn.core.model.BusinessException
import com.cpvsn.core.util.UrlEncoder
import com.cpvsn.core.util.extension.add_prefix
import com.cpvsn.core.util.extension.toLocalDateTime
import com.cpvsn.crud.model.Includes
import com.cpvsn.crud.model.dot
import com.cpvsn.rm.core.annotation.DocConstant
import com.cpvsn.rm.core.config.PortalProperties
import com.cpvsn.rm.core.config.Webpage
import com.cpvsn.rm.core.extensions.safe_change_precision_to_0digits
import com.cpvsn.rm.core.extensions.safe_change_precision_to_2digits
import com.cpvsn.rm.core.extensions.toDefaultInstant
import com.cpvsn.rm.core.extensions.to_title_case
import com.cpvsn.rm.core.features.advisor.Advisor
import com.cpvsn.rm.core.features.advisor.job.AdvisorJob
import com.cpvsn.rm.core.features.advisor.payment_form.AdvisorPaymentForm
import com.cpvsn.rm.core.features.client.Client
import com.cpvsn.rm.core.features.client.legal_preference.ClientCompliancePreference
import com.cpvsn.rm.core.features.client.legal_preference.chaperone.TaskClientChaperone
import com.cpvsn.rm.core.features.compliance.tc.Tc
import com.cpvsn.rm.core.features.contract.PayWay
import com.cpvsn.rm.core.features.contract.entity.Contract
import com.cpvsn.rm.core.features.core.app_config.AppConfigEntry
import com.cpvsn.rm.core.features.core.app_config.AppConfigKey
import com.cpvsn.rm.core.features.email_template.EmailTemplateEngine
import com.cpvsn.rm.core.features.email_template.EmailTemplateUtil
import com.cpvsn.rm.core.features.email_template.html.*
import com.cpvsn.rm.core.features.email_template.models.PlaceholderBasedModel
import com.cpvsn.rm.core.features.finance.advisor_payment.AdvisorPayment
import com.cpvsn.rm.core.features.finance.advisor_payment.local.LocalAdvisorPayment
import com.cpvsn.rm.core.features.finance.payment.Payment
import com.cpvsn.rm.core.features.finance.payment.PaymentForm
import com.cpvsn.rm.core.features.finance.payment.PaymentItem
import com.cpvsn.rm.core.features.finance.payment.PaymentTopic
import com.cpvsn.rm.core.features.inquiry.model.InquiryInstanceTextFormUtil
import com.cpvsn.rm.core.features.misc.loopup.LoopupNumber
import com.cpvsn.rm.core.features.misc.schedule.Schedule
import com.cpvsn.rm.core.features.portal.PortalType
import com.cpvsn.rm.core.features.portal.common.payload.ClientUserRegisterPayload
import com.cpvsn.rm.core.features.project.Project
import com.cpvsn.rm.core.features.project.ProjectClientContact
import com.cpvsn.rm.core.features.project.ProjectMember
import com.cpvsn.rm.core.features.project.ProjectWorkflow
import com.cpvsn.rm.core.features.task.Task
import com.cpvsn.rm.core.features.task.approval.cap.TaskCapLegalReview
import com.cpvsn.rm.core.features.task.approval.client.TaskClientLegalReview
import com.cpvsn.rm.core.features.task.feedback.TaskContactFeedback
import com.cpvsn.rm.core.features.user.User
import com.cpvsn.rm.core.util.Formatters
import com.cpvsn.rm.core.util.UrlUtil
import com.cpvsn.rm.core.util.biz_error
import com.cpvsn.rm.core.util.format_with_zone_id
import org.springframework.beans.factory.getBean
import java.text.ParseException
import java.text.SimpleDateFormat
import java.time.*
import java.time.format.DateTimeFormatter
import java.time.format.TextStyle
import java.time.temporal.ChronoUnit
import java.util.*

/**
 * Note that, change the name of placeholder
 * may break existing email templates
 */
@DocConstant
enum class EmailTemplatePlaceholder(
    override val description: String = "",
    override val category: EmailTemplatePlaceholderCategory,
    /**
     * hide to user
     */
    override val hide_by_default: Boolean = false,
) : EmailTextPlaceHolder {

    //region common
    DATE(
        description = "date",
        category = EmailTemplatePlaceholderCategory.COMMON,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return Formatters.DATETIME.yyyyMMdd
                .format_with_zone_id(Instant.now(), data.resolve_timezone())
        }
    },
    DATETIME(
        description = "datetime",
        category = EmailTemplatePlaceholderCategory.COMMON,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return Formatters.DATETIME.`yyyy-MM-dd HHmmss`
                .format_with_zone_id(Instant.now(), data.resolve_timezone())
        }
    },
    CURRENT_TIMEZONE(
        description = "current timezone",
        category = EmailTemplatePlaceholderCategory.COMMON,
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return data.resolve_timezone().toString()
        }
    },
    //endregion

    //region advisor
    ADVISOR_ID(
        description = "advisor id",
        category = EmailTemplatePlaceholderCategory.ADVISOR,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return data.advisor?.id?.toString() ?: ""
        }
    },
    ADVISOR_NAME(
        description = "advisor name",
        category = EmailTemplatePlaceholderCategory.ADVISOR,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            if (self in data.no_replace_placeholders) return self
            return data.advisor?.full_name ?: ""
        }
    },

    ADVISOR_FULL_NAME(
        description = "advisor full name",
        category = EmailTemplatePlaceholderCategory.ADVISOR
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            if (self in data.no_replace_placeholders) return self
            return data.advisor?.full_name ?: ""
        }
    },

    ADVISOR_FIRST_NAME(
        description = "advisor first name",
        category = EmailTemplatePlaceholderCategory.ADVISOR
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            if (self in data.no_replace_placeholders) return self
            return data.advisor?.firstname ?: ""
        }
    },

    ADVISOR_FIRST_NAME_OR_DR_LAST_NAME(
        description = "advisor first name or dr. last name if title is dr.",
        category = EmailTemplatePlaceholderCategory.ADVISOR
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            if (self in data.no_replace_placeholders) return self
            return if (data.advisor?.lastname?.isNotBlank() == true && data.advisor?.name_prefix == "Dr.") "Dr. ${data.advisor?.lastname}"
            else data.advisor?.firstname ?: ""
        }
    },

    ADVISOR_LAST_NAME(
        description = "advisor last name",
        category = EmailTemplatePlaceholderCategory.ADVISOR
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            if (self in data.no_replace_placeholders) return self
            return data.advisor?.lastname ?: ""
        }
    },

    ADVISOR_PROFILE_PAGE_URL(
        description = "advisor profile page url",
        category = EmailTemplatePlaceholderCategory.ADVISOR,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            val advisor_id = data.advisor?.id ?: return ""
            return Webpage.AdvisorProfile(advisor_id).url
        }
    },

    ADVISOR_HOURLY_RATE(
        description = "advisor hourly rate",
        category = EmailTemplatePlaceholderCategory.ADVISOR,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return data.advisor?.rate?.toString() ?: ""
        }
    },

    ADVISOR_HOURLY_RATE_WITH_CURRENCY(
        description = "advisor hourly rate with currency",
        category = EmailTemplatePlaceholderCategory.ADVISOR,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            val rate = data.advisor?.rate?.toString() ?: return ""
            val currency = data.advisor?.rate_currency?.toString() ?: return ""

            return "$rate $currency"
        }
    },

    ADVISOR_COMPANY(
        description = "advisor company",
        category = EmailTemplatePlaceholderCategory.ADVISOR
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            val advisor = Advisor.join(
                data.advisor ?: return "",
                Includes.setOf(Advisor::current_job dot AdvisorJob::company)
            )
            return advisor.current_job?.company?.name.orEmpty()
        }
    },

    ADVISOR_PUBLIC_COMPANY_LESS_MONTH(
        description = "The number of months that advisor has left public company for",
        category = EmailTemplatePlaceholderCategory.ADVISOR,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            val client = Client.join(
                data.client ?: return "Public Company No Limit.",
                Includes.setOf(Client::compliance_preference dot ClientCompliancePreference::rule_json_str)
            )
            return client.compliance_preference?.rule_object?.client_conditional_approve_type_former_employee_window_detail?.toString()
                ?.let {
                    "Public Company < $it months: No"
                } ?: "Public Company No Limit."
        }
    },

    ADVISOR_POSITION(
        description = "advisor position",
        category = EmailTemplatePlaceholderCategory.ADVISOR
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            val advisor = Advisor.join(
                data.advisor ?: return "",
                Includes.setOf(Advisor::current_job dot AdvisorJob::company)
            )
            return advisor.current_job?.position.orEmpty()
        }
    },

    ADVISOR_CURRENT_JOB_DESCRIPTION(
        description = "advisor current job description",
        category = EmailTemplatePlaceholderCategory.ADVISOR,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            val advisor = Advisor.join(
                data.advisor ?: return "",
                Includes.setOf(Advisor::current_job dot AdvisorJob::company)
            )
            val job = advisor.current_job ?: return ""
            return "${job.company?.name.orEmpty()} | ${job.position} | ${job.start_date} —— ${job.end_date}"
        }
    },

    ADVISOR_BACKGROUND(
        description = "advisor background",
        category = EmailTemplatePlaceholderCategory.ADVISOR,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            val advisor = data.advisor ?: return ""
            val bg = advisor.background
            return if (bg.isNullOrBlank()) {
                Advisor.join(advisor, setOf(Advisor::auto_background.name))
                    .auto_background.orEmpty()
            } else {
                bg
            }
        }
    },

    ADVISOR_PROJECT_LINK_LIST_HTML(
        description = "advisor project list",
        category = EmailTemplatePlaceholderCategory.ADVISOR,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            val advisor = data.advisor ?: return ""
            if (advisor.projects == null) {
                Advisor.join(
                    advisor, Includes.setOf(
                        Advisor::projects dot Project::db_leads_page_url_jit
                    )
                )
            }
            return advisor.projects.orEmpty().map {
                ATag(
                    href = it.db_info_page_url_jit.orEmpty(), children = listOf(
                        PlainTextTag(text = it.name.orEmpty())
                    )
                )
            }.joinToString(separator = "<br>") { it.toHtml() }
        }
    },

    ADVISOR_EMPLOYMENT_HISTORY(
        description = "advisor employee history",
        category = EmailTemplatePlaceholderCategory.ADVISOR,
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            val advisor = data.advisor ?: return ""
            if (advisor.jobs == null) {
                Advisor.join(advisor, Includes.setOf(Advisor::jobs dot AdvisorJob::company))
            }
            val dateFormat = SimpleDateFormat("yyyy-MM-dd")
            val sorted_jobs = advisor.jobs?.sortedWith(
                compareBy(
                    { !it.is_current },
                    {
                        try {
                            dateFormat.parse(it.start_date)
                        } catch (e: ParseException) {
                            Date(0)
                        }
                    }
                )
            ).orEmpty()
            return sorted_jobs.joinToString("<br>") {
                "${it.company?.name.orEmpty()} | ${it.position} | ${it.start_date} ~ ${
                    if (it.is_current) {
                        "current"
                    } else it.end_date
                }"
            }
        }
    },

    ADVISOR_SCREEN_RESPONSE(
        description = "advisor screen questions response",
        category = EmailTemplatePlaceholderCategory.ADVISOR,
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            class ScreeningQA(
                val question: String = "",
                val answer: String = "",
            )

            val task = data.task ?: return ""
            if (task.sq_instances == null) {
                Task.join(
                    task,
                    Includes.setOf(
                        Task::sq_instances,
                    )
                )
            }
            val qa_list = task.sq_instances?.let { sq_list ->
                sq_list.maxByOrNull { it.create_at ?: Instant.MIN }?.qa_list_snapshot.orEmpty().mapNotNull { question ->
                    question.answer?.let { answer ->
                        ScreeningQA(
                            question = question.title.replace("<div>|</div>".toRegex(), ""),
                            answer = InquiryInstanceTextFormUtil.construct_email_answer_text(question, answer)
                        )
                    }
                }.filter { it.answer.isNotBlank() }
            }

            return qa_list?.mapIndexed { index, screen_qa ->
                "Q${index + 1}.${screen_qa.question}<br>${screen_qa.answer}"
            }?.joinToString("<br>").orEmpty()

        }
    },


    ADVISOR_AVAILABLE_TIME(
        description = "advisor available time",
        category = EmailTemplatePlaceholderCategory.ADVISOR,
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            val advisor = data.advisor ?: return ""
            val current_time_zone = data.resolve_timezone()
            val day_start_time = LocalDate.now().atStartOfDay(current_time_zone).toInstant()
            val schedule_list = Schedule.findAll(
                query = Schedule.Query(
                    advisor_id = advisor.id,
                    start_time_gte = day_start_time
                )
            )
            val date_formatter = DateTimeFormatter.ofPattern("MM/dd")
            val time_formatter = DateTimeFormatter.ofPattern("h:mma", Locale.ENGLISH)
            return schedule_list.sortedBy {
                it.start_time
            }.joinToString("<br>") { schedule ->
                val name_of_week = schedule.start_time?.atZone(current_time_zone)?.dayOfWeek?.getDisplayName(
                    TextStyle.FULL,
                    Locale.ENGLISH
                ).orEmpty()
                val date = schedule.start_time?.atZone(current_time_zone)?.format(date_formatter).orEmpty()
                val start_time = schedule.start_time?.atZone(current_time_zone)?.format(time_formatter).orEmpty()
                val end_time = schedule.end_time?.atZone(current_time_zone)?.format(time_formatter).orEmpty()
                "${name_of_week} ${date}: ${start_time}-${end_time}"
            }
        }
    },

    /**
     * Sent to recipients of 1099s (the U.S. tax form for non-employee income). Helps the recipient understand
     * where the money they made the previous year came from so they are less likely to dispute the amount
     */
    ADVISOR_LAST_YEAR_CONSULTATIONS(
        description = "information about last year's consultations to help with 1099 outreach",
        category = EmailTemplatePlaceholderCategory.ADVISOR,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            if (data.advisor == null) return ""
            val last_year = LocalDateTime.now().year - 1
            val complete_time_gte = LocalDateTime.of(last_year, Month.JANUARY, 1, 0, 0).toDefaultInstant()
            val complete_time_lte = LocalDateTime.of(last_year, Month.DECEMBER, 31, 23, 59).toDefaultInstant()
            // collect payment info for advisor in both PaymentTopic and LocalAdvisorPayment from 2024
            val payment_topics = PaymentTopic.findAll(
                PaymentTopic.Query(
                    payments = Payment.Query(
                        items = PaymentItem.Query(
                            status_in = setOf(PaymentItem.Status.PAID),
                            paid_at_gte = complete_time_gte,
                            paid_at_lte = complete_time_lte
                        ),
                        advisor = Advisor.Query(
                            id = data.advisor!!.id
                        ),
                    ),
                ),
                include = Includes.setOf(
                    PaymentTopic::project,
                    PaymentTopic::task dot Task::support,
                    PaymentTopic::payments dot Payment::items
                )
            )
            val local_payments = LocalAdvisorPayment.findAll(
                LocalAdvisorPayment.Query(
                    status = AdvisorPayment.Status.PAID,
                    collaboration_type_in = setOf(
                        LocalAdvisorPayment.CollaborationType.CN_TASK_US_ADVISOR,
                        LocalAdvisorPayment.CollaborationType.US_TASK_US_ADVISOR
                    ),
                    advisor_id = data.advisor!!.id,
                    paid_at_lte = complete_time_lte,
                    paid_at_gte = complete_time_gte
                ),
                include = Includes.setOf(LocalAdvisorPayment::task dot Task::support, LocalAdvisorPayment::project)
            )
            // compile the relevant information into a uniform format (a list of String:String maps) from the different payment item types
            val payment_topic_info = payment_topics.flatMap { payment_topic ->
                val payment_items = payment_topic.payments?.flatMap { it.items ?: emptyList() }?.filter {
                    it.paid_at != null && it.paid_at!! > complete_time_gte && it.paid_at!! < complete_time_lte
                }

                /**
                 * The research team uses freeform project names that are appropriate for advisor emails.
                 * The survey team's project names should not be visible to advisors. In this case, we call the project "Survey"
                 */
                val project_name = if (payment_topic.project?.sub_type == Project.SubType.Survey) "Survey"
                else payment_topic.project?.name ?: ""
                val support = payment_topic.task?.support?.name ?: ""

                payment_items?.map { payment_item ->
                    val payment_amount = payment_item.amount.safe_change_precision_to_2digits()
                    val payment_currency = payment_item.currency_name
                    val date = payment_item.paid_at?.toLocalDateTime()?.toLocalDate().toString() ?: ""
                    listOf(
                        Pair("Project", project_name),
                        Pair("Date", date),
                        Pair("Capvision Contact", support),
                        Pair("Payment Amount", "$payment_amount $payment_currency")
                    ).toMap()
                } ?: emptyList()
            }

            val local_payments_info =
                local_payments.filter { it.paid_at != null && it.paid_at!! > complete_time_gte && it.paid_at!! < complete_time_lte }
                    .map { local_payment ->
                        val project_name = if (local_payment.project?.sub_type == Project.SubType.Survey) "Survey"
                        else local_payment.project?.name
                            ?: local_payment.task_info_snapshot?.project_name
                            ?: ""
                        val support = local_payment.task?.support?.name
                            ?: local_payment.task_info_snapshot?.task_supporter_name
                            ?: ""
                        val payment_amount = local_payment.amount.safe_change_precision_to_2digits()
                        val date = local_payment.paid_at?.toLocalDateTime()?.toLocalDate().toString() ?: ""
                        val payment_currency = local_payment.currency
                        listOf(
                            Pair("Project", project_name),
                            Pair("Date", date),
                            Pair("Capvision Contact", support),
                            Pair("Payment Amount", "$payment_amount $payment_currency")
                        ).toMap()
                    }

            val all_payment_info = payment_topic_info.plus(local_payments_info).sortedBy { it["Date"] }
            // create an HTML table to inject into the email
            val headers = listOf(
                TrTag(
                    children = listOf(
                        TdTag(width = 120, text = "Project", style = "style = \"font-weight:bold\""),
                        TdTag(width = 120, text = "Date", style = "style = \"font-weight:bold\""),
                        TdTag(width = 120, text = "Capvision Contact", style = "style = \"font-weight:bold\""),
                        TdTag(width = 120, text = "Payment Amount", style = "style = \"font-weight:bold\""),
                    )
                )
            )

            return TableTag(
                tbodyTag = TbodyTag(children = headers.plus(all_payment_info.map { item ->
                    TrTag(
                        children = listOf(
                            TdTag(width = 120, text = item["Project"] ?: ""),
                            TdTag(width = 120, text = item["Date"] ?: ""),
                            TdTag(width = 130, text = item["Capvision Contact"] ?: ""),
                            TdTag(width = 120, text = item["Payment Amount"] ?: "", style = "align=right"),
                        )
                    )
                })), style = "border=\"1\" cellspacing=\"0\" cellpadding=\"3\" style=\"border-collapse: collapse;\""
            ).toHtml()
        }
    },

    //endregion

    //region user
    RECRUITER_SIGNATURE(
        description = "recruiter signature",
        category = EmailTemplatePlaceholderCategory.USER
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            val signature = data.user?.signature
                ?: return data.user?.name.orEmpty()
            // forbid recursive call
            if (EmailTemplateEngine.TextEngine.contains_placeholder(
                    signature,
                    this
                )
            ) {
                throw BusinessException("invalid placeholder:${this.name} detected")
            }
            return EmailTemplateEngine.TextEngine.process(signature, data)
        }
    },

    RECRUITER_NAME(
        description = "recruiter name",
        category = EmailTemplatePlaceholderCategory.USER
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return data.user?.name.orEmpty()
        }
    },

    RECRUITER_GIVEN_NAME(
        description = "recruiter given name",
        category = EmailTemplatePlaceholderCategory.USER
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return data.user?.given_name.orEmpty()
        }
    },

    RECRUITER_TITLE(
        description = "recruiter title",
        category = EmailTemplatePlaceholderCategory.USER
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            val user = data.user ?: return ""
            User.join(user, Includes.setOf(User::position))
            return user.position?.name ?: ""
        }
    },

    RECRUITER_PHONE(
        description = "recruiter phone",
        category = EmailTemplatePlaceholderCategory.USER
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return data.user?.phone_number ?: ""
        }
    },

    RECRUITER_EMAIL(
        description = "recruiter email",
        category = EmailTemplatePlaceholderCategory.USER
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return data.user?.email ?: ""
        }
    },
    //endregion

    //region project
    PROJECT_ID(
        description = "project id",
        category = EmailTemplatePlaceholderCategory.PROJECT,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return data.project?.id?.toString() ?: ""
        }
    },

    PROJECT_NAME(
        description = "project name",
        category = EmailTemplatePlaceholderCategory.PROJECT,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            val project_name = data.project?.name ?: ""
            return if (data.rendering_target == PlaceholderBasedModel.RenderingTarget.SUBJECT) project_name.to_title_case()
            else project_name
        }
    },

    PROJECT_SUB_TYPE(
        description = "project sub type",
        category = EmailTemplatePlaceholderCategory.PROJECT,
        hide_by_default = false,
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return data.project?.sub_type?.name.orEmpty()
        }
    },

    PROJECT_MANAGER_NAME(
        description = "project manager name",
        category = EmailTemplatePlaceholderCategory.PROJECT,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            val project = data.project ?: return ""
            Project.join(
                project, setOf(
                    Project::manager.name,
                    "${Project::manager.name}.${ProjectMember::user.name}"
                )
            )
            return project.manager?.user?.name.orEmpty()
        }
    },

    PROJECT_REQUEST(
        description = "project request",
        category = EmailTemplatePlaceholderCategory.PROJECT,
        hide_by_default = true,
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return data.project?.request_content.orEmpty()
        }
    },

    PROJECT_CREATE_DATE(
        description = "project create date",
        category = EmailTemplatePlaceholderCategory.PROJECT,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd")
            return data.project?.create_at?.let {
                formatter.format_with_zone_id(it, data.resolve_timezone())
            }.orEmpty()
        }

    },

    PROJECT_START_DATE(
        description = "project start date",
        category = EmailTemplatePlaceholderCategory.PROJECT,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return data.project?.start_date?.toString() ?: ""
        }
    },

    PROJECT_CASE_CODE(
        description = "project case code",
        category = EmailTemplatePlaceholderCategory.PROJECT,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return data.project?.code ?: ""
        }
    },

    PROJECT_STATUS(
        description = "project status",
        category = EmailTemplatePlaceholderCategory.PROJECT,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return data.project?.status?.name ?: ""
        }
    },

    OUTSOURCE_PROJECT_APPROVE_COMMENT(
        description = "outsource project approve comment",
        category = EmailTemplatePlaceholderCategory.PROJECT,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return (data.context_params["outsource_project_approve_comment"] as? String).orEmpty()
        }
    },

    PROJECT_MANAGER_GIVEN_NAME(
        description = "project manager given name",
        category = EmailTemplatePlaceholderCategory.PROJECT,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            val project = data.project ?: return ""
            Project.join(
                project, setOf(
                    Project::manager.name,
                    "${Project::manager.name}.${ProjectMember::user.name}"
                )
            )
            return project.manager?.user?.given_name.orEmpty()
        }
    },

    PROJECT_COMMON_CONTACT_NAMES(
        description = "project common contact names",
        category = EmailTemplatePlaceholderCategory.PROJECT,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            val project = data.project ?: return ""
            Project.joinOnce(
                project,
                Includes.setOf(
                    Project::project_client_contacts dot ProjectClientContact::client_contact,
                )
            )
            return project.client_common_contacts
                ?.joinToString(", ") { it.name }
                .orEmpty()
        }
    },

    // resolve to first PROJECT_COMMON_CONTACT_NAMES
    PROJECT_COMMON_CONTACT_NAME(
        description = "project (first) common contact name",
        category = EmailTemplatePlaceholderCategory.PROJECT,
        hide_by_default = true,
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            val project = data.project ?: return ""
            Project.joinOnce(
                project,
                Includes.setOf(
                    Project::project_client_contacts dot ProjectClientContact::client_contact,
                )
            )
            return project.client_common_contacts?.firstOrNull()?.name ?: "_______"
        }
    },

    /**
     * if no primary contact, use the first one
     */
    PROJECT_PRIMARY_CONTACT_NAME(
        description = "project primary contact name",
        category = EmailTemplatePlaceholderCategory.PROJECT,
        hide_by_default = false,
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            val project = data.project ?: return ""
            Project.joinOnce(
                project,
                Includes.setOf(
                    Project::project_client_contacts dot ProjectClientContact::client_contact,
                )
            )
            val project_contacts = project
                .project_client_contacts
                ?.filter { it.client_contact?.is_common_user == true }
                .orEmpty()
            return (project_contacts
                .find { it.client_contact_type == ProjectClientContact.Type.PRIMARY }
                ?: project_contacts.firstOrNull())?.client_contact?.name.orEmpty()
        }
    },

    PROJECT_DETAIL_PAGE_URL(
        description = "project detail page url",
        category = EmailTemplatePlaceholderCategory.PROJECT,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            val project_id = data.project?.id ?: return ""
            return Webpage.ProjectDetail(project_id).url
        }
    },

    PROJECT_INVESTMENT_TARGETS(
        description = "project investment targets companies",
        category = EmailTemplatePlaceholderCategory.PROJECT,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            val project = data.project ?: return ""
            Project.join(
                project,
                Includes.setOf(Project::investment_target_companies)
            )
            val res = project.investment_target_companies.orEmpty()
                .joinToString(", ") { company ->
                    var str = company.name.orEmpty()
                    if (company.exchange.isNotBlank())
                        str += " : ${company.exchange}"
                    if (company.stock_code.isNotBlank())
                        str += " : ${company.stock_code}"
                    str
                }
                .takeIf { it.isNotBlank() }
                ?: project.investment_target
                ?: ""
            return res
        }
    },
    //endregion

    //region task angle
    ANGLE_NAME(
        description = "angle name",
        category = EmailTemplatePlaceholderCategory.ANGLE,
        hide_by_default = false,
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            val task = data.task ?: return ""
            if (task.angle == null) {
                Task.join(task, Includes.setOf(Task::angle))
            }
            val angle = data.task?.angle?.name.orEmpty()
            return if (data.rendering_target == PlaceholderBasedModel.RenderingTarget.SUBJECT) angle.to_title_case() else angle
        }
    },
    ANGLE_NAME_PREFIX(
        description = "angle name prefix",
        category = EmailTemplatePlaceholderCategory.ANGLE,
        hide_by_default = false
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            if (data.context_params["angle_prefix"] == null) return ""
            if (data.context_params["angle_prefix"]?.toString()?.toBoolean() == false) return ""
            val task = data.task ?: return ""
            if (task.angle == null) {
                Task.join(task, Includes.setOf(Task::angle))
            }
            val res = data.task?.angle?.name.orEmpty()
            return if (res.isEmpty()) "" else "[$res] "
        }
    },
    ANGLE_NAME_SUFFIX(
        description = "angle name suffix",
        category = EmailTemplatePlaceholderCategory.ANGLE,
        hide_by_default = false
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            if (data.context_params["angle_suffix"] == null) return ""
            if (data.context_params["angle_suffix"]?.toString()?.toBoolean() == false) return ""
            val task = data.task ?: return ""
            if (task.angle == null) {
                Task.join(task, Includes.setOf(Task::angle))
            }
            val res = data.task?.angle?.name.orEmpty()
            return if (res.isEmpty()) "" else " [$res]"
        }
    },
    ANGLE_TOPIC(
        description = "angle topic",
        category = EmailTemplatePlaceholderCategory.ANGLE,
        hide_by_default = false
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            val task = data.task ?: return ""
            if (task.angle == null) {
                Task.join(task, Includes.setOf(Task::angle))
            }
            val angle_topic = data.task?.angle?.topic.orEmpty()
            return if (data.rendering_target == PlaceholderBasedModel.RenderingTarget.SUBJECT) angle_topic.to_title_case() else angle_topic
        }
    },

    ANGLE_TOPIC_OR_PROJECT_NAME(
        description = "angle topic or project name",
        category = EmailTemplatePlaceholderCategory.ANGLE,
        hide_by_default = false
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            val project_name = data.project?.name
                ?: data.task?.project?.name.orEmpty()
            val task = data.task ?: return project_name
            if (task.angle == null) {
                Task.join(task, Includes.setOf(Task::angle))
            }
            val angle_topic_or_project_name = data.task?.angle?.topic ?: project_name
            return if (data.rendering_target == PlaceholderBasedModel.RenderingTarget.SUBJECT) angle_topic_or_project_name.to_title_case() else angle_topic_or_project_name
        }
    },

    ANGLE_DISCUSSION_TOPIC(
        description = "angle discussion topic",
        category = EmailTemplatePlaceholderCategory.ANGLE,
        hide_by_default = false
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            val task = data.task ?: return ""
            if (task.angle == null) {
                Task.join(task, Includes.setOf(Task::angle))
            }
            return data.task?.angle?.discussion_topic.orEmpty()
        }
    },

    ANGLE_DISCUSSION_TOPIC_FRAGMENT(
        description = "advisor discussion fragment",
        category = EmailTemplatePlaceholderCategory.ANGLE,
        hide_by_default = false,
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            val task = data.task ?: return ""
            if (task.angle == null) {
                Task.join(task, Includes.setOf(Task::angle))
            }
            return if (data.task?.angle?.discussion_topic.isNullOrBlank()) {
                ""
            } else {
                EmailTemplateUtil.resolve_fragment(
                    content_type = EmailContentType.ANGLE_DISCUSSION_TOPIC_FRAGMENT,
                    data,
                )
            }
        }
    },
    //endregion

    //region task
    TASK_ID(
        description = "task id",
        category = EmailTemplatePlaceholderCategory.TASK,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return data.task?.id?.toString() ?: ""
        }
    },

    TASK_DISPLAY_ID(
        description = "task display id",
        category = EmailTemplatePlaceholderCategory.TASK,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return data.task?.display_id ?: ""
        }
    },

    TASK_CONTACT_SCHEDULE_START_TIME(
        description = "task contact schedule starttime",
        category = EmailTemplatePlaceholderCategory.TASK,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return (data.context_params["contact_schedule"] as? Schedule)
                ?.start_time
                ?.let {
                    DateTimeFormatter.RFC_1123_DATE_TIME
                        .withZone(data.resolve_timezone())
                        .format(it)
                }.orEmpty()
        }
    },

    TASK_CONTACT_SCHEDULE_END_TIME(
        description = "task contact schedule endtime",
        category = EmailTemplatePlaceholderCategory.TASK,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return (data.context_params["contact_schedule"] as? Schedule)
                ?.end_time
                ?.let {
                    DateTimeFormatter.RFC_1123_DATE_TIME
                        .withZone(data.resolve_timezone())
                        .format(it)
                }.orEmpty()
        }
    },

    TASK_CONTACT_SCHEDULE_RANGES(
        description = "task contact schedule ranges",
        category = EmailTemplatePlaceholderCategory.TASK,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            @Suppress("UNCHECKED_CAST")
            val schedules = Task.join(
                data.task!!,
                Includes.setOf(Task::contact_schedules)
            ).contact_schedules
            return schedules?.joinToString("") {
                // WTF?
                "${"&nbsp;".repeat(15)}${
                    DateTimeFormatter.RFC_1123_DATE_TIME.withZone(data.resolve_timezone())
                        .format(it.start_time)
                } - ${
                    DateTimeFormatter.RFC_1123_DATE_TIME.withZone(data.resolve_timezone())
                        .format(it.end_time)
                }<br/>"
            } ?: ""
        }
    },

    TASK_CONTACT_SCHEDULE_COMMENT(
        description = "task contact schedule comment",
        category = EmailTemplatePlaceholderCategory.TASK,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            val schedule = (data.context_params["contact_schedule"] as? Schedule)
                ?: Task.join(
                    data.task!!,
                    Includes.setOf(Task::contact_schedules)
                ).contact_schedules?.firstOrNull()
            return schedule?.comment.orEmpty()
        }
    },

    TASK_CONTACT_FEEDBACK_EXPERTISE_RATE(
        description = "task contact feedback expertise rate",
        category = EmailTemplatePlaceholderCategory.TASK,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return (data.context_params["contact_feedback"] as? TaskContactFeedback)?.expertise_rate?.toString()
                .orEmpty()
        }
    },
    TASK_CONTACT_FEEDBACK_COMMUNICATION_RATE(
        description = "task contact feedback communication rate",
        category = EmailTemplatePlaceholderCategory.TASK,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return (data.context_params["contact_feedback"] as? TaskContactFeedback)?.communication_rate?.toString()
                .orEmpty()
        }
    },
    TASK_CONTACT_FEEDBACK_PROFESSIONALISM_RATE(
        description = "task contact feedback professionalism rate",
        category = EmailTemplatePlaceholderCategory.TASK,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return (data.context_params["contact_feedback"] as? TaskContactFeedback)?.professionalism_rate?.toString()
                .orEmpty()
        }
    },
    TASK_CONTACT_FEEDBACK_COMMENT(
        description = "task contact feedback comment",
        category = EmailTemplatePlaceholderCategory.TASK,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return (data.context_params["contact_feedback"] as? TaskContactFeedback)?.comment.orEmpty()
        }
    },

    TASK_CAP_LEGAL_COMMENT(
        description = "task capvision legal comment",
        category = EmailTemplatePlaceholderCategory.TASK,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return (data.context_params["task_cap_legal_review"] as? TaskCapLegalReview)?.comment.orEmpty()
        }
    },

    TASK_CAP_LEGAL_REJECT_REASON(
        description = "task capvision legal reject reason",
        category = EmailTemplatePlaceholderCategory.TASK,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return (data.context_params["task_cap_legal_review"] as? TaskCapLegalReview)?.reject_reason.orEmpty()
        }
    },

    TASK_CLIENT_LEGAL_NAME(
        description = "task client legal name",
        category = EmailTemplatePlaceholderCategory.TASK,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            val review =
                (data.context_params["task_client_legal_review"] as? TaskClientLegalReview)
                    ?: return ""
            TaskClientLegalReview.join(
                review, setOf(
                    TaskClientLegalReview::contact.name,
                    TaskClientLegalReview::user.name
                )
            )
            // for some reason, our legal can approve a task in place of client legal
            return review.contact?.name
                ?: review.user?.name.orEmpty()
        }
    },

    TASK_CLIENT_LEGAL_COMMENT(
        description = "task client legal comment",
        category = EmailTemplatePlaceholderCategory.TASK,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return (data.context_params["task_client_legal_review"] as? TaskClientLegalReview)?.comment.orEmpty()
        }
    },

    TASK_CLIENT_LEGAL_REJECT_REASON(
        description = "task client legal reject reason",
        category = EmailTemplatePlaceholderCategory.TASK,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return (data.context_params["task_client_legal_review"] as? TaskClientLegalReview)?.reject_reason.orEmpty()
        }
    },

    TASK_DATE(
        description = "task date",
        category = EmailTemplatePlaceholderCategory.TASK,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            val passed_start_time =
                (data.context_params[TASK_START_TIME.name] as? String)?.let {
                    Instant.parse(it)
                }
            val start_time = passed_start_time
                ?: data.task?.start_time ?: return ""
            return Formatters.DATETIME.`yyyy-MM-dd`
                .format_with_zone_id(start_time, data.resolve_timezone())
        }
    },

    TASK_DATE_EEEE_MMMM(
        description = "task date eeee-mmmm",
        category = EmailTemplatePlaceholderCategory.TASK,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            val start_time = data.task?.start_time ?: return ""
            return Formatters.DATETIME.`EEEE, MMMM`.format_with_zone_id(start_time, data.resolve_timezone())
        }
    },

    TASK_DATE_EEEE_MMMM_D(
        description = "task date eeee-mmmm-d",
        category = EmailTemplatePlaceholderCategory.TASK,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            val start_time = data.task?.start_time ?: return ""
            return Formatters.DATETIME.`EEEE, MMMM d`.format_with_zone_id(start_time, data.resolve_timezone())
        }
    },

    TASK_START_TIME(
        description = "task start time",
        category = EmailTemplatePlaceholderCategory.TASK,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            if (self in data.no_replace_placeholders) return self
            val passed_value =
                (data.context_params[this.name] as? String)?.let { Instant.parse(it) }
            val start_time = passed_value ?: data.task?.start_time ?: return self
            val zoneId = data.resolve_timezone()
            return Formatters.DATETIME.`EEEE, MMMM dd h mm a`
                .withZone(zoneId)
                .format_with_zone_id(start_time, zoneId)
        }
    },

    TASK_END_TIME(
        description = "task end time",
        category = EmailTemplatePlaceholderCategory.TASK,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            if (self in data.no_replace_placeholders) return self
            val passed_value =
                (data.context_params[this.name] as? String)?.let { Instant.parse(it) }
            val end_time = passed_value ?: data.task?.end_time ?: return self
            val zoneId = data.resolve_timezone()
            return Formatters.DATETIME.`EEEE, MMMM dd h mm a`
                .withZone(zoneId)
                .format_with_zone_id(end_time, zoneId)
        }
    },

    VETTING_CALL_START_TIME(
        description = "vetting call start time",
        category = EmailTemplatePlaceholderCategory.TASK,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            if (self in data.no_replace_placeholders) return self
            val passed_value =
                (data.context_params[this.name] as? String)?.let { Instant.parse(it) }
            val start_time = passed_value ?: data.extra_context?.task_arrangement?.start_time ?: return self
            val zoneId = data.resolve_timezone()
            return Formatters.DATETIME.`EEEE, MMMM dd h mm a`
                .withZone(zoneId).withLocale(Locale.ENGLISH)
                .format_with_zone_id(start_time, zoneId)
        }
    },

    TASK_CONSULTATION_TIME(
        description = "payment consultation time",
        category = EmailTemplatePlaceholderCategory.TASK,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            val task = data.task ?: return ""
            val payment = task.advisor_normal_payment
                ?: Task.join(task, setOf(Task::advisor_normal_payment.name))
                    .advisor_normal_payment
                ?: return ""
            return "${payment.hours.safe_change_precision_to_2digits()} Hours"
        }
    },

    TASK_PROJECT_PAGE_URL(
        description = "task url in project page",
        category = EmailTemplatePlaceholderCategory.TASK,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            val task = data.task ?: return ""
            Task.join(task, Includes.setOf(Task::db_page_url_jit))
            return task.db_page_url_jit.orEmpty()
        }
    },

    //endregion

    //region task: advisor info
    TASK_DISPLAY_RATE_MULTIPLIER_TO_CLIENT(
        description = "task display rate multiplier to client",
        category = EmailTemplatePlaceholderCategory.TASK,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            val task = data.task ?: return ""
            Task.join(
                task, Includes.setOf(
                    Task::senior_rate_multiplier,
                    Task::given_senior_rate_multiplier,
                    Task::senior_rate_multiplier_considering_unsigned_tc,
                    Task::standard_rate_multiplier_display_to_client
                )
            )
            val multiplier = task.standard_rate_multiplier_display_to_client
                ?: return ""
            return "${multiplier.safe_change_precision_to_2digits()}"
        }
    },

    TASK_HOURLY_RATE(
        description = "payment hourly rate",
        category = EmailTemplatePlaceholderCategory.TASK,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            val task = data.task ?: return ""
            val payment = task.advisor_normal_payment
                ?: Task.join(task, setOf(Task::advisor_normal_payment.name))
                    .advisor_normal_payment
                ?: return ""
            return "${payment.rate} ${payment.currency}"
        }
    },

    /**
     * e.g. 100.5 USD
     */
    TASK_RATE_WITH_CURRENCY(
        description = "task rate with currency",
        category = EmailTemplatePlaceholderCategory.TASK,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            val task = data.task ?: return ""
            // e.g. when we send outreach in chrome extension, the task might haven't be created yet.
            val (rate, rate_currency) = if (data.context_params["rate"] != null) {
                data.context_params["rate"]?.toString()
                    ?.toBigDecimal() to data.context_params["rate_currency"]
            } else {
                task.rate to task.rate_currency
            }
            if (rate == null || rate_currency == null) return ""
            return "${rate.safe_change_precision_to_0digits()} $rate_currency"
        }
    },

    TASK_NOMINAL_CHARGE_RATE(
        description = "Nominal Charge Rate",
        category = EmailTemplatePlaceholderCategory.TASK,
        hide_by_default = false
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            val task = data.task ?: return ""
            return (task.client_rate_usd
                ?: task.default_client_rate_usd
                ?: task.default_client_rate_usd_considering_unsigned_tc)?.toString()
                ?: ""
        }
    },

    TASK_PAYMENT_AMOUNT(
        description = "payment amount",
        category = EmailTemplatePlaceholderCategory.TASK,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            val task = data.task ?: return ""
            val payment = task.advisor_normal_payment
                ?: Task.join(task, setOf(Task::advisor_normal_payment.name))
                    .advisor_normal_payment
                ?: return ""
            return "${
                payment.amount.setScale(2).toPlainString()
            } ${payment.currency}"
        }
    },

    TASK_ADVISOR_COMPANY_NAME(
        description = "task advisor company name",
        category = EmailTemplatePlaceholderCategory.TASK,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            val task = data.task ?: return ""
            Task.join(
                task, setOf(
                    Task::advisor_profile.name,
                    *setOf(
                        AdvisorJob::company.name
                    ).add_prefix(Task::advisor_profile.name)
                )
            )
            return task.advisor_profile?.company?.name ?: ""
        }
    },

    TASK_ADVISOR_POSITION(
        description = "task advisor position name",
        category = EmailTemplatePlaceholderCategory.TASK,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            val task = data.task ?: return ""
            Task.join(
                task, setOf(
                    Task::advisor_profile.name
                )
            )
            return task.advisor_profile?.position ?: ""
        }
    },

    // processed in front end
    TASK_ADVISOR_BIOS(
        description = "task advisor bios",
        category = EmailTemplatePlaceholderCategory.TASK,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            // currently, this placeholder is resolved in client
            return "{$name}"
        }
    },
    //endregion

    // region arrange
    WEISS_USERS_DISCLAIMER_ONE(
        description = "weiss users disclaimer one",
        category = EmailTemplatePlaceholderCategory.CLIENT,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            Client.join(data.client!!, Includes.setOf(Client::preference))
            return if (data.client?.preference?.preferred_workflow == ProjectWorkflow.WEISS) {
                " At the start of the call, please remember to read the following statement to the expert and affirm verbal confirmation from the expert:<br><br>" +
                        "<em>\"Please confirm that you understand that we are an investment firm that may use the information you provide in making investments. " +
                        "Please also confirm that you understand that we are not seeking, and you agree that you will not provide to us any confidential information about any public company.\"</em>"
            } else ""
        }
    },

    WEISS_USERS_DISCLAIMER_TWO(
        description = "weiss users disclaimer two",
        category = EmailTemplatePlaceholderCategory.CLIENT,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            Client.join(data.client!!, Includes.setOf(Client::preference))
            return if (data.client?.preference?.preferred_workflow == ProjectWorkflow.WEISS) {
                "<br>Disclaimer: As a reminder, you shall in no way, during the course of this consultation, seek any information that you believe (i) may constitute material non-public information, (ii) must be kept confidential, or (iii) should not otherwise be disclosed. If you receive or believe you may have received any such information, please contact a member of Compliance immediately. Finally, please be sure to remind the consultant at the outset of the consultation not to discuss any topics on which they believe they hold confidential information. All approvals are valid for 30 (thirty) calendar days. A new approval is required for every consultation. Contact Compliance with any questions or concerns.<br><br>"
            } else ""
        }
    },

    WEISS_EXPERT_DISCLAIMER(
        description = "weiss expert disclaimer",
        category = EmailTemplatePlaceholderCategory.CLIENT,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            Client.join(data.client!!, Includes.setOf(Client::preference))
            return if (data.client?.preference?.preferred_workflow == ProjectWorkflow.WEISS) {
                if (data.advisor?.is_cn == true || data.advisor?.origin.equals("CN")) {
                    // China-based expert
                    "<br>Compliance Disclaimer: You understand this client is an investment firm that may use the information you provide in making investments and it could be seriously harmed if you provide it with any confidential information about a public company. You understand that this client is not seeking, and you agree that you will not provide to it any confidential information about any public company.<br>" +
                            "免责声明: 您了解该客户是一家投资公司，可能会使用您提供的信息进行投资，如果您向其提供有关上市公司的任何机密信息，该客户可能会受到严重损害。您了解该客户并非寻求，并且您同意不会向其提供有关任何上市公司的任何机密信息。<br><br>"
                } else {
                    // non-China-based expert
                    "<br>Compliance Disclaimer: You understand this client is an investment firm that may use the information you provide in making investments and it could be seriously harmed if you provide it with any confidential information about a public company. You understand that this client is not seeking, and you agree that you will not provide to it any confidential information about any public company.<br><br>"
                }
            } else ""
        }
    },

    CLIENT_ESTUARY_DISCLAIMER(
        description = "client estuary disclaimer",
        category = EmailTemplatePlaceholderCategory.CLIENT,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            Client.join(data.client!!, Includes.setOf(Client::preference))
            return if (data.client?.preference?.preferred_workflow == ProjectWorkflow.ESTUARY_CAPITAL_MANAGEMENT) {
                "As a reminder, you must read this aloud at the beginning of the call: " +
                        "<em>\"You have signed and returned to the Expert Network/Estuary Capital Management LP the questionnaire and acknowledgment for this call. Are your responses to that document still accurate today? Thank you. As you know, I am calling from Estuary Capital Management LP, an investment adviser that trades securities. As such, we do not want to receive any confidential or material, nonpublic information about any publicly traded companies because we do not want to restrict our trading ability.\"</em> "
            } else ""
        }
    },
    // endregion

    //region arrange/other bridge
    BRIDGE_INFO(
        description = "task conference bridge info",
        category = EmailTemplatePlaceholderCategory.TASK,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            if (self in data.no_replace_placeholders) return self
            return data.extra_context?.task_arrangement?.arrange_detail_str
                ?: data.task?.arrange_detail_str
                ?: self
        }
    },

    ADVISOR_BRIDGE_INFO(
        description = "task conference advisor bridge info",
        category = EmailTemplatePlaceholderCategory.TASK,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            if (self in data.no_replace_placeholders) return self
            return data.extra_context?.task_arrangement?.arrange_detail_str
                ?: data.task?.arrange_advisor_bridge_info_str
                ?: self
        }
    },
    //endregion

    //region arrange/loopup
    // e.g: Conference Access Number: USA +****************
    // 取决于area, 即loopup_number
    //fixme 似乎有个bug: 这里传的area都是专家的area, 而客户的邮件模板里也是用的这个placeholder
    CONFERENCE_LINE_DAIL_IN_ACCESS(
        description = "conference line client contact dial in access",
        category = EmailTemplatePlaceholderCategory.TASK,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            if (self in data.no_replace_placeholders) return self

            val areaAccessNumber = if (data.extra_context?.advisor_area_loopup_number != null) {
                data.extra_context?.advisor_area_loopup_number!!
            } else {
                val number_id = data.context_params["loopup_area_id"]?.toString()?.toInt() ?: return ""
                LoopupNumber.get(number_id)
            }

            return "${areaAccessNumber.area_name} ${areaAccessNumber.dial_in_number}"
        }
    },

    // e.g: Dial-In Code: 2456675#
    // 取决于room, 即loopup_room
    CONFERENCE_LINE_NUMBER(
        description = "conference line number",
        category = EmailTemplatePlaceholderCategory.TASK,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            if (self in data.no_replace_placeholders) return self

            val provided = data.context_params[this.name]?.toString()?.let { "$it#" }
            if (!provided.isNullOrBlank()) return provided

            val room = data.extra_context?.loopup_room
                ?: Task.join(data.task ?: return "", setOf(Task::loopup_room.name))
                    .loopup_room

            return room?.let { "${it.guest_code}#" }
                ?: "{{$name}}"
        }
    },

    // e.g: Call me to dial me in: https://meet.loopup.com/EEs72vB
    // 取决于room, 即loopup_room
    CONFERENCE_LINE_LINK(
        description = "conference line link",
        category = EmailTemplatePlaceholderCategory.TASK,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            if (self in data.no_replace_placeholders) return self

            val provided = data.context_params[this.name]?.toString()?.let { "https://$it" }
            if (!provided.isNullOrBlank()) return provided

            val room = data.extra_context?.loopup_room
                ?: Task.join(data.task ?: return "", setOf(Task::loopup_room.name))
                    .loopup_room
            return room?.url?.let { "https://$it" }
                ?: "{{$name}}"
        }
    },

    // e.g: One click dial-in: +****************,,2456675#
    // area和room的结合
    CONFERENCE_LINE_ONE_CLICK_DIAL_IN_LINK_CONTACT(
        description = "conference line one click dial-in link (Contact)",
        category = EmailTemplatePlaceholderCategory.TASK,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            if (self in data.no_replace_placeholders) return self

            // part 1
            val accessPhoneNumbers =
                data.extra_context?.contact_area_loopup_numbers
                    ?: data.task?.contact_loopup_area_ids?.split(",")
                        ?.filter { it.isNotBlank() }?.map { it.toInt() }.orEmpty()
                        .map { LoopupNumber.get(it) }
            val phoneNumbers = accessPhoneNumbers.map { it.dial_in_number }

            // part 2
            val dial_in_code = CONFERENCE_LINE_NUMBER.resolve(data)

            // concat
            val res = phoneNumbers.joinToString(", ") { dial_in_number ->
                "${dial_in_number},,,$dial_in_code".let {
                    "<a href=\'tel:$it\'>$it</a>"
                }
            }
            return res
        }
    },

    CONFERENCE_LINE_ONE_CLICK_DIAL_IN_LINK_ADVISOR(
        description = "conference line one click dial-in link (Advisor)",
        category = EmailTemplatePlaceholderCategory.TASK,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            if (self in data.no_replace_placeholders) return self

            // part 1
            val accessPhoneNumber =
                data.extra_context?.advisor_area_loopup_number
                    ?: data.task?.advisor_loopup_area_id?.let { LoopupNumber.get(it) }
            val dial_in_number = accessPhoneNumber?.dial_in_number ?: return ""

            // part 2
            val dial_in_code = CONFERENCE_LINE_NUMBER.resolve(data)

            // concat
            val res = "${dial_in_number},,,$dial_in_code".let {
                "<a href=\'tel:$it\'>$it</a>"
            }
            return res
        }
    },

    //endregion

    //region arrange/twilio


    //endregion

    //region zoom
    ZOOM_MEETING_NUMBER(
        description = "zoom meeting number",
        category = EmailTemplatePlaceholderCategory.TASK,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            if (self in data.no_replace_placeholders) return self
            return data.extra_context?.zoom_meeting?.display_meeting_id ?: self
        }
    },

    ZOOM_MEETING_PWD(
        description = "zoom meeting password",
        category = EmailTemplatePlaceholderCategory.TASK,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            if (self in data.no_replace_placeholders) return self
            return data.extra_context?.zoom_meeting?.password ?: self
        }
    },

    ZOOM_MEETING_JOIN_LINK(
        description = "zoom meeting join link",
        category = EmailTemplatePlaceholderCategory.TASK,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            if (self in data.no_replace_placeholders) return self
            return data.extra_context?.zoom_meeting?.join_url ?: self
        }
    },

    ZOOM_MEETING_ONE_CLICK_DIAL(
        description = "zoom meeting one click dial",
        category = EmailTemplatePlaceholderCategory.TASK,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            if (self in data.no_replace_placeholders) return self
            val client = data.client ?: return ""
            Client.join(client, Includes.setOf(Client::preference))
            if (client.preference?.include_one_click_dial_in_client_invite != true) return ""
            val meeting = data.extra_context?.zoom_meeting ?: return ""
            return "One-Click Dial: <a href=\"tel:+16469313860,,${meeting.meeting_id}#,,,,*${meeting.password}#\" target=\"_blank\" rel=\"noopener\">+16469313860,,${meeting.meeting_id}#,,,,*${meeting.password}#</a><br />Meeting ID: ${meeting.meeting_id}<br />Passcode: ${meeting.password}<br /><br />"
        }
    },
    //endregion

    // region payment
    PAYMENT_HOURLY_RATE(
        description = "payment hourly rate",
        category = EmailTemplatePlaceholderCategory.MISCELLANEOUS,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            @Suppress("UNCHECKED_CAST")
            val payment = (data.context_params["payment"] as? Map<String, String?>)
                ?: return ""
            return payment[this.name] ?: ""
        }
    },

    PAYMENT_AMOUNT(
        description = "payment amount",
        category = EmailTemplatePlaceholderCategory.MISCELLANEOUS,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            @Suppress("UNCHECKED_CAST")
            val payment = (data.context_params["payment"] as? Map<String, Any?>)
                ?: return ""
            return payment[this.name]?.toString().orEmpty()
        }
    },

    PAYMENT_TIME(
        description = "payment time",
        category = EmailTemplatePlaceholderCategory.MISCELLANEOUS,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            @Suppress("UNCHECKED_CAST")
            val payment = (data.context_params["payment"] as? Map<String, Any?>)
                ?: return ""
            return payment[this.name]?.toString().orEmpty()
        }
    },

    PAYMENT_PROJECT_NAME(
        description = "payment project name",
        category = EmailTemplatePlaceholderCategory.MISCELLANEOUS,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            @Suppress("UNCHECKED_CAST")
            return (data.context_params["payment"] as? Map<String, String?>)
                ?.get(this.name)
                ?: data.project?.name.orEmpty()
        }
    },

    PAYMENT_TASK_MANAGER_NAME(
        description = "payment task manager name",
        category = EmailTemplatePlaceholderCategory.MISCELLANEOUS,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            @Suppress("UNCHECKED_CAST")
            return (data.context_params["payment"] as? Map<String, String?>)
                ?.get(this.name)
                ?: data.user?.name.orEmpty()
        }
    },

    PAYMENT_TASK_MANAGER_SIGNATURE(
        description = "payment task manager signature",
        category = EmailTemplatePlaceholderCategory.MISCELLANEOUS,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            @Suppress("UNCHECKED_CAST")
            return (data.context_params["payment"] as? Map<String, String?>)
                ?.get(this.name)
                ?: data.user?.signature.orEmpty()
        }
    },

    PAYMENT_METHOD(
        description = "payment method type",
        category = EmailTemplatePlaceholderCategory.MISCELLANEOUS,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return (data.context_params["advisor_payment_form"] as? AdvisorPaymentForm?)?.let { form ->
                AdvisorPaymentForm.join(form, Includes.setOf(AdvisorPaymentForm::pay_method_name))
            }?.pay_method_name
                ?: ""
        }
    },

    PAYMENT_FORM_AMOUNT(
        description = "payment method type",
        category = EmailTemplatePlaceholderCategory.MISCELLANEOUS,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            val snapshot = (data.context_params["payment_form"] as? PaymentForm?)
                ?.payment_snapshots?.maxByOrNull { it.id }

            return "${snapshot?.amount?.setScale(2)?.toPlainString() ?: ""} ${snapshot?.currency_name ?: ""}"
        }
    },

    PAYMENT_FORM_DURATION(
        description = "payment method type",
        category = EmailTemplatePlaceholderCategory.MISCELLANEOUS,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            val snapshot = (data.context_params["payment_form"] as? PaymentForm?)
                ?.payment_snapshots?.maxByOrNull { it.id }

            return "${snapshot?.minutes ?: ""} minutes"
        }
    },

    PAYMENT_TROLLEY_INCOMPLETE_PROFILE(
        description = "payment trolley incomplete profile",
        category = EmailTemplatePlaceholderCategory.MISCELLANEOUS,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            data.advisor?.let { advisor ->
                Advisor.join(advisor, Includes.setOf(Advisor::is_trolley_recipient_incomplete))
            }

            return if (data.advisor?.is_trolley_recipient_incomplete != false) " Please be advised that your Payment Information is incomplete. We kindly remind you to complete it to ensure a smooth payment process."
            else ""
        }
    },
    //endregion

    //region revenue preview
    REVENUE_FORM_PREVIEW(
        description = "revenue form preview",
        category = EmailTemplatePlaceholderCategory.MISCELLANEOUS,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            @Suppress("UNCHECKED_CAST")
            val revenue =
                (data.context_params["revenue_preview"] as? Map<String, String?>)
                    ?: return ""
            val all_items = listOf(
                "Basic Hour" to revenue["basic_hour"]?.let { "$it CapH" },
                "Senior Expert" to revenue["senior_expert"]?.let { "x$it" },
                "Overseas" to revenue["overseas"]?.let { "x$it" },
                "Translation" to revenue["translation"]?.let { "$it CapH" },
                "Transcription" to revenue["transcription"]?.let { "$it CapH" },
                "In Person" to revenue["in_person"]?.let { "$it CapH" },
                "Discount" to revenue["discount"]?.let { "-$it CapH" },
                "Total" to revenue["total"]
            )

            @Suppress("UNCHECKED_CAST")
            val show_items = all_items.filter {
                it.second != null
            } as List<Pair<String, String>>

            return TableTag(
                tbodyTag = TbodyTag(children = show_items.map { (item, amount) ->
                    TrTag(
                        children = listOf(
                            TdTag(width = 120, text = item),
                            TdTag(width = 178, text = amount, style = "align=right")
                        )
                    )
                }),
                style = "border=\"1\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse: collapse;\""
            ).toHtml()
        }
    },

    BILLING_NOTES_PREVIEW(
        description = "billing notes preview",
        category = EmailTemplatePlaceholderCategory.MISCELLANEOUS,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            @Suppress("UNCHECKED_CAST")
            val revenue =
                (data.context_params["revenue_preview"] as? Map<String, String?>)
                    ?: return ""
            return revenue["billing_notes"] ?: ""
        }
    },
    //endregion

    //region client
    CLIENT_NAME(
        description = "client name",
        category = EmailTemplatePlaceholderCategory.CLIENT,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return data.client?.name ?: ""
        }
    },

    CLIENT_TYPE(
        description = "client type",
        category = EmailTemplatePlaceholderCategory.CLIENT
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return data.client?.type?.value ?: ""
        }
    },

    CLIENT_PROFILE_PAGE_URL(
        description = "client profile page url",
        category = EmailTemplatePlaceholderCategory.CLIENT,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            val client_id = data.client?.id ?: return ""
            return Webpage.ClientProfile(client_id).url
        }
    },

    CLIENT_RECORDING_EXPIRATION(
        description = "client recording expiration",
        category = EmailTemplatePlaceholderCategory.CLIENT,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            val preference = data.task?.let { task ->
                Task.join(task, Includes.setOf(Task::client dot Client::preference))
                task.client?.preference
            } ?: return ""
            if (preference.enable_expire_delete_recordings_transcripts == false) {
                return ""
            }
            val days = preference.recordings_expire_duration_minutes?.div(60)?.div(24)
            return " and you will be unable to download the recording after $days days"
        }
    },
    //endregion

    //region contract
    CONTRACT_REQUESTER_NAME(
        description = "contract requester name",
        category = EmailTemplatePlaceholderCategory.CONTRACT,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            data.contract?.let {
                Contract.join(it, Includes.setOf(Contract::requester))
            }
            return data.contract?.requester?.name.orEmpty()
        }
    },

    CONTRACT_GENERATOR_NAME(
        description = "contract generator name",
        category = EmailTemplatePlaceholderCategory.CONTRACT,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            data.contract?.let {
                Contract.join(it, Includes.setOf(Contract::generator))
            }
            return data.contract?.generator?.name.orEmpty()
        }
    },

    CONTRACT_APPLIER_NAME(
        description = "contract applier name",
        category = EmailTemplatePlaceholderCategory.CONTRACT,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            data.contract?.let {
                Contract.join(it, Includes.setOf(Contract::applier))
            }
            return data.contract?.applier?.name.orEmpty()
        }
    },

    CONTRACT_APPROVER_NAME(
        description = "contract approver name",
        category = EmailTemplatePlaceholderCategory.CONTRACT,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            data.contract?.let {
                Contract.join(it, Includes.setOf(Contract::applier))
            }
            return data.contract?.approver?.name.orEmpty()
        }
    },

    CONTRACT_NAME(
        description = "contract name",
        category = EmailTemplatePlaceholderCategory.CONTRACT,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return data.contract?.name ?: ""
        }
    },

    CONTRACT_PAYMENT_TERM_SIZE(
        description = "contract payment term size",
        category = EmailTemplatePlaceholderCategory.CONTRACT,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            val contract = data.contract ?: return ""
            Contract.join(contract, setOf(Contract::payment_term.name))
            return data.contract?.payment_term?.size?.toString().orEmpty()
        }
    },

    CONTRACT_APPROVAL_PAGE_URL(
        description = "contract approval page url",
        category = EmailTemplatePlaceholderCategory.CONTRACT,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            val contract_id = data.contract?.id ?: return ""
            return Webpage.ContractApproval(contract_id).url
        }
    },

    CONTRACT_DETAIL_PAGE_URL(
        description = "contract profile page url",
        category = EmailTemplatePlaceholderCategory.CONTRACT,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            val contract_id = data.contract?.id ?: return ""
            return Webpage.ContractDetail(contract_id).url
        }
    },

    CONTRACT_REQUEST_REJECT_REASON(
        description = "contract request reject reason",
        category = EmailTemplatePlaceholderCategory.CONTRACT,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return data.contract?.generate_note ?: ""
        }
    },

    CONTRACT_APPLY_REJECT_REASON(
        description = "contract apply reject reason",
        category = EmailTemplatePlaceholderCategory.CONTRACT,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return data.contract?.approve_note ?: ""
        }
    },

    CONTRACT_PAYWAY(
        description = "contract payway",
        category = EmailTemplatePlaceholderCategory.CONTRACT,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return data.contract?.payway?.name ?: ""
        }
    },

    CONTRACT_START_DATE(
        description = "contract start date",
        category = EmailTemplatePlaceholderCategory.CONTRACT,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            val start_time = data.contract?.start_time ?: return ""
            return Formatters.DATETIME.`yyyy-MM-dd`
                .format_with_zone_id(start_time, data.resolve_timezone())
        }
    },

    CONTRACT_END_DATE(
        description = "contract end date",
        category = EmailTemplatePlaceholderCategory.CONTRACT,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            val end_time = data.contract?.end_time ?: return ""
            return Formatters.DATETIME.`yyyy-MM-dd`
                .format_with_zone_id(end_time, data.resolve_timezone())
        }
    },

    CONTRACT_REST_EXPIRE_DAYS(
        description = "count of days until the contract expires",
        category = EmailTemplatePlaceholderCategory.CONTRACT,
        hide_by_default = false
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            val end_time = data.contract?.end_time ?: return ""
            //half adjust(四舍五入)
            return Duration.between(Instant.now(), end_time.plus(12, ChronoUnit.HOURS)).toDays().toString()
        }
    },

    CONTRACT_TOTAL_HOURS(
        description = "contract total hours",
        category = EmailTemplatePlaceholderCategory.CONTRACT,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return data.contract?.charge_hours?.safe_change_precision_to_2digits()
                ?.toString() ?: ""
        }
    },

    CONTRACT_USED_HOURS(
        description = "contract use hours",
        category = EmailTemplatePlaceholderCategory.CONTRACT,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            val contract = data.contract ?: return ""
            Contract.join(contract, setOf(Contract::used_hours.name))
            return contract.used_hours!!.safe_change_precision_to_2digits()
                .toString()
        }
    },

    CONTRACT_REMAINING_HOURS(
        description = "contract remaining hours",
        category = EmailTemplatePlaceholderCategory.CONTRACT,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            val contract = data.contract ?: return ""
            if (contract.payway !in setOf(PayWay.TRIAL, PayWay.PREPAY)) return ""
            Contract.join(contract, setOf(Contract::used_hours.name))
            return (contract.charge_hours!! - contract.used_hours!!).safe_change_precision_to_2digits()
                .toString()
        }
    },

    //endregion

    //region contract: payment term
    CONTRACT_PAYMENT_TERM_AMOUNT(
        description = "contract payment term amount",
        category = EmailTemplatePlaceholderCategory.CONTRACT_PAYMENT_TERM,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return data.contract_payment_term?.amount?.let {
                Formatters.NUMBER.`dot00#`.format(it)
            }.orEmpty()
        }
    },

    CONTRACT_PAYMENT_TERM_INVOICE_DATE(
        description = "contract payment term invoice date",
        category = EmailTemplatePlaceholderCategory.CONTRACT_PAYMENT_TERM,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return data.contract_payment_term?.invoice_date.orEmpty()
        }
    },
    //endregion

    //region client contact
    CLIENT_CONTACT_NAME(
        description = "contact name",
        category = EmailTemplatePlaceholderCategory.CONTACT,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return data.contact?.name ?: ""
        }
    },

    CLIENT_CONTACT_FIRST_NAME(
        description = "contact first name",
        category = EmailTemplatePlaceholderCategory.CONTACT,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return data.contact?.firstname ?: ""
        }
    },

    CLIENT_CONTACT_PROFILE_PAGE_URL(
        description = "client contact profile page url",
        category = EmailTemplatePlaceholderCategory.CONTACT,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            val client_contact_id = data.contact?.id ?: return ""
            return Webpage.ClientContactProfile(client_contact_id).url
        }
    },
    //endregion

    //region invoice
    INVOICE_AMOUNT(
        description = "invoice amount",
        category = EmailTemplatePlaceholderCategory.INVOICE,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return data.invoice?.amount?.let {
                Formatters.NUMBER.`dot00#`.format(it)
            }.orEmpty()
        }
    },
    //endregion

    //region portal url
    /**
     * are we actully using this?
     */
    PORTAL_ADVISOR_SURVEY_URL(
        description = "portal advisor post call survey url",
        category = EmailTemplatePlaceholderCategory.PORTAL_URL,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return data.app_context
                .getBean<PortalProperties>()
                .advisorSurveyUrl?.let {
                    UrlUtil.join(it, PortalType.ADVISOR_POST_CALL.token_placeholder)
                }.orEmpty()
        }
    },

    PORTAL_ADVISOR_PRE_CALL_URL(
        description = "portal advisor pre call url",
        category = EmailTemplatePlaceholderCategory.PORTAL_URL,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return data.app_context
                .getBean<PortalProperties>()
                .advisorPreCallUrl
                ?.let {
                    UrlUtil.join(it, PortalType.ADVISOR_PRE_CALL.token_placeholder)
                }.orEmpty()
        }
    },

    PORTAL_ADVISOR_POST_CALL_URL(
        description = "portal advisor post call url",
        category = EmailTemplatePlaceholderCategory.PORTAL_URL,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return data.app_context
                .getBean<PortalProperties>()
                .advisorPostCallUrl
                ?.let {
                    UrlUtil.join(it, PortalType.ADVISOR_POST_CALL.token_placeholder)
                }.orEmpty()
        }
    },

    PORTAL_ADVISOR_BANK_ACCOUNT_URL(
        description = "portal advisor bank account form url",
        category = EmailTemplatePlaceholderCategory.PORTAL_URL,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return data.app_context
                .getBean<PortalProperties>()
                .advisorBankAccountUrl
                ?.let {
                    UrlUtil.join(
                        it,
                        PortalType.ADVISOR_BANK_ACCOUNT.token_placeholder
                    )
                }.orEmpty()
        }
    },

    PORTAL_ADVISOR_UNSUBSCRIBE_OUTREACH_LINK(
        description = "portal advisor unsubscribe outreach link",
        category = EmailTemplatePlaceholderCategory.PORTAL_URL,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return data.app_context
                .getBean<PortalProperties>()
                .advisorUnsubscribeOutreachUrl
                ?.let {
                    UrlUtil.join(
                        it,
                        PortalType.ADVISOR_UNSUBSCRIBE_OUTREACH.token_placeholder
                    )
                }.orEmpty()
        }
    },

    /**
     * https://www.notion.so/capvision/Expert-Emails-One-Click-Decline-44fd9c41d3aa439184c89eb083a07772
     */
    PORTAL_ADVISOR_DECLINE_LINK(
        description = "portal advisor one click decline link",
        category = EmailTemplatePlaceholderCategory.PORTAL_URL,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return data.app_context
                .getBean<PortalProperties>()
                .advisorDeclineUrl
                ?.let {
                    UrlUtil.join(it, PortalType.ADVISOR_DECLINE.token_placeholder)
                }.orEmpty()
        }
    },

    PORTAL_ADVISOR_RECORD_CONSENT_LINK(
        description = "portal advisor record consent link",
        category = EmailTemplatePlaceholderCategory.PORTAL_URL,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return data.app_context
                .getBean<PortalProperties>()
                .advisorRecordingConsentUrl
                ?.let {
                    UrlUtil.join(it, PortalType.TASK_ADVISOR_RECORD_CONSENT.token_placeholder)
                }.orEmpty()
        }
    },

    /**
     * https://www.notion.so/Survey-MVP-DB-Decipher-a86813cccdc34d37ab81f06da36a26b2
     */
    PORTAL_ADVISOR_3RD_PARTY_SURVEY_LINK(
        description = "portal advisor 3rd party survey link",
        category = EmailTemplatePlaceholderCategory.PORTAL_URL,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return data.app_context
                .getBean<PortalProperties>()
                .advisor3rdPartySurveyUrl
                ?.let {
                    UrlUtil.join(
                        it,
                        PortalType.ADVISOR_THIRD_PARTY_SURVEY.token_placeholder
                    )
                }.orEmpty()
        }
    },

    /**
     * https://www.notion.so/Survey-MVP-DB-Decipher-a86813cccdc34d37ab81f06da36a26b2
     */
    PORTAL_ADVISOR_PAYMENT_CONFIRM_LINK(
        description = "portal advisor payment confirm link",
        category = EmailTemplatePlaceholderCategory.PORTAL_URL,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return data.app_context
                .getBean<PortalProperties>()
                .advisorPaymentConfirmUrl
                ?.let {
                    UrlUtil.join(
                        it,
                        PortalType.ADVISOR_PAYMENT_CONFIRM.token_placeholder
                    )
                }.orEmpty()
        }
    },

    PORTAL_CONTACT_PICKER_URL(
        description = "portal contact picker url",
        category = EmailTemplatePlaceholderCategory.PORTAL_URL,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return data.app_context
                .getBean<PortalProperties>()
                .contactPreCallUrl
                ?.let {
                    UrlUtil.join(it, PortalType.CONTACT_PRE_CALL.token_placeholder)
                }.orEmpty()
        }
    },

    PORTAL_CONTACT_POST_CALL_LINK(
        description = "portal contact post call url ",
        category = EmailTemplatePlaceholderCategory.PORTAL_URL,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return data.app_context
                .getBean<PortalProperties>()
                .contactPostCallUrl
                ?.let {
                    UrlUtil.join(it, PortalType.CONTACT_POST_CALL.token_placeholder)
                }.orEmpty()
        }
    },

    RESET_PWD_LINK(
        description = "reset pwd url",
        category = EmailTemplatePlaceholderCategory.PORTAL_URL,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return data.app_context
                .getBean<PortalProperties>()
                .resetPwdUrl
                ?.let {
                    UrlUtil.join(it, PortalType.RESET_PWD.token_placeholder)
                }.orEmpty()
        }
    },

    CONFERENCE_DIAL_LINK(
        description = "portal conference dial url",
        category = EmailTemplatePlaceholderCategory.PORTAL_URL,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return data.app_context
                .getBean<PortalProperties>()
                .conferenceDialUrl
                ?.let {
                    UrlUtil.join(it, PortalType.TASK_CONFERENCE_DIAL_CONFIRMATION.token_placeholder)
                }.orEmpty()
        }
    },

    CLIENT_PORTAL_CONFERENCE_DIAL_LINK(
        description = "client portal conference dial url",
        category = EmailTemplatePlaceholderCategory.PORTAL_URL,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return data.app_context
                .getBean<PortalProperties>()
                .clientPortalConferenceDialUrl
                ?.let {
                    UrlUtil.join(it, PortalType.TASK_CONFERENCE_DIAL_CONFIRMATION.token_placeholder)
                }.orEmpty()
        }
    },


    CLIENT_PORTAL_TWILIO_ASSET_VERIFICATION_LINK(
        description = "client portal twilio asset verification",
        category = EmailTemplatePlaceholderCategory.PORTAL_URL,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return data.app_context
                .getBean<PortalProperties>()
                .clientPortalConferenceAssetUrl
                ?.let {
                    UrlUtil.join(it, PortalType.TASK_CONFERENCE_ASSET.token_placeholder)
                }.orEmpty()
        }
    },

    /**
     * 如果client已经enable client portal, 则入会页面链接到client portal，否则链接到portal
     */
    CLIENT_CONFERENCE_DIAL_LINK(
        description = "client conference dial url",
        category = EmailTemplatePlaceholderCategory.PORTAL_URL,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            val portalProperties = data.app_context
                .getBean<PortalProperties>()
            val url = if (data.client?.enable_client_portal == true) {
                portalProperties.clientPortalConferenceDialUrl
            } else {
                portalProperties.conferenceDialUrl
            }
            return UrlUtil.join(url, PortalType.TASK_CONFERENCE_DIAL_CONFIRMATION.token_placeholder)
        }
    },

    PORTAL_COMPLIANCE_LOGIN_LINK(
        description = "portal compliance login url",
        category = EmailTemplatePlaceholderCategory.PORTAL_URL,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return data.app_context
                .getBean<PortalProperties>()
                .clientComplianceLoginUrl
                ?: ""
        }
    },

    PORTAL_CLIENT_USER_REGISTER_LINK(
        description = "portal client user register url",
        category = EmailTemplatePlaceholderCategory.PORTAL_URL,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return data.app_context
                .getBean<PortalProperties>()
                .clientUserRegisterUrl
                ?.let {
                    val route = UrlUtil.join(
                        it,
                        PortalType.CLIENT_USER_REGISTER.token_placeholder,
                    )
                    val redirect_url = data
                        .context_params[ClientUserRegisterPayload::redirect_uri.name]
                        ?.toString()

                    if (redirect_url.isNullOrBlank()) {
                        route
                    } else {
                        // router?redirect_url=...
                        UrlUtil.addParam(
                            route,
                            key = ClientUserRegisterPayload::redirect_uri.name,
                            value = UrlEncoder.quote(redirect_url)
                        )
                    }
                }.orEmpty()
        }
    },
    //endregion

    //region misc
    CAPVISION_CONSULTATION_APPROVAL_PAGE_URL(
        description = "capvision consultation approval page url",
        category = EmailTemplatePlaceholderCategory.MISCELLANEOUS,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return Webpage.ConsultationApprovalPage.url
        }
    },

    COMPLIANCE_PORTAL_ACCOUNT(
        description = "compliance portal login account",
        category = EmailTemplatePlaceholderCategory.MISCELLANEOUS,
        hide_by_default = true
    ),

    COMPLIANCE_PORTAL_INITIAL_PASSWORD(
        description = "compliance portal login password",
        category = EmailTemplatePlaceholderCategory.MISCELLANEOUS,
        hide_by_default = true
    ),

    USER_INITIAL_PASSWORD(
        description = "user login password",
        category = EmailTemplatePlaceholderCategory.MISCELLANEOUS,
        hide_by_default = true
    ),

    CLIENT_APPROVAL_REJECT_REASON(
        description = "client approval reject reason",
        category = EmailTemplatePlaceholderCategory.MISCELLANEOUS,
        hide_by_default = true
    ),

    REQUEST_MORE_INFO_CONTENT(
        description = "client legal request more info about task or project",
        category = EmailTemplatePlaceholderCategory.MISCELLANEOUS,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return (data.context_params["content"] as? String) ?: ""
        }
    },

    TC_NAME(
        description = "tc name",
        category = EmailTemplatePlaceholderCategory.MISCELLANEOUS,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            val tc_id = data.context_params["tc_id"] as? Int ?: return ""
            return Tc.find(tc_id)?.name.orEmpty()
        }
    },

    TC_DETAIL_PAGE_URL(
        description = "tc link",
        category = EmailTemplatePlaceholderCategory.MISCELLANEOUS,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            val tc_id = data.context_params["tc_id"] as? Int ?: return ""
            return Webpage.TcDetail(id = tc_id).url
        }
    },

    //    CLIENT_CONTACT_LOGIN_CODE(
//            description = "contact login code",
//            category = EmailTemplatePlaceholderCategory.CONTACT
//    )

    // currently we don't use this placeholder
    // because unsubscribe fragment is mandatory.
    // @see com.cpvsn.rm.core.features.task.outreach.TaskOutreachService
//    ADVISOR_UNSUBSCRIBE_OUTREACH_FRAGMENT(
//        description = "advisor unsubscribe outreach fragment",
//        category = EmailTemplatePlaceholderCategory.TASK,
//        hide_by_default = true,
//    ) {
//        override fun resolve(data: PlaceholderBasedModel): String {
//            return EmailTemplateUtil.resolve_fragment(
//                content_type = EmailContentType.ADVISOR_UNSUBSCRIBE_OUTREACH_FRAGMENT,
//                data,
//            )
//        }
//    },

    ADVISOR_DECLINE_FRAGMENT(
        description = "advisor decline fragment",
        category = EmailTemplatePlaceholderCategory.TASK,
        hide_by_default = true,
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return EmailTemplateUtil.resolve_fragment(
                content_type = EmailContentType.ADVISOR_DECLINE_FRAGMENT,
                data,
            )
        }
    },

    ADVISOR_RECOMMEND_CLIENT_PORTAL_FRAGMENT(
        description = "advisor recommend client portal fragment",
        category = EmailTemplatePlaceholderCategory.PROJECT,
        hide_by_default = true,
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            if (data.client?.enable_client_portal != true) return ""
            return EmailTemplateUtil.resolve_fragment(
                content_type = EmailContentType.CONTACT_ADVISOR_RECOMMEND_CLIENT_PORTAL_FRAGMENT,
                data,
            )
        }
    },

    /**
     * https://www.notion.so/Client-Portal-one-click-authorization-and-Account-User-setting-17fcd2d7a2924a0f94732c05e4a31c03
     */
    ADVISOR_RECOMMEND_CLIENT_PORTAL_PAGE_URL(
        description = "project detail page url in client portal",
        category = EmailTemplatePlaceholderCategory.PROJECT,
        hide_by_default = true
    ) {
        /**
         * here we pass all data.context_params as request param to target url
         */
        override fun resolve(data: PlaceholderBasedModel): String {
            return data.app_context
                .getBean<PortalProperties>()
                .clientPortalAdvisorRecommendationUrl
                ?.let {
                    UrlUtil.join(
                        it,
                        PortalType.CONTACT_ADVISOR_RECOMMENDATION.token_placeholder
                    )
                }.orEmpty()
        }
    },

    /**
     * https://www.notion.so/capvision/Scheduling-Client-Invite-subject-when-sending-placeholder-for-Consulting-Firm-clients-02da22f92bad4cb19bab33c9d967df01
     * 将对应text改成条件渲染的，不用新增模板了
     */
    PENDING_COMPLIANCE_APPROVAL_PREFIX_IN_SUBJECT(
        description = "email subject prefix",
        category = EmailTemplatePlaceholderCategory.COMMON,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            val normalPrefix = "Pending Compliance Approval – "
            val client = Client.join(
                data.client ?: return normalPrefix,
                Includes.setOf(Client::preference)
            )
            val is_bcg = client.preference?.preferred_workflow == ProjectWorkflow.BCG
            val is_consulting_firm_and_tc_unsigned = client.type == Client.Type.CONSULTING_FIRM
                    && data.advisor?.type == Advisor.Type.LEAD
            return if (is_consulting_firm_and_tc_unsigned || is_bcg) {
                ""
            } else {
                normalPrefix
            }
        }
    },
    //endregion

    //region +
    // processed in front end
    CA_CONTENT(
        description = "embedded ca content",
        category = EmailTemplatePlaceholderCategory.TASK,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            // currently, this placeholder is resolved in client
            return "{$name}"
        }
    },

    CA_CONTENT_IF_ECOM_AUTO_APPROVE(
        description = "embedded ca content",
        category = EmailTemplatePlaceholderCategory.TASK,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            // currently, this placeholder is resolved in client
            return "{$name}"
        }
    },

    // processed in front end
    SQ_CONTENT(
        description = "embedded sq content",
        category = EmailTemplatePlaceholderCategory.TASK,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            // currently, this placeholder is resolved in client
            return "{$name}"
        }
    },

    CONTRACT_REMINDER_MESSAGE(
        description = "contract reminder message",
        category = EmailTemplatePlaceholderCategory.CONTRACT,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return data.context_params[this.name].toString()
        }
    },

    //endregion

    OUTSOURCE_PROJECT_NAME(
        description = "outsource project name",
        category = EmailTemplatePlaceholderCategory.OUTSOURCE,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return data.context_params[this.name].toString()
        }
    },

    OUTSOURCE_PROJECT_MANAGER_NAME(
        description = "outsource project manager name",
        category = EmailTemplatePlaceholderCategory.OUTSOURCE,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return data.context_params[this.name].toString()
        }
    },

    OUTSOURCE_FROM(
        description = "outsource from",
        category = EmailTemplatePlaceholderCategory.OUTSOURCE,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return data.task?.let {
                Task.join(it, Includes.setOf(Task::task_outsource_info))
                it.task_outsource_info?.outsource_advisor_from?.name
            }.orEmpty()
        }
    },

    OUTSOURCE_TO(
        description = "outsource to",
        category = EmailTemplatePlaceholderCategory.OUTSOURCE,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return data.task?.let {
                Task.join(it, Includes.setOf(Task::task_outsource_info))
                it.task_outsource_info?.outsource_advisor_to?.name
            }.orEmpty()
        }
    },
    OUTSOURCE_APPROVAL_PROJECT_NAME(
        description = "outsource approval project name",
        category = EmailTemplatePlaceholderCategory.OUTSOURCE,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return data.outsource_approval?.project_name.orEmpty()
        }
    },
    OUTSOURCE_APPROVAL_PROJECT_TSID(
        description = "outsource approval project tsid",
        category = EmailTemplatePlaceholderCategory.OUTSOURCE,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return data.outsource_approval?.project_tsid.orEmpty()
        }
    },
    OUTSOURCE_APPROVAL_PROJECT_MANAGER_NAME(
        description = "outsource approval project manager name",
        category = EmailTemplatePlaceholderCategory.OUTSOURCE,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return data.outsource_approval?.outsource_project_manager?.map {
                it.name
            }?.joinToString(",").orEmpty()
        }
    },
    OUTSOURCE_APPROVAL_PROJECT_MANAGER_EMAIL(
        description = "outsource approval project manager email",
        category = EmailTemplatePlaceholderCategory.OUTSOURCE,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return data.outsource_approval?.outsource_project_manager?.map {
                it.email
            }?.joinToString(",").orEmpty()
        }
    },
    OUTSOURCE_APPROVAL_CLIENT_COMPLIANCE_RULES(
        description = "outsource approval project client compliance rules",
        category = EmailTemplatePlaceholderCategory.OUTSOURCE,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return data.outsource_approval?.client_event_rules.orEmpty()
        }
    },
    OUTSOURCE_APPROVAL_REQUEST(
        description = "outsource approval project request",
        category = EmailTemplatePlaceholderCategory.OUTSOURCE,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return data.outsource_approval?.request_content.orEmpty()
        }
    },
    OUTSOURCE_APPROVAL_ORIGINAL_REQUEST(
        description = "outsource approval project original request",
        category = EmailTemplatePlaceholderCategory.OUTSOURCE,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return data.outsource_approval?.original_request_content.orEmpty()
        }
    },
    OUTSOURCE_APPROVAL_SQ(
        description = "outsource approval project screen questions",
        category = EmailTemplatePlaceholderCategory.OUTSOURCE,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return data.outsource_approval?.outsource_screening_questions?.joinToString("").orEmpty()
        }
    },
    OUTSOURCE_EXCEPTION_MESSAGE(
        description = "outsource exception message",
        category = EmailTemplatePlaceholderCategory.OUTSOURCE,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            return data.context_params["exception_message"]?.toString().orEmpty()
        }
    },
    CHAPERONE_NAMES(
        description = "chaperone name",
        category = EmailTemplatePlaceholderCategory.TASK,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            val name_list = data.task?.let { task ->
                Task.join(
                    task,
                    Includes.setOf(Task::task_client_chaperones dot TaskClientChaperone::chaperone)
                ).task_client_chaperones?.mapNotNull {
                    it.chaperone?.name
                }
            }.orEmpty()
            return name_list.joinToString(",")
        }

    },
    CHAPERONE_INFO(
        description = "chaperone info",
        category = EmailTemplatePlaceholderCategory.TASK,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            val chaperones = data.task?.let { task ->
                Task.join(
                    task,
                    Includes.setOf(Task::task_client_chaperones dot TaskClientChaperone::chaperone)
                ).task_client_chaperones?.mapNotNull {
                    it.chaperone
                }
            }.orEmpty()
            if (chaperones.isEmpty()) {
                return "No Chaperone Required"
            }
            return chaperones.joinToString(", ") {
                "${it.name}(${it.email})"
            }
        }

    },
    BCG_PAYMENT_NOTE(
        description = "bcg note",
        category = EmailTemplatePlaceholderCategory.TASK,
        hide_by_default = true
    ) {
        override fun resolve(data: PlaceholderBasedModel): String {
            val client = data.client ?: biz_error("client must be passed")
            Client.join(client, Includes.setOf(Client::preference))
            return if (client.preference?.preferred_workflow == ProjectWorkflow.BCG) {
                AppConfigEntry.firstOrNull(
                    query = AppConfigEntry.Query(
                        key_enum = AppConfigKey.BCG_PAYMENT_NOTE
                    )
                )?.value
            } else {
                null
            }.orEmpty()
        }
    }

    ;
    //endregion

    override fun resolve(data: PlaceholderBasedModel): String {
        return data.context_params[this.name]?.toString() ?: ""
    }

    protected val name_lower_case: String by lazy {
        this.name.toLowerCase()
    }

    protected val self: String = "{{$name}}"
}

package com.cpvsn.rm.clientportal.features.ar

import com.cpvsn.crud.model.Includes
import com.cpvsn.crud.model.dot
import com.cpvsn.crud.pagination.Page
import com.cpvsn.crud.pagination.PageRequest
import com.cpvsn.rm.clientportal.features.ar.response.ProjectResponse
import com.cpvsn.rm.clientportal.features.ar.response.TaskResponse
import com.cpvsn.rm.core.features.advisor.Advisor
import com.cpvsn.rm.core.features.advisor.job.AdvisorJob
import com.cpvsn.rm.core.features.client.Client
import com.cpvsn.rm.core.features.client.ClientService
import com.cpvsn.rm.core.features.misc.location.Location
import com.cpvsn.rm.core.features.misc.schedule.Schedule
import com.cpvsn.rm.core.features.misc.schedule.ScheduleService
import com.cpvsn.rm.core.features.portal.clientcontact.AdvisorRecomendationService
import com.cpvsn.rm.core.features.portal.clientcontact.AdvisorRecomendationService.Companion.applyBasicFilter
import com.cpvsn.rm.core.features.portal.clientcontact.ClientPortalContext
import com.cpvsn.rm.core.features.portal.clientcontact.pojo.ClientPortalAdjustRankRequest
import com.cpvsn.rm.core.features.portal.clientcontact.pojo.ContactScheduleRequest
import com.cpvsn.rm.core.features.portal.clientcontact.pojo.DropDownData
import com.cpvsn.rm.core.features.portal.clientcontact.pojo.TabsAggregation
import com.cpvsn.rm.core.features.project.Project
import com.cpvsn.rm.core.features.slack.SlackService
import com.cpvsn.rm.core.features.task.Task
import com.cpvsn.rm.core.features.task.TaskAdvisorRecommendService
import com.cpvsn.rm.core.features.task.TaskZoomMeetingService
import com.cpvsn.rm.core.features.task.advisor_profile.TaskAdvisorProfile
import com.cpvsn.rm.core.features.task.arrange.TaskArrangement
import com.cpvsn.rm.core.features.task.arrange.TaskTwilioConferenceService
import com.cpvsn.rm.core.features.task.client_selection.ClientSelectionTagRef
import com.cpvsn.rm.core.features.task.event.TaskEventService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping(
    value = ["/recommendation"],
    produces = [(MediaType.APPLICATION_JSON_VALUE)]
)
class ClientPortalApi {

    //region @
    @Autowired
    private lateinit var scheduleService: ScheduleService

    @Autowired
    private lateinit var taskEventService: TaskEventService

    @Autowired
    private lateinit var service: AdvisorRecomendationService

    @Autowired
    private lateinit var slackService: SlackService

    @Autowired
    private lateinit var clientService: ClientService

    @Autowired
    private lateinit var taskTwilioConferenceService: TaskTwilioConferenceService

    @Autowired
    private lateinit var taskZoomMeetingService: TaskZoomMeetingService

    @Autowired
    private lateinit var taskAdvisorRecommendService: TaskAdvisorRecommendService
    //endregion

    data class CreatePortalResponse(
        val token: String
    )
    @GetMapping("/projects/{project_id}/portal")
    fun create_project_portal(
        @PathVariable project_id: Int
    ): CreatePortalResponse {
        val portal = taskAdvisorRecommendService.create_portal(project_id, null, false)
        return CreatePortalResponse(token = portal.token)
    }

    @GetMapping("/portal")
    fun portal(): ClientPortalContext {
        val response = ClientPortalContext.current()
        if (!response.is_internal) {
            slackService.async_send_enter_client_portal_message(response)
        }
        return response
    }

    @GetMapping("/click_button/request_consultation/{task_id}")
    fun button_click(
        @PathVariable task_id: Int,
    ) {
        val response = ClientPortalContext.current()
        if (!response.is_internal) {
            slackService.async_send_click_request_consultation_message(task_id)
        }
    }

    /**
     * 这些下拉框的可选值应该仅限已经Send to Client的tasks涉及的
     */
    @GetMapping("/dropdowns")
    fun get_drop_down_data(
        @RequestParam(required = false) project_id: Int? = null,
    ): DropDownData {
        val projectId = service.retrieve_context_project_id(project_id)
        return service.get_drop_down_data(project_id = projectId)
    }

    @GetMapping("/tabs/aggregation")
    fun tabs_aggregation(
        @RequestParam(required = false) project_id: Int? = null,
    ): TabsAggregation {
        val projectId = service.retrieve_context_project_id(project_id)
        return service.tabs_aggregation(project_id = projectId)
    }


    @PostMapping("/contact_schedule")
    fun contact_schedule(
        @RequestBody request: ContactScheduleRequest,
    ): Task {
        return service.contact_schedule(request)
    }

    @Deprecated("use /search/list")
    @GetMapping("/search")
    fun tasks_search(
        @RequestParam query_string: String?,
        @RequestParam(required = false) project_id: Int? = null,
        query: Task.Query,
        extra: Includes?,
        pageRequest: PageRequest,
    ): Page<Task> {
        val projectId = service.retrieve_context_project_id(project_id)
        val page_request = pageRequest.copy(
            sort = pageRequest.sort?.takeIf { it.isNotEmpty() } ?: AdvisorRecomendationService.defaultSort
        )
        val q = query.copy(project_id = projectId).applyBasicFilter()

        if (query_string.isNullOrBlank()) {
            return Task.list(q, page_request, extra.orEmpty())
        }
        return service.search_with_code_filtering(
            q,
            query_string,
            page_request,
            extra
        )
    }

    @GetMapping("/search/list")
    fun tasks_search(
        query: Task.Query,
        pageRequest: PageRequest,
    ): Page<TaskResponse> {
        val project_id = service.retrieve_context_project_id()
        val q = query.copy(
            project_id = project_id
        ).applyBasicFilter()
        return Task.list(
            q,
            pageRequest,
            Includes.setOf(
                Task::project,
                Task::client dot Client::preference,
                Task::advisor dot Advisor::location dot Location::parent dot Location::parent,
                Task::advisor dot Advisor::jobs dot AdvisorJob::company,
                Task::client_selection_tag_refs dot ClientSelectionTagRef::tag,
                Task::latest_submitted_sq,
                Task::events,
                Task::advisor_profile dot TaskAdvisorProfile::company,
                Task::client_decline,
                Task::specified_client_rate_jit,
                Task::specified_client_rate_currency_jit,
                Task::specified_client_rate_currency_after_multiple,
                Task::default_client_rate_usd,
                Task::default_client_rate_usd_considering_unsigned_tc,
                Task::contact_schedules,
            )
        ).map {
            TaskResponse(it)
        }
    }

    @GetMapping("/search/full")
    fun tasks_search_full(
        query: Task.Query,
        pageRequest: PageRequest,
    ): Page<TaskResponse> {
        val project_id = service.retrieve_context_project_id()
        val q = query.copy(
            project_id = project_id
        ).applyBasicFilter()
        return Task.list(
            q,
            pageRequest,
            Includes.setOf(
                Task::project,
                Task::client dot Client::preference,
                Task::advisor_schedules_after_current,
                Task::non_conflicting_advisor_schedules_after_current,
                Task::schedule dot Schedule::zone_id_string,
                Task::advisor dot Advisor::jobs dot AdvisorJob::company,
                Task::advisor dot Advisor::location dot Location::parent dot Location::parent,
                Task::client_selection_tag_refs dot ClientSelectionTagRef::tag,
                Task::contact_schedules,
                Task::angle,
                Task::events,
                Task::advisor_profile dot TaskAdvisorProfile::company,
                Task::latest_submitted_sq,
                Task::loopup_room,
                Task::loopup_numbers,
                Task::revenues,
                Task::client_decline,
                Task::specified_client_rate_jit,
                Task::specified_client_rate_currency_jit,
                Task::specified_client_rate_currency_after_multiple,
                Task::default_client_rate_usd,
                Task::default_client_rate_usd_considering_unsigned_tc,
                Task::standard_rate_multiplier_display_to_client,
                Task::arrangement dot TaskArrangement::twilio_voice_conference,
                Task::arrangement dot TaskArrangement::zoom_meeting,
            )
        ).map {
            TaskResponse(it)
        }
    }

    @GetMapping("/search/excel_info")
    fun tasks_search_excel_info(
        query: Task.Query,
        pageRequest: PageRequest,
    ): Page<TaskResponse> {
        val project_id = service.retrieve_context_project_id()
        val q = query.copy(
            project_id = project_id
        ).applyBasicFilter()
        return Task.list(
            q,
            pageRequest,
            Includes.setOf(
                Task::project,
                Task::angle,
                Task::advisor dot Advisor::location,
                Task::advisor_profile dot TaskAdvisorProfile::company,
                Task::client dot Client::preference,
                Task::client_selection_tag_refs dot ClientSelectionTagRef::tag,
                Task::latest_submitted_sq,
                Task::revenues,
                Task::events,
                Task::specified_client_rate_jit,
                Task::specified_client_rate_currency_jit,
                Task::specified_client_rate_currency_after_multiple,
                Task::default_client_rate_usd,
                Task::default_client_rate_usd_considering_unsigned_tc,
                Task::standard_rate_multiplier_display_to_client,
            )
        ).map {
            TaskResponse(it)
        }
    }

    @Deprecated("unused")
    @GetMapping("/tasks")
    fun tasks(
        query: Task.Query,
        pageRequest: PageRequest,
        extra: Includes?,
    ): Page<Task> {
        val context = ClientPortalContext.current()
        val page_request = pageRequest.copy(
            sort = pageRequest.sort?.takeIf { it.isNotEmpty() } ?: AdvisorRecomendationService.defaultSort
        )
        val q = query.copy(project_id = context.project_id).applyBasicFilter()

        return Task.list(q, page_request, extra.orEmpty())
    }

    @GetMapping("/project")
    fun project(): ProjectResponse {
        val project_id = service.retrieve_context_project_id()
        val project = Project.get(
            project_id, Includes.setOf(
                Project::client dot Client::preference,
                Project::angles
            )
        )
        val is_bcg = project.client_id?.let {
            clientService.is_bcg_client(it)
        }
        val should_generate_in_zoom_meeting_name = project.client_id?.let {
            clientService.should_generate_in_meeting_name(it)
        }
        return ProjectResponse(project).apply {
            this.is_client_bcg = is_bcg
            this.should_generate_in_zoom_meeting_name = should_generate_in_zoom_meeting_name
        }
    }

    @PatchMapping("/adjust_client_portal_rank")
    fun adjust_client_portal_rank(
        @RequestBody request: ClientPortalAdjustRankRequest,
    ) {
        service.adjust_client_portal_rank(request)
    }

}

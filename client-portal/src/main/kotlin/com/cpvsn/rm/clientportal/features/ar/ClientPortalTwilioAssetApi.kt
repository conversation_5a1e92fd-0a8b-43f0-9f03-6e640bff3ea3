package com.cpvsn.rm.clientportal.features.ar

import com.cpvsn.core.model.BusinessException
import com.cpvsn.core.util.extension.assert_not_null
import com.cpvsn.core.util.extension.assert_valid_id
import com.cpvsn.crud.model.Includes
import com.cpvsn.crud.model.dot
import com.cpvsn.crud.pagination.PageRequest
import com.cpvsn.crud.spring.pagination.PageRequestDefault
import com.cpvsn.rm.core.features.auth.portal.PortalAuth
import com.cpvsn.rm.core.features.auth.portal.getIPortal
import com.cpvsn.rm.core.features.auth.portal.getPortal
import com.cpvsn.rm.core.features.client.Client
import com.cpvsn.rm.core.features.client.contact.ClientContact
import com.cpvsn.rm.core.features.misc.schedule.ScheduleService
import com.cpvsn.rm.core.features.portal.PortalType
import com.cpvsn.rm.core.features.portal.clientcontact.AdvisorRecomendationService
import com.cpvsn.rm.core.features.portal.clientcontact.ClientPortalAssetComplianceEmail
import com.cpvsn.rm.core.features.portal.common.Portal
import com.cpvsn.rm.core.features.portal.common.payload.ClientAssetAccessSigninPayload
import com.cpvsn.rm.core.features.project.Project
import com.cpvsn.rm.core.features.slack.SlackService
import com.cpvsn.rm.core.features.task.Task
import com.cpvsn.rm.core.features.task.arrange.TaskTwilioConferenceService
import com.cpvsn.rm.core.features.task.arrange.pojo.AssetAcquireEmailTokenResponse
import com.cpvsn.rm.core.features.task.arrange.pojo.AssetAcquireVerificationEmailResponse
import com.cpvsn.rm.core.features.task.arrange.pojo.AssetAcquireVerifyTokenResponse
import com.cpvsn.rm.core.features.task.arrange.pojo.ClientAssetListResponse
import com.cpvsn.rm.core.features.task.constant.BridgeType
import com.cpvsn.rm.core.features.task.event.TaskEventService
import com.cpvsn.rm.core.features.template.FileWrapper
import com.cpvsn.rm.core.features.twiliosdk.entity.TwilioAsset
import com.cpvsn.rm.core.features.zoom.meeting.entity.ZoomAsset
import com.cpvsn.web.auth.AuthContext
import org.apache.commons.io.IOUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.core.io.ByteArrayResource
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import java.net.URL

@RestController
@RequestMapping(
        value = ["/recommendation"],
        produces = [(MediaType.APPLICATION_JSON_VALUE)]
)
class ClientPortalTwilioAssetApi {
    //region @
    @Autowired
    private lateinit var scheduleService: ScheduleService
    @Autowired
    private lateinit var taskEventService: TaskEventService
    @Autowired
    private lateinit var service: AdvisorRecomendationService
    @Autowired
    private lateinit var slackService: SlackService
    @Autowired
    private lateinit var taskTwilioConferenceService: TaskTwilioConferenceService
    //endregion

    //region task asset(conf-asset-info page)
    @GetMapping("/tasks/twilio/assets/client_compliance")
    fun get_client_compliance(): Client? {
        val ticket = AuthContext.getIPortal() as? Portal
        val project_id = ticket?.project_id.assert_valid_id()
        val project = Project.find(project_id, Includes.setOf(Project::client dot Client::compliance_preference))
        return project?.client
    }

    @GetMapping("/tasks/twilio/assets/verify_email")
    fun verify_email_before_asset(
            @RequestParam require_compliance_review: Boolean,
            @RequestParam email: String,
            @RequestParam compliance_emails: List<String>,
    ): AssetAcquireVerificationEmailResponse {
        val ticket = AuthContext.getPortal(PortalType.TASK_CONFERENCE_ASSET)
        val task_id = ticket.task_id.assert_valid_id()
        return taskTwilioConferenceService.verify_input_email(
            email,
            task_id,
            ticket,
            require_compliance_review,
            compliance_emails
        )
    }

    @GetMapping("/tasks/twilio/assets/verify_email_pin")
    fun verify_email_pin_before_asset(
            @RequestParam email: String,
            @RequestParam pin_code: String,
    ): AssetAcquireEmailTokenResponse {
        val ticket = AuthContext.getPortal(PortalType.TASK_CONFERENCE_ASSET)
        val task_id = ticket.task_id.assert_valid_id()
        return taskTwilioConferenceService.verify_email_pin_code(email, pin_code, ticket)
    }
    //endregion

    //region client asset list(asset-list page)
    // https://www.notion.so/capvision/Client-Portal-Twilio-Transcript-and-Recording-list-page-a9bbf15c298849109928eaf5488a11e2
    @GetMapping("/client/assets/verify_email")
    fun verify_email_for_client_asset_list(
        @RequestParam email: String,
        @RequestParam compliance_emails: List<String>,
    ): AssetAcquireVerificationEmailResponse {
        val auth = AuthContext.getAuthentication()
        return when (auth) {
            is PortalAuth -> {
                val ticket = AuthContext.getIPortal() as? Portal
                val project_id = ticket?.project_id.assert_valid_id()
                val project = Project.find(project_id, Includes.setOf(Project::client))
                taskTwilioConferenceService.verify_client_input_email(email, project, compliance_emails, project?.client.assert_not_null { "Client cannot be null." })
            }
            else -> {
                // This is a public endpoint.
                // When accessed without an existing token (direct sign-in via https://compliance2.capvision.com/client-portal/#/email-login),
                // this generates a CLIENT_ASSET_ACCESS_SIGNIN token upon successful authentication.
                val contact = ClientContact.findAll(
                    ClientContact.Query(email = email),
                    Includes.setOf(ClientContact::client)
                ).firstOrNull { it.client?.id != null }
                val asset_compliance = ClientPortalAssetComplianceEmail.firstOrNull(
                    ClientPortalAssetComplianceEmail.Query(email = email)
                )
                val client_id = contact?.client?.id ?: asset_compliance?.client_id
                    ?: return AssetAcquireVerificationEmailResponse(allow = false, approved = true)
                val client = Client.get(client_id)
                taskTwilioConferenceService.generate_asset_access_signin_token(email, client, asset_compliance != null)
            }
        }
    }

    @GetMapping("/client/assets/verify_email_pin")
    fun verify_email_pin_for_client_asset_list(
        @RequestParam email: String,
        @RequestParam pin_code: String,
    ): AssetAcquireEmailTokenResponse {
        val ticket = AuthContext.getIPortal() as? Portal
        val client = when (ticket?.type) {
            PortalType.CONTACT_ADVISOR_RECOMMENDATION,
            PortalType.TASK_CONFERENCE_ASSET -> {
                val project = Project.find(ticket.project_id!!, Includes.setOf(Project::client))
                val client = project?.client ?: throw BusinessException("Client cannot be null.")
                client
            }

            PortalType.CLIENT_ASSET_ACCESS_SIGNIN -> {
                // direct sign-in via https://compliance2.capvision.com/client-portal/#/email-login
                val client = (ticket.payload_obj as? ClientAssetAccessSigninPayload)?.let {
                    Client.get(it.client_id)
                } ?: throw BusinessException("Client cannot be null.")
                client
            }

            else -> throw BusinessException("Unsupported PortalType: ${ticket?.type}.")
        }
        return taskTwilioConferenceService.verify_client_email_pin_code(email, pin_code, client)
    }

    @GetMapping("/client/assets/verify_email_auth_token")
    fun client_assets_verify_email_auth_token(
        @RequestParam token: String,
        @RequestParam access_type: TaskTwilioConferenceService.AccessType,
        require_compliance_review: Boolean?,
        @RequestParam compliance_emails: List<String>,
    ): AssetAcquireVerifyTokenResponse {
        val email_auth = taskTwilioConferenceService.verify_email_auth_token(token, access_type, require_compliance_review, compliance_emails)
        return AssetAcquireVerifyTokenResponse(email = email_auth?.email, sign_in_token = email_auth?.sign_in_token)
    }

    @GetMapping("/client/assets/list")
    fun get_client_assets_list(
        @RequestParam email: String,
        @PageRequestDefault(page = 1, size = 20)
        pageRequest: PageRequest,
        query: Task.Query,
        extra: Includes?,
        require_compliance_review: Boolean?,
        @RequestParam(required = false) compliance_emails: List<String>?,
    ): ClientAssetListResponse {
        val portal = AuthContext.getIPortal() as? Portal
        val (client_id, has_compliance_or_legal_role) = when (portal?.type) {
            PortalType.CLIENT_ASSET_ACCESS_SIGNIN -> {
                // direct sign-in via https://compliance2.capvision.com/client-portal/#/email-login
                val payload = portal.payload_obj as? ClientAssetAccessSigninPayload
                val client_id = payload?.client_id ?: throw BusinessException("Client cannot be null.")
                val client_contact = ClientContact.findAll(
                    ClientContact.Query(
                        client_id = client_id,
                        email = email
                    )
                ).firstOrNull()
                val has_compliance_or_legal_role = (client_contact?.types?.any {
                    it in setOf(ClientContact.Type.LEGAL, ClientContact.Type.COMPLIANCE)
                } ?: false || payload.is_compliance)
                Pair(client_id, has_compliance_or_legal_role)
            }

            PortalType.CONTACT_ADVISOR_RECOMMENDATION,
            PortalType.TASK_CONFERENCE_ASSET -> {
                val client_id = Project.find(portal.project_id!!)?.client_id.assert_valid_id { "Client ID not found for project ${portal.project_id}" }
                Pair(client_id, null)
            }

            else -> throw BusinessException("Unsupported PortalType: ${portal?.type}.")
        }
        return taskTwilioConferenceService.get_client_asset_list(
            email,
            pageRequest,
            query,
            extra,
            client_id,
            require_compliance_review,
            compliance_emails,
            has_compliance_or_legal_role
        )
    }
    //endregion

    //region download
    @GetMapping("/tasks/{task_id}/twilio/assets/available_status")
    fun get_task_twilio_assets_available_status(
            @PathVariable task_id: Int,
    ): Map<String, Boolean> {
        val task = Task.find(task_id, Includes.setOf(Task::advisor_record_consent))
        if (task?.advisor_record_consent?.is_consent != true) {
            return mapOf("recording" to false, "transcription" to false, "video" to false)
        }

        var exist_recording = false
        var exist_transcription = false
        var exist_video = false
        when (task.arrange_bridge_type) {
            BridgeType.TWILIO -> {
                val assets = taskTwilioConferenceService.get_task_twilio_assets(task_id)
                    .filter { it.capvision_file_url.isNotBlank() }
                exist_recording =
                    assets.any { it.type == TwilioAsset.Type.VOICE_CONFERENCE_RECORDING && it.capvision_file_url.isNotBlank() }
                exist_transcription =
                    assets.any { it.type == TwilioAsset.Type.VOICE_CONFERENCE_TRANSCRIPTION && it.capvision_file_url.isNotBlank() }
            }

            BridgeType.ZOOM -> {
                val assets = taskTwilioConferenceService.get_task_zoom_assets(task_id)
                exist_recording =
                    assets.any { it.file_type == ZoomAsset.FileType.M4A && it.capvision_file_url.isNotBlank() }
                exist_transcription =
                    assets.any { it.file_type == ZoomAsset.FileType.TRANSCRIPT && it.capvision_file_url.isNotBlank() }
                exist_video =
                    assets.any { it.file_type == ZoomAsset.FileType.MP4 && it.capvision_file_url.isNotBlank() }
            }

            else -> {}
        }
        return mapOf("recording" to exist_recording, "transcription" to exist_transcription, "video" to exist_video)
    }

    @GetMapping("/tasks/{task_id}/twilio/assets/recording/download_redirect")
    fun download_recording_asset_redirect(
            @PathVariable task_id: Int,
    ): Map<String, String> {
        val task = Task.get(task_id)
        val portal = AuthContext.getIPortal() as? Portal
        taskTwilioConferenceService.check_portal_permission(portal, task)
        val file_url = taskTwilioConferenceService.get_recording_file_url(task)
        return mapOf("file_url" to file_url)
    }

    @GetMapping("/tasks/{task_id}/twilio/assets/recording/download")
    fun download_recording_asset(
        @PathVariable task_id: Int,
    ): ResponseEntity<ByteArrayResource> {
        val task = Task.get(task_id)
        val portal = AuthContext.getIPortal() as? Portal
        taskTwilioConferenceService.check_portal_permission(portal, task)
        val file_url = taskTwilioConferenceService.get_recording_file_url(task)
        val byteArray = IOUtils.toByteArray(URL(file_url).openConnection().getInputStream())
        val res = FileWrapper(
            file_name = file_url.split("/").last(),
            media_type = MediaType.valueOf("audio/mpeg").toString(),
            content = byteArray
        )
        return res.to_response_entity()
    }

    @GetMapping("/tasks/{task_id}/twilio/assets/zoom_video/download_redirect")
    fun download_zoom_video_redirect(
        @PathVariable task_id: Int,
    ): Map<String, String> {
        val task = Task.get(task_id)
        val portal = AuthContext.getIPortal() as? Portal
        taskTwilioConferenceService.check_portal_permission(portal, task)
        val file_url = taskTwilioConferenceService.generate_zoom_video_url(task)
        return mapOf("file_url" to file_url)
    }

    @GetMapping("/tasks/{task_id}/twilio/assets/transcription/download_redirect")
    fun download_transcription_asset_redirect(
        @PathVariable task_id: Int,
    ): Map<String, String> {
        val task = Task.get(task_id)
        val portal = AuthContext.getIPortal() as? Portal
        taskTwilioConferenceService.check_portal_permission(portal, task)
        val file_url = taskTwilioConferenceService.get_transcript_asset_file_url(task)
        return mapOf("file_url" to file_url)
    }

    @GetMapping("/tasks/{task_id}/transcript")
    fun download_transcript(
        @PathVariable task_id: Int,
        @RequestParam format: TaskTwilioConferenceService.TranscriptFileType?,
        @RequestParam is_blind_expert_name: Boolean = false
    ): ResponseEntity<ByteArrayResource> {
        val task = Task.get(task_id)
        Task.join(
            task,
            Includes.setOf(Task::project, Task::advisor, Task::angle)
        )

        val portal = AuthContext.getIPortal() as? Portal
        taskTwilioConferenceService.check_portal_permission(portal, task)

        val file_url = taskTwilioConferenceService.get_transcript_asset_file_url(task)
        val content = URL(file_url).readText(Charsets.UTF_8)

        val (file_byte_array, file_name, media_type) = when (format) {
            TaskTwilioConferenceService.TranscriptFileType.WORD -> {
                Triple(
                    taskTwilioConferenceService.generate_word_transcript_as_byte_array(
                        content,
                        task,
                        is_blind_expert_name
                    ),
                    taskTwilioConferenceService.generate_transcript_file_name(
                        task,
                        TaskTwilioConferenceService.TranscriptFileType.WORD,
                        is_blind_expert_name
                    ),
                    MediaType.valueOf("application/vnd.openxmlformats-officedocument.wordprocessingml.document")
                        .toString()
                )
            }

            null,
            TaskTwilioConferenceService.TranscriptFileType.PDF -> {
                Triple(
                    taskTwilioConferenceService.generate_pdf_transcript_as_byte_array(
                        content,
                        task,
                        is_blind_expert_name
                    ),
                    taskTwilioConferenceService.generate_transcript_file_name(
                        task,
                        TaskTwilioConferenceService.TranscriptFileType.PDF,
                        is_blind_expert_name
                    ),
                    MediaType.valueOf("application/pdf").toString()
                )

            }
        }
        val res = FileWrapper(
            file_name = file_name,
            media_type = media_type,
            content = file_byte_array
        )
        return res.to_response_entity()
    }
    //endregion
}
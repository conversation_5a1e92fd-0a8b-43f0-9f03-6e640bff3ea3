package com.cpvsn.rm.clientportal.features.ar.response

import com.cpvsn.core.util.experimental.ExperimentalBeanUtil
import com.cpvsn.rm.core.features.advisor.Advisor
import com.cpvsn.rm.core.features.task.Task

class TaskResponse(task: Task) {

    val id = task.id

    val tsid = task.tsid

    val advisor_id = task.advisor_id

    val project_id = task.project_id

    val client_id = task.client_id

    val angle_id = task.angle_id

    val display_id = task.display_id

    val display_id_locked = task.display_id_locked

    val type = task.type

    val general_status = task.general_status

    val capvision_compliance_status = task.capvision_compliance_status

    val client_compliance_status = task.client_compliance_status

    val client_contact_status = task.client_contact_status

    val client_portal_status = task.client_portal_status

    val scheduling_status = task.scheduling_status

    val start_time = task.start_time

    val end_time = task.end_time

    val arrange_bridge_type = task.arrange_bridge_type

    val arrange_detail_str = task.arrange_detail_str

    val screening_summary = task.screening_summary

    val client_rate_usd = task.client_rate_usd

    val schedule = task.schedule

    val advisor = task.advisor?.let {
        ExperimentalBeanUtil.convert(
            it,
            Advisor::class
        ).apply {
            this.rate = null
            this.rate_currency = null
            if (task.client?.preference?.blind_expert_names_in_to_client_and_portal == true
                || task.project?.blind_expert_profiles == true
            ) {
                this.firstname = ""
                this.lastname = ""
                this.name_prefix = ""
            }
        }
    }

    val client = task.client

    val project = task.project

    val client_selection_tag_refs = task.client_selection_tag_refs

    val contact_schedules = task.contact_schedules

    val advisor_schedules_after_current = task.advisor_schedules_after_current

    val non_conflicting_advisor_schedules_after_current = task.non_conflicting_advisor_schedules_after_current

    val angle = task.angle

    val events = task.events

    val advisor_profile = task.advisor_profile

    val latest_submitted_sq = task.latest_submitted_sq

    val loopup_room = task.loopup_room

    val loopup_numbers = task.loopup_numbers

    val revenues = task.revenues

    val client_decline = task.client_decline

    val specified_client_rate_jit = task.specified_client_rate_jit

    var specified_client_rate_currency_jit = task.specified_client_rate_currency_jit

    val specified_client_rate_currency_after_multiple = task.specified_client_rate_currency_after_multiple

    val default_client_rate_usd = task.default_client_rate_usd

    val default_client_rate_usd_considering_unsigned_tc = task.default_client_rate_usd_considering_unsigned_tc

    val standard_rate_multiplier_display_to_client = task.standard_rate_multiplier_display_to_client

    val arrangement = task.arrangement

}